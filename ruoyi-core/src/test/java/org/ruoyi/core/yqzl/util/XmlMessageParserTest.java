package org.ruoyi.core.yqzl.util;

import org.junit.Test;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponse;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponseRow;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;

import java.math.BigDecimal;

/**
 * XmlMessageParser测试类
 * 测试新的row字段映射功能
 */
public class XmlMessageParserTest {

    @Test
    public void testParseTransferOrderResponseWithList() throws Exception {
        // 模拟包含list的XML响应
        String xmlContent = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<stream>\n" +
                "    <status>SUCCESS</status>\n" +
                "    <statusText>交易成功</statusText>\n" +
                "    <sucTotalNum>2</sucTotalNum>\n" +
                "    <sucTotalAmt>2000.00</sucTotalAmt>\n" +
                "    <errTotalNum>0</errTotalNum>\n" +
                "    <errTotalAmt>0.00</errTotalAmt>\n" +
                "    <list name=\"userDataList\">\n" +
                "        <row>\n" +
                "            <clientID>12345678901234567890</clientID>\n" +
                "            <status>SUCCESS</status>\n" +
                "            <statusText>第一笔转账成功</statusText>\n" +
                "        </row>\n" +
                "        <row>\n" +
                "            <clientID>09876543210987654321</clientID>\n" +
                "            <status>SUCCESS</status>\n" +
                "            <statusText>第二笔转账成功</statusText>\n" +
                "        </row>\n" +
                "    </list>\n" +
                "</stream>";

        // 解析XML
        XmlMessageParser.XmlParseResult<TransferOrderResponse> result = 
            XmlMessageParser.parseXmlResponse(xmlContent, TransferOrderResponse.class);

        // 验证结果
        assert result != null;
        assert result.getSingleData() != null;
        
        TransferOrderResponse response = result.getSingleData();
        
        // 验证基本字段
        assert "SUCCESS".equals(response.getStatus());
        assert "交易成功".equals(response.getStatusText());
        assert response.getSucTotalNum().equals(2);
        assert response.getSucTotalAmt().equals(new BigDecimal("2000.00"));
        assert response.getErrTotalNum().equals(0);
        assert response.getErrTotalAmt().equals(new BigDecimal("0.00"));
        
        // 验证row列表
        assert response.getRow() != null;
        assert response.getRow().size() == 2;
        
        TransferOrderResponseRow row1 = response.getRow().get(0);
        assert "12345678901234567890".equals(row1.getClientID());
        assert "SUCCESS".equals(row1.getStatus());
        assert "第一笔转账成功".equals(row1.getStatusText());
        
        TransferOrderResponseRow row2 = response.getRow().get(1);
        assert "09876543210987654321".equals(row2.getClientID());
        assert "SUCCESS".equals(row2.getStatus());
        assert "第二笔转账成功".equals(row2.getStatusText());
        
        System.out.println("测试通过：成功解析包含list的TransferOrderResponse");
        System.out.println("解析到的row列表大小: " + response.getRow().size());
        for (int i = 0; i < response.getRow().size(); i++) {
            TransferOrderResponseRow row = response.getRow().get(i);
            System.out.println("Row " + (i + 1) + " - ClientID: " + row.getClientID() + 
                             ", Status: " + row.getStatus() + 
                             ", StatusText: " + row.getStatusText());
        }
    }

    @Test
    public void testParseTransferOrderResponseWithSingleRow() throws Exception {
        // 模拟包含单个row的XML响应
        String xmlContent = "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
                "<stream>\n" +
                "    <status>SUCCESS</status>\n" +
                "    <statusText>交易成功</statusText>\n" +
                "    <sucTotalNum>1</sucTotalNum>\n" +
                "    <sucTotalAmt>1000.00</sucTotalAmt>\n" +
                "    <errTotalNum>0</errTotalNum>\n" +
                "    <errTotalAmt>0.00</errTotalAmt>\n" +
                "    <row>\n" +
                "        <clientID>12345678901234567890</clientID>\n" +
                "        <status>SUCCESS</status>\n" +
                "        <statusText>转账成功</statusText>\n" +
                "    </row>\n" +
                "</stream>";

        // 解析XML（这种情况下会放入dataList中）
        XmlMessageParser.XmlParseResult<TransferOrderResponseRow> result = 
            XmlMessageParser.parseXmlResponse(xmlContent, TransferOrderResponseRow.class);

        // 验证结果
        assert result != null;
        assert result.getDataList() != null;
        assert result.getDataList().size() == 1;
        
        TransferOrderResponseRow row = result.getDataList().get(0);
        assert "12345678901234567890".equals(row.getClientID());
        assert "SUCCESS".equals(row.getStatus());
        assert "转账成功".equals(row.getStatusText());
        
        System.out.println("测试通过：成功解析包含单个row的响应");
        System.out.println("Row - ClientID: " + row.getClientID() + 
                         ", Status: " + row.getStatus() + 
                         ", StatusText: " + row.getStatusText());
    }
}
