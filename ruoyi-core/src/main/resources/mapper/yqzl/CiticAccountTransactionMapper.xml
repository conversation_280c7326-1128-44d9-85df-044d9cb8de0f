<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yqzl.mapper.CiticAccountTransactionMapper">

    <resultMap type="CiticAccountTransaction" id="CiticAccountTransactionResult">
        <result property="id"    column="id"    />
        <result property="tranDate"    column="tran_date"    />
        <result property="tranTime"    column="tran_time"    />
        <result property="tranNo"    column="tran_no"    />
        <result property="sumTranNo"    column="sum_tran_no"    />
        <result property="tranAmount"    column="tran_amount"    />
        <result property="creditDebitFlag"    column="credit_debit_flag"    />
        <result property="oppAccountNo"    column="opp_account_no"    />
        <result property="oppAccountName"    column="opp_account_name"    />
        <result property="oppOpenBankName"    column="opp_open_bank_name"    />
        <result property="abstractField"    column="abstract_field"    />
        <result property="cashTransferFlag"    column="cash_transfer_flag"    />
        <result property="opId"    column="op_id"    />
        <result property="opName"    column="op_name"    />
        <result property="ckId"    column="ck_id"    />
        <result property="ckName"    column="ck_name"    />
        <result property="balance"    column="balance"    />
        <result property="valueDate"    column="value_date"    />
        <result property="hostTranCode"    column="host_tran_code"    />
        <result property="e3rtDate"    column="e3rt_date"    />
        <result property="e3rtFlag"    column="e3rt_flag"    />
        <result property="oriDebitAmt"    column="ori_debit_amt"    />
        <result property="oriDebitCry"    column="ori_debit_cry"    />
        <result property="oriCreditAmt"    column="ori_credit_amt"    />
        <result property="oriCreditCry"    column="ori_credit_cry"    />
        <result property="traCryType"    column="tra_cry_type"    />
        <result property="tranRefNo"    column="tran_ref_no"    />
        <result property="clientId"    column="client_id"    />
        <result property="chkNum"    column="chk_num"    />
        <result property="rlTranNo"    column="rl_tran_no"    />
        <result property="rfTranDt"    column="rf_tran_dt"    />
        <result property="rfTranNo"    column="rf_tran_no"    />
        <result property="subAcccNo"    column="sub_accc_no"    />
        <result property="hostTranDesc"    column="host_tran_desc"    />
        <result property="oriNum"    column="ori_num"    />
    </resultMap>

    <sql id="selectCiticAccountTransactionVo">
        select id, tran_date, tran_time, tran_no, sum_tran_no, tran_amount, credit_debit_flag, opp_account_no, opp_account_name, opp_open_bank_name, abstract_field, cash_transfer_flag, op_id, op_name, ck_id, ck_name, balance, value_date, host_tran_code, e3rt_date, e3rt_flag, ori_debit_amt, ori_debit_cry, ori_credit_amt, ori_credit_cry, tra_cry_type, tran_ref_no, client_id, chk_num, rl_tran_no, rf_tran_dt, rf_tran_no, sub_accc_no, host_tran_desc, ori_num from yqzl_citic_account_transaction
    </sql>

    <select id="selectCiticAccountTransactionList" parameterType="CiticAccountTransaction" resultMap="CiticAccountTransactionResult">
        <include refid="selectCiticAccountTransactionVo"/>
        <where>
            <if test="tranDate != null  and tranDate != ''"> and tran_date = #{tranDate}</if>
            <if test="tranTime != null  and tranTime != ''"> and tran_time = #{tranTime}</if>
            <if test="tranNo != null  and tranNo != ''"> and tran_no = #{tranNo}</if>
            <if test="sumTranNo != null  and sumTranNo != ''"> and sum_tran_no = #{sumTranNo}</if>
            <if test="tranAmount != null "> and tran_amount = #{tranAmount}</if>
            <if test="creditDebitFlag != null  and creditDebitFlag != ''"> and credit_debit_flag = #{creditDebitFlag}</if>
            <if test="oppAccountNo != null  and oppAccountNo != ''"> and opp_account_no = #{oppAccountNo}</if>
            <if test="oppAccountName != null  and oppAccountName != ''"> and opp_account_name like concat('%', #{oppAccountName}, '%')</if>
            <if test="oppOpenBankName != null  and oppOpenBankName != ''"> and opp_open_bank_name like concat('%', #{oppOpenBankName}, '%')</if>
            <if test="abstractField != null  and abstractField != ''"> and abstract_field = #{abstractField}</if>
            <if test="cashTransferFlag != null  and cashTransferFlag != ''"> and cash_transfer_flag = #{cashTransferFlag}</if>
            <if test="opId != null  and opId != ''"> and op_id = #{opId}</if>
            <if test="opName != null  and opName != ''"> and op_name like concat('%', #{opName}, '%')</if>
            <if test="ckId != null  and ckId != ''"> and ck_id = #{ckId}</if>
            <if test="ckName != null  and ckName != ''"> and ck_name like concat('%', #{ckName}, '%')</if>
            <if test="balance != null "> and balance = #{balance}</if>
            <if test="valueDate != null  and valueDate != ''"> and value_date = #{valueDate}</if>
            <if test="hostTranCode != null  and hostTranCode != ''"> and host_tran_code = #{hostTranCode}</if>
            <if test="e3rtDate != null  and e3rtDate != ''"> and e3rt_date = #{e3rtDate}</if>
            <if test="e3rtFlag != null  and e3rtFlag != ''"> and e3rt_flag = #{e3rtFlag}</if>
            <if test="oriDebitAmt != null "> and ori_debit_amt = #{oriDebitAmt}</if>
            <if test="oriDebitCry != null  and oriDebitCry != ''"> and ori_debit_cry = #{oriDebitCry}</if>
            <if test="oriCreditAmt != null "> and ori_credit_amt = #{oriCreditAmt}</if>
            <if test="oriCreditCry != null  and oriCreditCry != ''"> and ori_credit_cry = #{oriCreditCry}</if>
            <if test="traCryType != null  and traCryType != ''"> and tra_cry_type = #{traCryType}</if>
            <if test="tranRefNo != null  and tranRefNo != ''"> and tran_ref_no = #{tranRefNo}</if>
            <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
            <if test="chkNum != null  and chkNum != ''"> and chk_num = #{chkNum}</if>
            <if test="rlTranNo != null  and rlTranNo != ''"> and rl_tran_no = #{rlTranNo}</if>
            <if test="rfTranDt != null  and rfTranDt != ''"> and rf_tran_dt = #{rfTranDt}</if>
            <if test="rfTranNo != null  and rfTranNo != ''"> and rf_tran_no = #{rfTranNo}</if>
            <if test="subAcccNo != null  and subAcccNo != ''"> and sub_accc_no = #{subAcccNo}</if>
            <if test="hostTranDesc != null  and hostTranDesc != ''"> and host_tran_desc = #{hostTranDesc}</if>
            <if test="oriNum != null  and oriNum != ''"> and ori_num = #{oriNum}</if>
        </where>
    </select>

    <select id="selectCiticAccountTransactionById" parameterType="Long" resultMap="CiticAccountTransactionResult">
        <include refid="selectCiticAccountTransactionVo"/>
        where id = #{id}
    </select>

    <insert id="insertCiticAccountTransaction" parameterType="CiticAccountTransaction" useGeneratedKeys="true" keyProperty="id">
        insert into yqzl_citic_account_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tranDate != null">tran_date,</if>
            <if test="tranTime != null">tran_time,</if>
            <if test="tranNo != null">tran_no,</if>
            <if test="sumTranNo != null">sum_tran_no,</if>
            <if test="tranAmount != null">tran_amount,</if>
            <if test="creditDebitFlag != null">credit_debit_flag,</if>
            <if test="oppAccountNo != null">opp_account_no,</if>
            <if test="oppAccountName != null">opp_account_name,</if>
            <if test="oppOpenBankName != null">opp_open_bank_name,</if>
            <if test="abstractField != null">abstract_field,</if>
            <if test="cashTransferFlag != null">cash_transfer_flag,</if>
            <if test="opId != null">op_id,</if>
            <if test="opName != null">op_name,</if>
            <if test="ckId != null">ck_id,</if>
            <if test="ckName != null">ck_name,</if>
            <if test="balance != null">balance,</if>
            <if test="valueDate != null">value_date,</if>
            <if test="hostTranCode != null">host_tran_code,</if>
            <if test="e3rtDate != null">e3rt_date,</if>
            <if test="e3rtFlag != null">e3rt_flag,</if>
            <if test="oriDebitAmt != null">ori_debit_amt,</if>
            <if test="oriDebitCry != null">ori_debit_cry,</if>
            <if test="oriCreditAmt != null">ori_credit_amt,</if>
            <if test="oriCreditCry != null">ori_credit_cry,</if>
            <if test="traCryType != null">tra_cry_type,</if>
            <if test="tranRefNo != null">tran_ref_no,</if>
            <if test="clientId != null">client_id,</if>
            <if test="chkNum != null">chk_num,</if>
            <if test="rlTranNo != null">rl_tran_no,</if>
            <if test="rfTranDt != null">rf_tran_dt,</if>
            <if test="rfTranNo != null">rf_tran_no,</if>
            <if test="subAcccNo != null">sub_accc_no,</if>
            <if test="hostTranDesc != null">host_tran_desc,</if>
            <if test="oriNum != null">ori_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tranDate != null">#{tranDate},</if>
            <if test="tranTime != null">#{tranTime},</if>
            <if test="tranNo != null">#{tranNo},</if>
            <if test="sumTranNo != null">#{sumTranNo},</if>
            <if test="tranAmount != null">#{tranAmount},</if>
            <if test="creditDebitFlag != null">#{creditDebitFlag},</if>
            <if test="oppAccountNo != null">#{oppAccountNo},</if>
            <if test="oppAccountName != null">#{oppAccountName},</if>
            <if test="oppOpenBankName != null">#{oppOpenBankName},</if>
            <if test="abstractField != null">#{abstractField},</if>
            <if test="cashTransferFlag != null">#{cashTransferFlag},</if>
            <if test="opId != null">#{opId},</if>
            <if test="opName != null">#{opName},</if>
            <if test="ckId != null">#{ckId},</if>
            <if test="ckName != null">#{ckName},</if>
            <if test="balance != null">#{balance},</if>
            <if test="valueDate != null">#{valueDate},</if>
            <if test="hostTranCode != null">#{hostTranCode},</if>
            <if test="e3rtDate != null">#{e3rtDate},</if>
            <if test="e3rtFlag != null">#{e3rtFlag},</if>
            <if test="oriDebitAmt != null">#{oriDebitAmt},</if>
            <if test="oriDebitCry != null">#{oriDebitCry},</if>
            <if test="oriCreditAmt != null">#{oriCreditAmt},</if>
            <if test="oriCreditCry != null">#{oriCreditCry},</if>
            <if test="traCryType != null">#{traCryType},</if>
            <if test="tranRefNo != null">#{tranRefNo},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="chkNum != null">#{chkNum},</if>
            <if test="rlTranNo != null">#{rlTranNo},</if>
            <if test="rfTranDt != null">#{rfTranDt},</if>
            <if test="rfTranNo != null">#{rfTranNo},</if>
            <if test="subAcccNo != null">#{subAcccNo},</if>
            <if test="hostTranDesc != null">#{hostTranDesc},</if>
            <if test="oriNum != null">#{oriNum},</if>
         </trim>
    </insert>

    <update id="updateCiticAccountTransaction" parameterType="CiticAccountTransaction">
        update yqzl_citic_account_transaction
        <trim prefix="SET" suffixOverrides=",">
            <if test="tranDate != null">tran_date = #{tranDate},</if>
            <if test="tranTime != null">tran_time = #{tranTime},</if>
            <if test="tranNo != null">tran_no = #{tranNo},</if>
            <if test="sumTranNo != null">sum_tran_no = #{sumTranNo},</if>
            <if test="tranAmount != null">tran_amount = #{tranAmount},</if>
            <if test="creditDebitFlag != null">credit_debit_flag = #{creditDebitFlag},</if>
            <if test="oppAccountNo != null">opp_account_no = #{oppAccountNo},</if>
            <if test="oppAccountName != null">opp_account_name = #{oppAccountName},</if>
            <if test="oppOpenBankName != null">opp_open_bank_name = #{oppOpenBankName},</if>
            <if test="abstractField != null">abstract_field = #{abstractField},</if>
            <if test="cashTransferFlag != null">cash_transfer_flag = #{cashTransferFlag},</if>
            <if test="opId != null">op_id = #{opId},</if>
            <if test="opName != null">op_name = #{opName},</if>
            <if test="ckId != null">ck_id = #{ckId},</if>
            <if test="ckName != null">ck_name = #{ckName},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="valueDate != null">value_date = #{valueDate},</if>
            <if test="hostTranCode != null">host_tran_code = #{hostTranCode},</if>
            <if test="e3rtDate != null">e3rt_date = #{e3rtDate},</if>
            <if test="e3rtFlag != null">e3rt_flag = #{e3rtFlag},</if>
            <if test="oriDebitAmt != null">ori_debit_amt = #{oriDebitAmt},</if>
            <if test="oriDebitCry != null">ori_debit_cry = #{oriDebitCry},</if>
            <if test="oriCreditAmt != null">ori_credit_amt = #{oriCreditAmt},</if>
            <if test="oriCreditCry != null">ori_credit_cry = #{oriCreditCry},</if>
            <if test="traCryType != null">tra_cry_type = #{traCryType},</if>
            <if test="tranRefNo != null">tran_ref_no = #{tranRefNo},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="chkNum != null">chk_num = #{chkNum},</if>
            <if test="rlTranNo != null">rl_tran_no = #{rlTranNo},</if>
            <if test="rfTranDt != null">rf_tran_dt = #{rfTranDt},</if>
            <if test="rfTranNo != null">rf_tran_no = #{rfTranNo},</if>
            <if test="subAcccNo != null">sub_accc_no = #{subAcccNo},</if>
            <if test="hostTranDesc != null">host_tran_desc = #{hostTranDesc},</if>
            <if test="oriNum != null">ori_num = #{oriNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCiticAccountTransactionById" parameterType="Long">
        delete from yqzl_citic_account_transaction where id = #{id}
    </delete>

    <delete id="deleteCiticAccountTransactionByIds" parameterType="String">
        delete from yqzl_citic_account_transaction where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertCiticAccountTransaction" parameterType="java.util.List">
        INSERT INTO yqzl_citic_account_transaction
        (
            tran_date, tran_time, tran_no, sum_tran_no, tran_amount,
            credit_debit_flag, opp_account_no, opp_account_name, opp_open_bank_name, abstract_field,
            cash_transfer_flag, op_id, op_name, ck_id, ck_name,
            balance, value_date, host_tran_code, e3rt_date, e3rt_flag,
            ori_debit_amt, ori_debit_cry, ori_credit_amt, ori_credit_cry,
            tra_cry_type, tran_ref_no, client_id, chk_num, rl_tran_no,
            rf_tran_dt, rf_tran_no, sub_accc_no, host_tran_desc, ori_num,
            account_no,open_bank_name
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tranDate},
            #{item.tranTime},
            #{item.tranNo},
            #{item.sumTranNo},
            #{item.tranAmount},
            #{item.creditDebitFlag},
            #{item.oppAccountNo},
            #{item.oppAccountName},
            #{item.oppOpenBankName},
            #{item.abstractField},
            #{item.cashTransferFlag},
            #{item.opId},
            #{item.opName},
            #{item.ckId},
            #{item.ckName},
            #{item.balance},
            #{item.valueDate},
            #{item.hostTranCode},
            #{item.e3rtDate},
            #{item.e3rtFlag},
            #{item.oriDebitAmt},
            #{item.oriDebitCry},
            #{item.oriCreditAmt},
            #{item.oriCreditCry},
            #{item.traCryType},
            #{item.tranRefNo},
            #{item.clientId},
            #{item.chkNum},
            #{item.rlTranNo},
            #{item.rfTranDt},
            #{item.rfTranNo},
            #{item.subAcccNo},
            #{item.hostTranDesc},
            #{item.oriNum},
            #{item.accountNo},
            #{item.openBankName}
            )
        </foreach>
    </insert>
</mapper>
