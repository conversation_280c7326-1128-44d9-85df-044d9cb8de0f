<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaDictDynamicMapper">
    
    <resultMap type="OaDictDynamic" id="OaDictDynamicResult">
        <result property="id"    column="id"    />
        <result property="dictId"    column="dict_id"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="operationContent"    column="operation_content"    />
        <result property="operationById"    column="operation_by_id"    />
        <result property="operationBy"    column="operation_by"    />
    </resultMap>

    <sql id="selectOaDictDynamicVo">
        select id, dict_id, operation_time, operation_content, operation_by_id, operation_by from oa_dict_dynamic
    </sql>

    <select id="selectOaDictDynamicList" parameterType="OaDictDynamic" resultMap="OaDictDynamicResult">
        <include refid="selectOaDictDynamicVo"/>
        <where>  
            <if test="dictId != null "> and dict_id = #{dictId}</if>
            <if test="operationTime != null "> and operation_time = #{operationTime}</if>
            <if test="operationContent != null  and operationContent != ''"> and operation_content = #{operationContent}</if>
            <if test="operationById != null "> and operation_by_id = #{operationById}</if>
            <if test="operationBy != null  and operationBy != ''"> and operation_by = #{operationBy}</if>
        </where>
    </select>
    
    <select id="selectOaDictDynamicById" parameterType="Long" resultMap="OaDictDynamicResult">
        <include refid="selectOaDictDynamicVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertOaDictDynamic" parameterType="OaDictDynamic" useGeneratedKeys="true" keyProperty="id">
        insert into oa_dict_dynamic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictId != null">dict_id,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="operationContent != null">operation_content,</if>
            <if test="operationById != null">operation_by_id,</if>
            <if test="operationBy != null">operation_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictId != null">#{dictId},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="operationContent != null">#{operationContent},</if>
            <if test="operationById != null">#{operationById},</if>
            <if test="operationBy != null">#{operationBy},</if>
         </trim>
    </insert>

    <update id="updateOaDictDynamic" parameterType="OaDictDynamic">
        update oa_dict_dynamic
        <trim prefix="SET" suffixOverrides=",">
            <if test="dictId != null">dict_id = #{dictId},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="operationContent != null">operation_content = #{operationContent},</if>
            <if test="operationById != null">operation_by_id = #{operationById},</if>
            <if test="operationBy != null">operation_by = #{operationBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOaDictDynamicById" parameterType="Long">
        delete from oa_dict_dynamic where id = #{id}
    </delete>

    <delete id="deleteOaDictDynamicByIds" parameterType="String">
        delete from oa_dict_dynamic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>