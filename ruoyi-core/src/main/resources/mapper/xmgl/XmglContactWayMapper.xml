<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.xmglproject.mapper.XmglContactWayMapper">
    
    <resultMap type="org.ruoyi.core.xmglproject.domain.XmglContactWay" id="XmglContactWayResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="userName"    />
        <result property="fundName"    column="fund_name"    />
        <result property="fundTel"    column="fund_tel"    />
        <result property="fundWx"    column="fund_wx"    />
        <result property="fundEmail"    column="fund_email"    />
        <result property="fundDept"    column="fund_dept"    />
        <result property="productName"    column="product_name"    />
        <result property="productTel"    column="product_tel"    />
        <result property="productWx"    column="product_wx"    />
        <result property="productEmail"    column="product_email"    />
        <result property="productDept"    column="product_dept"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBr"    column="create_br"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBr"    column="update_br"    />
    </resultMap>

    <sql id="selectXmglContactWayVo">
        select xcw.id, xcw.project_id, xcw.user_id, xcw.fund_name, xcw.fund_tel, xcw.fund_wx, xcw.fund_email, xcw.fund_dept, xcw.product_name, xcw.product_tel,
               xcw.product_wx, xcw.product_email,xcw.product_dept, xcw.status, xcw.create_time, xcw.create_br, xcw.update_time, xcw.update_br,su.nick_name AS userName
        from xmgl_contact_way xcw
            LEFT JOIN sys_user su ON su.user_id = xcw.user_id
    </sql>

    <select id="selectXmglContactWayList" parameterType="XmglContactWay" resultMap="XmglContactWayResult">
        <include refid="selectXmglContactWayVo"/>
        <where>  
            <if test="projectId != null "> and xcw.project_id = #{projectId}</if>
            <if test="userId != null "> and xcw.user_id = #{userId}</if>
            <if test="fundName != null  and fundName != ''"> and xcw.fund_name like concat('%', #{fundName}, '%')</if>
            <if test="fundTel != null  and fundTel != ''"> and xcw.fund_tel = #{fundTel}</if>
            <if test="fundWx != null  and fundWx != ''"> and xcw.fund_wx = #{fundWx}</if>
            <if test="fundEmail != null  and fundEmail != ''"> and xcw.fund_email = #{fundEmail}</if>
            <if test="fundDept != null  and fundDept != ''"> and xcw.fund_dept = #{fundDept}</if>
            <if test="productName != null  and productName != ''"> and xcw.product_name like concat('%', #{productName}, '%')</if>
            <if test="productTel != null  and productTel != ''"> and xcw.product_tel = #{productTel}</if>
            <if test="productWx != null  and productWx != ''"> and xcw.product_wx = #{productWx}</if>
            <if test="productEmail != null  and productEmail != ''"> and xcw.product_email = #{productEmail}</if>
            <if test="productDept != null  and productDept != ''"> and xcw.product_dept = #{productDept}</if>
            <if test="status != null  and status != ''"> and xcw.status = #{status}</if>
            <if test="createBr != null  and createBr != ''"> and xcw.create_br = #{createBr}</if>
            <if test="updateBr != null  and updateBr != ''"> and xcw.update_br = #{updateBr}</if>
        </where>
    </select>
    
    <select id="selectXmglContactWayById" parameterType="Long" resultMap="XmglContactWayResult">
        <include refid="selectXmglContactWayVo"/>
        where xcw.id = #{id}
    </select>


    <select id="selectByProIdAndUserId" resultMap="XmglContactWayResult">
        <include refid="selectXmglContactWayVo"/>
        where xcw.project_id = #{id} and xcw.user_id = #{userId}
    </select>



    <select id="selectuserList" resultMap="XmglContactWayResult">
        <include refid="selectXmglContactWayVo"/>
        <where>
            <if test="id != null "> and xcw.project_id = #{id}</if>
            <if test="userId != null "> and xcw.user_id = #{userId}</if>
        </where>
        ORDER BY create_time DESC
        <!--        where project_id = #{id} and user_id = #{userId}-->
    </select>

    <select id="queryByUserIdAndPid" resultMap="XmglContactWayResult">
       select  * from xmgl_contact_way
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
        ORDER BY create_time DESC
        <!--        where project_id = #{id} and user_id = #{userId}-->
    </select>


    <insert id="insertXmglContactWay" parameterType="XmglContactWay" useGeneratedKeys="true" keyProperty="id">
        insert into xmgl_contact_way
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="fundName != null">fund_name,</if>
            <if test="fundTel != null">fund_tel,</if>
            <if test="fundWx != null">fund_wx,</if>
            <if test="fundEmail != null">fund_email,</if>
            <if test="fundDept != null">fund_dept,</if>
            <if test="productName != null">product_name,</if>
            <if test="productTel != null">product_tel,</if>
            <if test="productWx != null">product_wx,</if>
            <if test="productEmail != null">product_email,</if>
            <if test="productDept != null">product_dept,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBr != null">create_br,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBr != null">update_br,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="fundName != null">#{fundName},</if>
            <if test="fundTel != null">#{fundTel},</if>
            <if test="fundWx != null">#{fundWx},</if>
            <if test="fundEmail != null">#{fundEmail},</if>
            <if test="fundDept != null">#{fundDept},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productTel != null">#{productTel},</if>
            <if test="productWx != null">#{productWx},</if>
            <if test="productEmail != null">#{productEmail},</if>
            <if test="productDept != null">#{productDept},</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBr != null">#{createBr},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBr != null">#{updateBr},</if>
         </trim>
    </insert>

    <update id="updateXmglContactWay" parameterType="XmglContactWay">
        update xmgl_contact_way
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="fundName != null">fund_name = #{fundName},</if>
            <if test="fundTel != null">fund_tel = #{fundTel},</if>
            <if test="fundWx != null">fund_wx = #{fundWx},</if>
            <if test="fundEmail != null">fund_email = #{fundEmail},</if>
            <if test="fundDept != null">fund_dept = #{fundDept},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productTel != null">product_tel = #{productTel},</if>
            <if test="productWx != null">product_wx = #{productWx},</if>
            <if test="productEmail != null">product_email = #{productEmail},</if>
            <if test="productDept != null">product_dept = #{productDept},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBr != null">create_br = #{createBr},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBr != null">update_br = #{updateBr},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteXmglContactWayById" parameterType="Long">
        delete from xmgl_contact_way where id = #{id}
    </delete>

    <delete id="deleteXmglContactWayByIds" parameterType="String">
        delete from xmgl_contact_way where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateXmglContactWayByProjectId" parameterType="long">
        update xmgl_contact_way set status = '1' where project_id = #{id} and status = '0'
    </update>

    <select id="selectProjectChannelListByIds" parameterType="list" resultMap="XmglContactWayResult">
        select * from xmgl_project_channel where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectContactWayListByProjectId" resultMap="XmglContactWayResult">
        select distinct xcw.*, su.nick_name as 'userName'
        from xmgl_contact_way xcw
        left join sys_user su on xcw.user_id = su.user_id
        <where>
            <if test="type != '' and type.equals('all')">
                and xcw.project_id = #{projectId}
            </if>
            <if test="type != '' and type.equals('channel')">
                and xcw.project_id = #{projectId} and xcw.status = '0'
            </if>
        </where>
    </select>

    <select id="selectContactWayByLoginUserId" resultType="org.ruoyi.core.xmglproject.domain.XmglContactWay">
        select distinct xcw.*, su.nick_name as 'userName'
        from xmgl_project_user xpu
                left join xmgl_contact_way xcw on xpu.contact_id = xcw.id
                left join sys_user su on xcw.user_id = su.user_id
        where xpu.id = #{projectId} and xcw.user_id = {userId}
        group by xpu.contact_id
    </select>

    <update id="updateContactWayStatusByProjectId" parameterType="long">
        update xmgl_contact_way set status = '1' where project_id = #{projectId} and status = '0'
    </update>

    <select id="selectNewXmglContactWayByProjectId" parameterType="long"
            resultType="org.ruoyi.core.xmglproject.domain.XmglContactWay">
        select * from xmgl_contact_way where project_id = #{projectId} and status = '0'
    </select>

</mapper>