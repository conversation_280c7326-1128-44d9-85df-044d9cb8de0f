<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.notice.mapper.TgNoticeMainMapper">
    
    <resultMap type="TgNoticeMain" id="TgNoticeMainResult">
        <result property="id"    column="id"    />
        <result property="noticeName"    column="notice_name"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="noticeDataName"    column="data_name"    />
        <result property="publisher"    column="publisher"    />
        <result property="publishCompany"    column="publish_company"    />
        <result property="companyShortName"    column="company_short_name"    />
        <result property="content"    column="content"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="isHeader"    column="is_header"    />
        <result property="isEmphasis"    column="is_emphasis"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createrNickName"    column="nick_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="version"    column="version"    />
    </resultMap>

    <sql id="selectTgNoticeMainVo">
        select id, notice_name, notice_type, publisher, publish_company, content, publish_status, is_header, is_emphasis, del_flag, create_by, create_time, update_by, update_time from tg_notice_main
    </sql>

    <select id="selectTgNoticeMainList" parameterType="TgNoticeMain" resultMap="TgNoticeMainResult">
        select tnm.id, tnm.notice_name, tnm.notice_type, oda.data_name, tnm.publish_company, tnm.content, tnm.publish_status, tnm.is_header, tnm.is_emphasis,
               tnm.create_by, tnm.create_time, su1.nick_name, sc.company_short_name, tvr.version
        from tg_notice_main tnm
        left join tg_version_relation tvr on tnm.id = tvr.notice_id
        left join sys_user su1 on su1.user_name =  tnm.create_by
        left join sys_company sc on sc.id = tnm.publish_company
        left join oa_data_manage oda on tnm.notice_type = oda.id
        <where>
            <if test="companyIdList != null  and companyIdList.size() > 0">
                and tnm.publish_company in
                <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="noticeName != null  and noticeName != ''"> and tnm.notice_name like concat('%', #{noticeName}, '%')</if>
            <if test="noticeType != null "> and tnm.notice_type = #{noticeType}</if>
            <if test="sxCreateTime != null "> and date_format(tnm.create_time,'%y%m%d') = date_format(#{sxCreateTime},'%y%m%d')</if>
            <if test="publishStatus != null  and publishStatus != ''"> and tnm.publish_status = #{publishStatus}</if>
             and tnm.del_flag = '0'
        </where>
        /*keep orderby*/
        order by tnm.is_header desc, tnm.is_emphasis desc, tnm.create_time desc
    </select>
    
    <select id="selectTgNoticeMainById" parameterType="Long" resultMap="TgNoticeMainResult">
        select tnm.id, tnm.notice_name, tnm.notice_type, tnm.publish_company, tnm.content, tnm.publish_status, tnm.is_header, tnm.is_emphasis,
               tnm.create_by, tnm.create_time, su1.nick_name, sc.company_short_name, tnm.publisher, su2.nick_name as 'publisherNickName', tvr.version
        from tg_notice_main tnm
                 left join tg_version_relation tvr on tnm.id = tvr.notice_id
                 left join sys_user su1 on su1.user_name =  tnm.create_by
                 left join sys_user su2 on su2.user_id =  tnm.publisher
                 left join sys_company sc on sc.id = tnm.publish_company
        where tnm.id = #{id} and tnm.del_flag = '0'
    </select>

    <insert id="insertTgNoticeMain" parameterType="TgNoticeMain" useGeneratedKeys="true" keyProperty="id">
        insert into tg_notice_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeName != null and noticeName != ''">notice_name,</if>
            <if test="noticeType != null">notice_type,</if>
            <if test="publisher != null">publisher,</if>
            <if test="publishCompany != null">publish_company,</if>
            <if test="content != null">content,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="isEmphasis != null">is_emphasis,</if>
            <if test="isHeader != null">is_header,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeName != null and noticeName != ''">#{noticeName},</if>
            <if test="noticeType != null">#{noticeType},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="publishCompany != null">#{publishCompany},</if>
            <if test="content != null">#{content},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="isEmphasis != null">isEmphasis,</if>
            <if test="isHeader != null">isHeader,</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTgNoticeMain" parameterType="TgNoticeMain">
        update tg_notice_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeName != null and noticeName != ''">notice_name = #{noticeName},</if>
            <if test="noticeType != null">notice_type = #{noticeType},</if>
            <if test="publisher != null">publisher = #{publisher},</if>
            <if test="publishCompany != null">publish_company = #{publishCompany},</if>
            <if test="content != null">content = #{content},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="isEmphasis != null">is_emphasis = #{isEmphasis},</if>
            <if test="isHeader != null">is_header = #{isHeader},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTgNoticeMainById" parameterType="org.ruoyi.core.notice.domain.TgNoticeMain">
        update tg_notice_main set update_by = #{updateBy}, update_time = #{updateTime}, del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteTgNoticeMainByIds" parameterType="String">
        delete from tg_notice_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserHomePostCompany" resultType="com.ruoyi.system.domain.SysCompany">
        select sc.id, sc.company_name, sc.company_short_name from sys_user su
        left join sys_user_post sup on su.user_id = sup.user_id and home_post = '0'
        left join sys_post sp on sup.post_id = sp.post_id
        left join sys_company sc on sp.unit_id = sc.id
        where su.user_id = #{userId}
    </select>

    <select id="selectIndexTgNoticeMainList" resultType="org.ruoyi.core.notice.domain.vo.TgIndexNoticeVo">
        SELECT
            tnm.id,
            tnm.notice_name AS 'noticeName',
            tnm.notice_type AS 'noticeType',
            oda.data_name AS 'noticeTypeLabel',
            tnm.create_by AS 'createBy',
            su.nick_name AS 'createNickName',
            trr.read_type AS 'readType',
            tnm.is_header AS 'isHeader',
            tnm.is_emphasis AS 'isEmphasis',
            tnm.create_time,
            sc.company_short_name AS 'companyShortName',
            CASE
                WHEN trr.read_type = '1' THEN
                    '已读' ELSE '未读'
                END AS 'readTypeLabel'
        FROM
            tg_notice_main tnm
                LEFT JOIN tg_read_relation trr ON tnm.id = trr.notice_id
                LEFT JOIN tg_version_relation tvr ON tnm.id = tvr.notice_id
                LEFT JOIN oa_data_manage oda on tnm.notice_type = oda.id
                LEFT JOIN sys_user su ON tnm.create_by = su.user_name
                LEFT JOIN sys_company sc on tnm.publish_company = sc.id
        <where>
            <if test="noticeName != null  and noticeName != ''"> and tnm.notice_name like concat('%', #{noticeName}, '%')</if>
            <if test="noticeType != null "> and tnm.notice_type = #{noticeType}</if>
            <if test="sxCreateTime != null "> and date_format(tnm.create_time,'%y%m%d') = date_format(#{sxCreateTime},'%y%m%d')</if>
            <if test="publishStatus != null  and publishStatus != ''"> and tnm.publish_status = #{publishStatus}</if>
            <if test="companyIdList != null and companyIdList.size > 0">
               and tnm.publish_company IN
               <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
                   #{item}
               </foreach>
            </if>
            <if test="loginUserId != null">
                AND trr.user_id = #{loginUserId}
            </if>
            and tnm.publish_status = '1'
        </where>
        GROUP BY tnm.id
        ORDER BY
            tnm.is_header DESC,
            tnm.is_emphasis DESC,
            tnm.create_time DESC
    </select>

    <select id="selectCompanyListByCompanyIds" resultType="com.ruoyi.system.domain.SysCompany">
        select id, company_name, company_short_name from sys_company where id in
        <foreach collection="companyIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>