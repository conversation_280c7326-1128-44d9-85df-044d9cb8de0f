<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.license.mapper.ZzLicenseCatalogueMapper">

    <resultMap type="ZzLicenseCatalogue" id="ZzLicenseCatalogueResult">
        <result property="id"    column="id"    />
        <result property="catalogueName"    column="catalogue_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="catalogueSystemCode"    column="catalogue_system_code"    />
        <result property="catalogueCode"    column="catalogue_code"    />
        <result property="unitId"    column="unit_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectZzLicenseCatalogueVo">
        select id, catalogue_name, parent_id, catalogue_system_code, catalogue_code, unit_id, dept_id, order_num, remark, create_by, create_time, update_by, update_time from zz_license_catalogue
    </sql>

    <select id="selectZzLicenseCatalogueList" parameterType="ZzLicenseCatalogue" resultMap="ZzLicenseCatalogueResult">
        select ss.* from (
        select ic.id, ic.catalogue_name, ic.parent_id, ic.catalogue_system_code, ic.catalogue_code, ic.unit_id, ic.dept_id, ic.order_num,
        ic.remark, ic.create_time, ic.update_time,
        su.nick_name as 'update_by',
        su2.nick_name as 'create_by',
        dept.dept_name as 'deptName',
        org.company_short_name as 'unitName',
        b.catalogue_name as 'parentCatalogueName'
        from zz_license_catalogue ic
        LEFT join sys_dept dept on ic.dept_id = dept.dept_id
        LEFT join sys_company org on ic.unit_id = org.id and org.is_inside = '1'
        LEFT JOIN zz_license_catalogue b ON ic.parent_id = b.id
        LEFT JOIN sys_user su on ic.update_by = su.user_name
        LEFT JOIN sys_user su2 on ic.create_by = su2.user_name
        <where>
            <if test="unitIdSet != null and unitIdSet.size() > 0">
                AND ic.unit_id in
                <foreach collection="unitIdSet" item="unitId" separator="," open="(" close=")">
                    #{unitId}
                </foreach>
            </if>
        <!--left join
        (SELECT zu.bill_id,zu.authority_id,zu.authority_type,zu.del_flag FROM zz_authority zu
        ) au ON ic.id = au.bill_id
        <where>
            <if test="unitIdSet != null and unitIdSet.size() > 0">
                AND ic.unit_id in
                <foreach collection="unitIdSet" item="unitId" separator="," open="(" close=")">
                    #{unitId}
                </foreach>
            </if>
            or ( ic.create_by = #{createBy} )
            <if test="auDeptIds != null and auDeptIds.size() > 0">
                OR (au.authority_id IN
                <foreach collection="auDeptIds" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
                AND au.authority_type = '0')
            </if>
            OR (au.authority_id = #{auUserId} AND au.authority_type = '2')
            <if test="auPostIds != null and auPostIds.size() > 0">
                OR (au.authority_id IN
                <foreach collection="auPostIds" item="auPostId" separator="," open="(" close=")">
                    #{auPostId}
                </foreach>
                AND au.authority_type = '1')
            </if>-->
        </where>
        group by ic.id
        order by ic.order_num,create_time desc
        )ss
        <where>
        <if test="catalogueName != null  and catalogueName != ''">and ss.catalogue_name like concat('%',#{catalogueName},'%')</if>
        <if test="catalogueCode != null  and catalogueCode != ''">and ss.catalogue_code like concat('%',#{catalogueCode},'%')</if>
        <if test="unitId != null  and unitId != ''">and ss.unit_id = #{unitId}</if>
        <if test="deptId != null  and deptId != ''">and ss.dept_id= #{deptId}</if>
        <if test="id != null ">and ss.parent_id = #{id}</if>
        </where>
    </select>

    <select id="selectZzLicenseCatalogueById" parameterType="Long" resultType="ZzLicenseCatalogueVo">
        SELECT
            ca.id, ca.catalogue_name, ca.parent_id, ca.catalogue_system_code, ca.catalogue_code, ca.unit_id,
            ca.dept_id, ca.order_num, ca.remark, ca.create_by, ca.create_time, ca.update_by, ca.update_time,
            parent.catalogue_name as 'parentCatalogueName',
                GROUP_CONCAT(DISTINCT sys_dept.dept_id ORDER BY sys_dept.dept_id ASC SEPARATOR ', ') AS 'auDeptIds',
                GROUP_CONCAT(DISTINCT sys_dept.dept_name ORDER BY sys_dept.dept_id ASC SEPARATOR ', ') AS 'auDeptNames',
                GROUP_CONCAT(DISTINCT sys_post.post_id ORDER BY sys_post.post_id ASC SEPARATOR ', ') AS 'auPostIds',
                GROUP_CONCAT(DISTINCT sys_post.post_name ORDER BY sys_post.post_id ASC SEPARATOR ', ') AS 'auPostNames',
                GROUP_CONCAT(DISTINCT sys_user.user_id ORDER BY sys_user.user_id ASC SEPARATOR ', ') AS 'auUserIds',
                GROUP_CONCAT(DISTINCT sys_user.nick_name ORDER BY sys_user.user_id ASC SEPARATOR ', ') AS 'auUserNames',
                dept.dept_name as 'deptName',
                org.company_short_name as 'unitName',
                org.company_name as 'unitAllName'
        FROM
            zz_license_catalogue ca
                LEFT JOIN zz_authority au ON ca.id = au.bill_id
                LEFT JOIN zz_license_catalogue parent on ca.parent_id = parent.id
                LEFT JOIN sys_dept ON au.authority_type = 0 AND au.authority_id = sys_dept.dept_id
                LEFT JOIN sys_post ON au.authority_type = 1 AND au.authority_id = sys_post.post_id
                LEFT JOIN sys_user ON au.authority_type = 2 AND au.authority_id = sys_user.user_id
                LEFT JOIN sys_dept dept on ca.dept_id = dept.dept_id
                LEFT JOIN sys_company org on ca.unit_id = org.id and org.is_inside = '1'
        where  ca.id = #{id}
        GROUP BY
            ca.id;
    </select>

    <insert id="insertZzLicenseCatalogue" parameterType="ZzLicenseCatalogue" useGeneratedKeys="true" keyProperty="id">
        insert into zz_license_catalogue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="catalogueName != null">catalogue_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="catalogueSystemCode != null">catalogue_system_code,</if>
            <if test="catalogueCode != null">catalogue_code,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="remark != ''">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="catalogueName != null">#{catalogueName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="catalogueSystemCode != null">#{catalogueSystemCode},</if>
            <if test="catalogueCode != null">#{catalogueCode},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="remark != ''">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateZzLicenseCatalogue" parameterType="ZzLicenseCatalogue">
        update zz_license_catalogue
        <trim prefix="SET" suffixOverrides=",">
            <if test="catalogueName != null">catalogue_name = #{catalogueName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="catalogueSystemCode != null">catalogue_system_code = #{catalogueSystemCode},</if>
            <if test="catalogueCode != null">catalogue_code = #{catalogueCode},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZzLicenseCatalogueById" parameterType="Long">
        delete from zz_license_catalogue where id = #{id}
    </delete>

    <delete id="deleteZzLicenseCatalogueByIds" parameterType="String">
        delete from zz_license_catalogue where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZzCatalogueList" resultType="org.ruoyi.core.license.domain.ZzLicenseCatalogue">
        SELECT
            a.id,
            a.catalogue_name,
            a.parent_id,
            a.catalogue_system_code,
            a.catalogue_code,
            a.unit_id,
            a.dept_id,
            a.order_num,
            a.remark,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            b.catalogue_name AS 'parentCatalogueName',
            c.dept_name AS 'deptName',
            d.company_short_name AS 'unitName'
        FROM
            zz_license_catalogue a
                LEFT JOIN zz_license_catalogue b ON a.parent_id = b.id
                LEFT JOIN sys_dept c ON a.dept_id = c.dept_id
                LEFT JOIN sys_company d ON d.id = a.unit_id and d.is_inside = '1'
        WHERE a.unit_id = #{unitId}
    </select>

    <select id="getCountByCreateTime" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM zz_license_catalogue
        <where>
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>
    <select id="selectChildrenCatalogueListById" resultType="org.ruoyi.core.license.domain.ZzLicenseCatalogue">
        <include refid="selectZzLicenseCatalogueVo"/>
            where parent_id = #{id}
    </select>

    <select id="queryCataloguesById" parameterType="ZzLicenseMoveVo" resultType="org.ruoyi.core.license.domain.ZzLicenseMain">
        select * from zz_license_main where catalogue_id = #{targetCataId} and license_system_code = #{licenseSystemCode}
    </select>

    <select id="getCountByUpdateTime" resultType="java.lang.Integer">
        SELECT count(id)
        FROM zz_license_catalogue
        WHERE catalogue_name is not null
    </select>

    <select id="selectUserAuthDeptListByCompanyId" resultType="com.ruoyi.common.core.domain.entity.SysDept">
        select * from sys_dept where unit_id in
        <foreach collection="companyIdSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAuthCompanyListByCompanyId" resultType="com.ruoyi.system.domain.SysCompany">
        select * from sys_company where id in
        <foreach collection="companyIdSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCatalogueLicenseList" resultType="org.ruoyi.core.license.domain.ZzLicenseCatalogue">
        select ic.id, ic.catalogue_name as 'catalogueName'
        from zz_license_catalogue ic
        <where>
            <if test="unitIdSet != null and unitIdSet.size() > 0">
                AND ic.unit_id in
                <foreach collection="unitIdSet" item="unitId" separator="," open="(" close=")">
                    #{unitId}
                </foreach>
            </if>
        </where>
        group by ic.id
        order by ic.order_num,create_time desc
    </select>
</mapper>
