package org.ruoyi.core.qiyeVX.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.qiyeVX.domain.VxUser;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName VXMapper.java
 * @Description TODO
 * @createTime 2023年05月29日 10:15:00
 */
@Mapper
public interface VXMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public VxUser selectVxUserById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param vxUser 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<VxUser> selectVxUserList(VxUser vxUser);

    /**
     * 新增【请填写功能名称】
     *
     * @param vxUser 【请填写功能名称】
     * @return 结果
     */
    public int insertVxUser(VxUser vxUser);

    /**
     * 修改【请填写功能名称】
     *
     * @param vxUser 【请填写功能名称】
     * @return 结果
     */
    public int updateVxUser(VxUser vxUser);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVxUserById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVxUserByIds(Long[] ids);

    /**
     * 批量插入
     *
     * @param list 列表
     * @return int
     */
    int  batchInsert (@Param("list") List<VxUser> list);

    List<VxUser> selectByUserId(@Param("userList") List<Long> userList);

    /**
     * 根据userID查询企业微信相关信息
     * @param userId
     * @return
     */
    VxUser selectSingleByUserId(@Param("userId") Long userId);
}
