package org.ruoyi.core.license.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.license.domain.ZzLicenseFiles;

/**
 * 证照附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Mapper
public interface ZzLicenseFilesMapper 
{
    /**
     * 查询证照附件
     * 
     * @param id 证照附件主键
     * @return 证照附件
     */
    public ZzLicenseFiles selectZzLicenseFilesById(Long id);

    /**
     * 查询证照附件列表
     * 
     * @param zzLicenseFiles 证照附件
     * @return 证照附件集合
     */
    public List<ZzLicenseFiles> selectZzLicenseFilesList(ZzLicenseFiles zzLicenseFiles);

    /**
     * 新增证照附件
     * 
     * @param zzLicenseFiles 证照附件
     * @return 结果
     */
    public int insertZzLicenseFiles(ZzLicenseFiles zzLicenseFiles);

    /**
     * 修改证照附件
     * 
     * @param zzLicenseFiles 证照附件
     * @return 结果
     */
    public int updateZzLicenseFiles(ZzLicenseFiles zzLicenseFiles);

    /**
     * 删除证照附件
     * 
     * @param id 证照附件主键
     * @return 结果
     */
    public int deleteZzLicenseFilesById(Long id);

    /**
     * 批量删除证照附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZzLicenseFilesByIds(Long[] ids);

    /**
     * 批量插入证照附件集合
     * @param licenseFilesList
     */
    public void insertZzLicenseFileList(@Param("licenseFilesList")List<ZzLicenseFiles> licenseFilesList);

    /**
     * 根据证照id查询证照附件
     * @param licenseId
     * @return
     */
    List<ZzLicenseFiles> selectZzLicenseFilesByLicenseId(String licenseId);
}
