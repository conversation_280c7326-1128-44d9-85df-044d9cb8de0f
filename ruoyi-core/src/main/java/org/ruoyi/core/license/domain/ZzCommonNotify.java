package org.ruoyi.core.license.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 证照-代办通知对象 zz_common_notify
 * 
 * <AUTHOR>
 * @date 2024-02-05
 */
public class ZzCommonNotify extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 证照id */
    private String licenseId;

    /** 通知模块 */
    @Excel(name = "通知模块")
    private String notifyModule;

    private String url;

    /** 通知类型 0通知 1待办 */
    @Excel(name = "通知类型 0通知 1待办")
    private String notifyType;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String notifyMsg;

    /** 待处理人id */
    @Excel(name = "待处理人id")
    private Long disposeUser;

    /** 阅读状态 0未阅 1已阅 */
    @Excel(name = "阅读状态 0未阅 1已阅")
    private String viewFlag;

    /** 状态 0正常 1禁用 */
    @Excel(name = "状态 0正常 1禁用")
    private String status;

    public String getLicenseId() {
        return licenseId;
    }

    public void setLicenseId(String licenseId) {
        this.licenseId = licenseId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
    public void setNotifyModule(String notifyModule) 
    {
        this.notifyModule = notifyModule;
    }

    public String getNotifyModule() 
    {
        return notifyModule;
    }
    public void setNotifyType(String notifyType) 
    {
        this.notifyType = notifyType;
    }

    public String getNotifyType() 
    {
        return notifyType;
    }
    public void setNotifyMsg(String notifyMsg) 
    {
        this.notifyMsg = notifyMsg;
    }

    public String getNotifyMsg() 
    {
        return notifyMsg;
    }
    public void setDisposeUser(Long disposeUser) 
    {
        this.disposeUser = disposeUser;
    }

    public Long getDisposeUser() 
    {
        return disposeUser;
    }
    public void setViewFlag(String viewFlag) 
    {
        this.viewFlag = viewFlag;
    }

    public String getViewFlag() 
    {
        return viewFlag;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("notifyModule", getNotifyModule())
            .append("notifyType", getNotifyType())
            .append("notifyMsg", getNotifyMsg())
            .append("disposeUser", getDisposeUser())
            .append("viewFlag", getViewFlag())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
