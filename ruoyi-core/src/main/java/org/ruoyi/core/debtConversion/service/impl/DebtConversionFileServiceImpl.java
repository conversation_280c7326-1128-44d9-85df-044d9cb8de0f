package org.ruoyi.core.debtConversion.service.impl;


import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.service.INewAuthorityService;
import com.ruoyi.system.service.ISysCompanyService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.debtConversion.domain.DebtConversionFile;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionFileVo;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionImport;
import org.ruoyi.core.debtConversion.domain.vo.DebtConversionVo;
import org.ruoyi.core.debtConversion.mapper.DebtConversionFileMapper;
import org.ruoyi.core.debtConversion.mapper.DebtConversionMapper;
import org.ruoyi.core.debtConversion.service.IDebtConversionFileService;
import org.ruoyi.core.debtConversion.service.IDebtConversionService;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 债转文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class DebtConversionFileServiceImpl implements IDebtConversionFileService
{
    @Autowired
    private DebtConversionFileMapper debtConversionFileMapper;
    @Autowired
    private IDebtConversionService debtConversionService;
    @Autowired
    private ISysCompanyService sysCompanyService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private INewAuthorityService newAuthorityService;
    /**
     * 查询债转文件
     *
     * @param id 债转文件主键
     * @return 债转文件
     */
    @Override
    public DebtConversionFile selectDebtConversionFileById(Long id)
    {
        return debtConversionFileMapper.selectDebtConversionFileById(id);
    }

    /**
     * 查询债转文件列表
     *
     * @param debtConversionFile 债转文件
     * @return 债转文件
     */
    @Override
    public List<DebtConversionFileVo> selectDebtConversionFileList(DebtConversionFileVo debtConversionFile)
    {
        if(!"1".equals(debtConversionFile.getDataSources())) {
            SysUser user = getLoginUser().getUser();
            List<Long> comapnyIds = newAuthorityService.getNewAuthorityForModuleTypeOfCompanyIdListByUserIdAndModuleType(user.getUserId(), AuthModuleEnum.DEBTCONVERSION.getCode());
            if (comapnyIds.isEmpty()) {
                return new ArrayList<>();
            }
            debtConversionFile.setAuthorityCompanyIds(comapnyIds);
        }
        PageUtil.startPage();
        List<DebtConversionFileVo> debtConversionFileVos = debtConversionFileMapper.selectDebtConversionFileList(debtConversionFile);

        List<Long> custIds = debtConversionFileVos.stream().map(DebtConversionFileVo::getCustId).distinct().collect(Collectors.toList());
        SysCompanyVo sysCompanyVo = new SysCompanyVo();
        sysCompanyVo.setCompanyIdList(custIds);
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectSysCompanyList(sysCompanyVo);
        Map<Long, String> companyMap = sysCompanyVos.stream().collect(Collectors.toMap(
                SysCompanyVo::getId,              // Value 选择器
                SysCompanyVo::getCompanyShortName, // Key 选择器
                (existing, replacement) -> existing // 合并函数（处理重复key）
        ));

        List<String> CreateBys = debtConversionFileVos.stream().map(DebtConversionFileVo::getCreateBy).distinct().collect(Collectors.toList());
        SysUser sysUser = new SysUser();
        sysUser.setUserNames(CreateBys);

        List<SysUser> sysUsers = sysUserService.selectUserListOfDayLog(sysUser);
        Map<String, String> userMap = sysUsers.stream().collect(Collectors.toMap(
                SysUser::getUserName,              // Value 选择器
                SysUser::getNickName, // Key 选择器
                (existing, replacement) -> existing // 合并函数（处理重复key）
        ));
        debtConversionFileVos.forEach(vo -> {
            vo.setCompanyShortName(companyMap.get(vo.getCustId()));
            vo.setCreateByName(userMap.get(vo.getCreateBy()));
        });
        return debtConversionFileVos;
    }

    /**
     * 新增债转文件
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    @Override
    public int insertDebtConversionFile(DebtConversionFile debtConversionFile)
    {
        debtConversionFile.setCreateBy(getUsername());
        debtConversionFile.setCreateTime(DateUtils.getNowDate());
        return debtConversionFileMapper.insertDebtConversionFile(debtConversionFile);
    }

    /**
     * 修改债转文件
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    @Override
    public int updateDebtConversionFile(DebtConversionFile debtConversionFile)
    {
        debtConversionFile.setUpdateTime(DateUtils.getNowDate());
        return debtConversionFileMapper.updateDebtConversionFile(debtConversionFile);
    }

    /**
     * 批量删除债转文件
     *
     * @param ids 需要删除的债转文件主键
     * @return 结果
     */
    @Override
    public int deleteDebtConversionFileByIds(Long[] ids)
    {
        return debtConversionFileMapper.deleteDebtConversionFileByIds(ids);
    }

    /**
     * 删除债转文件信息
     *
     * @param id 债转文件主键
     * @return 结果
     */
    @Override
    public int deleteDebtConversionFileById(Long id)
    {
        debtConversionService.deleteDebtConversionByFildId(id);
        return debtConversionFileMapper.deleteDebtConversionFileById(id);
    }

    @Override
    public int importData(DebtConversionFileVo debtConversionFile){
        insertDebtConversionFile(debtConversionFile);
        debtConversionFile.getSuccessList().forEach(vo -> {
            vo.setPushChannel("1");  //小程序推送
            vo.setFileId(debtConversionFile.getId());
            vo.setCreateBy(getUsername());
        });
        return debtConversionService.insertDebtConversionBatch(debtConversionFile.getSuccessList());
    }

    @Override
    public Map<String,List<DebtConversionImport>> importDataCheck(List<DebtConversionImport> debtConversionList){
        // 准备结果集
        Map<String, List<DebtConversionImport>> result = new HashMap<>();
        result.put("duplicateList", new ArrayList<>());
        result.put("successList", new ArrayList<>());

        List<String> codeList = debtConversionList.stream().map(DebtConversionImport::getLoanCode).distinct().collect(Collectors.toList());
        // 获取数据库已有数据的键集合（用于快速比对）
        if (!codeList.isEmpty()){
            DebtConversionImport debtConversionImport = new DebtConversionImport(); //使用一些过滤字段  避免查询全表
            debtConversionImport.setLoanCodeList(codeList);
            Set<String> existingKeys = debtConversionService.selectDebtConversionImportList(debtConversionImport)
                    .stream()
                    .map(this::buildKey)
                    .collect(Collectors.toSet());

            // 使用LinkedHashMap保持原始顺序
            Map<String, List<DebtConversionImport>> groupMap = new LinkedHashMap<>();

            // 第一次遍历：按关键字段分组
            for (DebtConversionImport dc : debtConversionList) {
                String key = buildKey(dc);
                groupMap.computeIfAbsent(key, k -> new ArrayList<>()).add(dc);
            }

            // 第二次遍历：分离重复项
            groupMap.forEach((key, group) -> {
                if (group.size() > 1 || existingKeys.contains(key)) {
                    // 重复数据（当前导入重复或与数据库重复）
                    result.get("duplicateList").addAll(group);
                } else {
                    // 真正唯一的数据
                    result.get("successList").addAll(group);
                }
            });
        }
        //收集
        Set<String> companyShortName = debtConversionList.stream()
                .filter(Objects::nonNull)
                .flatMap(dc -> Stream.of(
                        dc.getCustName(),
                        dc.getPartnerName(),
                        dc.getFundName(),
                        dc.getDebtRecipientName()
                ))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (companyShortName.isEmpty()){
            throw new RuntimeException("请检查导入模版或数据是否正确");
        }
        List<SysCompanyVo> sysCompanyVos = sysCompanyService.selectCompanyListByCompanyShortNames(companyShortName);
        Map<String, Long> companyMap = sysCompanyVos.stream().collect(Collectors.toMap(
                                        SysCompanyVo::getCompanyShortName, // Key 选择器
                                        SysCompanyVo::getId,              // Value 选择器
                                        (existing, replacement) -> existing // 合并函数（处理重复key）
                                ));

        // 使用迭代器安全地移除元素
        Iterator<DebtConversionImport> iterator = result.get("successList").iterator();
        while (iterator.hasNext()) {
            DebtConversionImport vo = iterator.next();

            // 检查公司是否存在
            if (!companyMap.containsKey(vo.getCustName()) || !companyMap.containsKey(vo.getFundName()) || !companyMap.containsKey(vo.getPartnerName()) || !companyMap.containsKey(vo.getDebtRecipientName())) {
                iterator.remove(); // 安全地从successList中移除
                result.get("duplicateList").add(vo);
                continue;
            }
            // 设置ID
            vo.setCustId(companyMap.get(vo.getCustName()));
            vo.setFundId(companyMap.get(vo.getFundName()));
            vo.setPartnerId(companyMap.get(vo.getPartnerName()));
            vo.setDebtRecipientId(companyMap.get(vo.getDebtRecipientName()));
        }

        return result;
    }

    /**
     * 构建比对键
     */
    private String buildKey(DebtConversionImport dc) {
        return String.join("|",
                dc.getLoanCode(),
                dc.getBorrower(),
                dc.getIdCard(),
                dc.getPhoneNum(),
                formatDate(dc.getLoanTime()),
                formatDate(dc.getGuaranteeTime()),
                String.valueOf(dc.getCustName()),
                String.valueOf(dc.getPartnerName()),
                String.valueOf(dc.getFundName()),
                dc.getLoanAmount() != null ? dc.getLoanAmount().toString() : "null",
                String.valueOf(dc.getDebtRecipientName())
        );
    }
    /**
     * 安全格式化日期
     */
    private static String formatDate(Date date) {
        return date != null ? new SimpleDateFormat("yyyy-MM-dd").format(date) : "null";
    }

    /**
     *  推送债转通知
     *
     * @param debtConversionFile 债转文件
     * @return 结果
     */
    @Override
    public int pushDebtConversion(DebtConversionFile debtConversionFile)
    {
        debtConversionFile.setPushStatus("3");
        debtConversionFile.setNoticeLaunchTime(DateUtils.getNowDate());
        debtConversionFile.setUpdateTime(DateUtils.getNowDate());

        DebtConversionVo debtConversionVo = new DebtConversionVo();
        debtConversionVo.setFileId(debtConversionFile.getId());
        List<DebtConversionVo> debtConversionVos = debtConversionService.selectDebtConversionList(debtConversionVo);

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        // 获取今天的结束时间（23:59:59）
        LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX);
        // 计算剩余秒数
        Duration duration = Duration.between(now, endOfDay);
        long remainingSeconds = duration.getSeconds();
        if (!redisCache.exists("debtConversionCodeCount")){
            redisCache.setCacheObject("debtConversionCodeCount",0,remainingSeconds, TimeUnit.SECONDS);
        }
        String format = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        debtConversionVos.forEach(vo -> {
            int cacheObject = (int)redisCache.getCacheObject("debtConversionCodeCount") + 1;
            redisCache.setCacheObject("debtConversionCodeCount",cacheObject,remainingSeconds, TimeUnit.SECONDS);
            vo.setDebtConversionCode(format + cacheObject);
        });
        debtConversionService.batchUpdateDebtConversionCode(debtConversionVos);

        debtConversionFile.setNoticeCompleteTime(DateUtils.getNowDate());
        return debtConversionFileMapper.updateDebtConversionFile(debtConversionFile);
    }
}
