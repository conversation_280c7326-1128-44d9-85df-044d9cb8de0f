package org.ruoyi.core.debtConversion.mapper;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.ruoyi.core.debtConversion.domain.InvoicingMiddle;

import java.util.List;

/**
 * 开票申请业务关联数据中间Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@DataSource(DataSourceType.DEBT)
public interface InvoicingMiddleMapper
{
    /**
     * 查询开票申请业务关联数据中间
     *
     * @param id 开票申请业务关联数据中间主键
     * @return 开票申请业务关联数据中间
     */
    public InvoicingMiddle selectInvoicingMiddleById(Long id);

    /**
     * 查询开票申请业务关联数据中间列表
     *
     * @param invoicingMiddle 开票申请业务关联数据中间
     * @return 开票申请业务关联数据中间集合
     */
    public List<InvoicingMiddle> selectInvoicingMiddleList(InvoicingMiddle invoicingMiddle);

    /**
     * 新增开票申请业务关联数据中间
     *
     * @param invoicingMiddle 开票申请业务关联数据中间
     * @return 结果
     */
    public int insertInvoicingMiddle(InvoicingMiddle invoicingMiddle);

    /**
     * 批量新增开票申请业务关联数据中间
     *
     * @param invoicingMiddleList 开票申请业务关联数据中间列表
     * @return 结果
     */
    public int batchInsertInvoicingMiddle(List<InvoicingMiddle> invoicingMiddleList);

    /**
     * 修改开票申请业务关联数据中间
     *
     * @param invoicingMiddle 开票申请业务关联数据中间
     * @return 结果
     */
    public int updateInvoicingMiddle(InvoicingMiddle invoicingMiddle);

    /**
     * 删除开票申请业务关联数据中间
     *
     * @param id 开票申请业务关联数据中间主键
     * @return 结果
     */
    public int deleteInvoicingMiddleById(Long id);

    /**
     * 批量删除开票申请业务关联数据中间
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInvoicingMiddleByIds(Long[] ids);
}
