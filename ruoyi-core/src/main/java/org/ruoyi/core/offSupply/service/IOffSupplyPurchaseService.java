package org.ruoyi.core.offSupply.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import org.ruoyi.core.offSupply.domain.OffSupplyPurchase;
import org.ruoyi.core.offSupply.domain.vo.SupplyPurchaseVo;

/**
 * 办公用品采购单Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface IOffSupplyPurchaseService 
{
    /**
     * 查询办公用品采购单
     * 
     * @param id 办公用品采购单主键
     * @return 办公用品采购单
     */
    public OffSupplyPurchase selectOffSupplyPurchaseById(Long id);

    /**
     * 查询办公用品采购单列表
     * 
     * @param offSupplyPurchase 办公用品采购单
     * @return 办公用品采购单集合
     */
    public List<OffSupplyPurchase> selectOffSupplyPurchaseList(OffSupplyPurchase offSupplyPurchase);

    /**
     * 新增办公用品采购单
     * 
     * @param offSupplyPurchase 办公用品采购单
     * @return 结果
     */
    public AjaxResult insertOffSupplyPurchase(OffSupplyPurchase offSupplyPurchase);

    /**
     * 修改办公用品采购单
     * 
     * @param offSupplyPurchase 办公用品采购单
     * @return 结果
     */
    public AjaxResult updateOffSupplyPurchase(OffSupplyPurchase offSupplyPurchase);

    /**
     * 批量删除办公用品采购单
     * 
     * @param ids 需要删除的办公用品采购单主键集合
     * @return 结果
     */
    public int deleteOffSupplyPurchaseByIds(Long[] ids);

    /**
     * 删除办公用品采购单信息
     * 
     * @param id 办公用品采购单主键
     * @return 结果
     */
    public int deleteOffSupplyPurchaseById(Long id);

    /**
     * 采购单提交时修改状态
     * @param supplyPurchaseVo
     * @return
     */
    int updateInfoByRequestInfo(SupplyPurchaseVo supplyPurchaseVo);
}
