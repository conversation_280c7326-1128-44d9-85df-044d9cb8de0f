package org.ruoyi.core.offSupply.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.AuthMain;
import com.ruoyi.system.domain.SysCompany;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.offSupply.domain.OffReceiveMain;
import org.ruoyi.core.offSupply.domain.OffReceiveDetail;
import org.ruoyi.core.offSupply.domain.OffReceivePurchaseDetail;
import org.ruoyi.core.offSupply.domain.OffSupplyMain;
import org.ruoyi.core.offSupply.domain.vo.OffReceiveReport;
import org.ruoyi.core.offSupply.domain.vo.ReceiveReportVo;
import org.ruoyi.core.offSupply.domain.vo.SysComVo;

/**
 * 办公用品领用主Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface OffReceiveMainMapper 
{
    /**
     * 查询办公用品领用
     * 
     * @param id 办公用品领用主主键
     * @return 办公用品领用主
     */
    public OffReceiveMain selectOffReceiveMainById(Long id);

    /**
     * 查询办公用品领用列表
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 办公用品领用主集合
     */
    public List<OffReceiveMain> selectOffReceiveMainList(OffReceiveMain offReceiveMain);

    /**
     * 新增办公用品领用
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 结果
     */
    public int insertOffReceiveMain(OffReceiveMain offReceiveMain);

    /**
     * 修改办公用品领用
     * 
     * @param offReceiveMain 办公用品领用主
     * @return 结果
     */
    public int updateOffReceiveMain(OffReceiveMain offReceiveMain);

    /**
     * 删除办公用品领用
     * 
     * @param id 办公用品领用主主键
     * @return 结果
     */
    public int deleteOffReceiveMainById(Long id);

    /**
     * 批量删除办公用品领用
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOffReceiveMainByIds(Long[] ids);

    /**
     * 批量删除办公用品申请详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOffReceiveDetailByReceiveIds(Long[] ids);
    
    /**
     * 批量新增办公用品申请详情
     * 
     * @param offReceiveDetailList 办公用品申请详情列表
     * @return 结果
     */
    public int batchOffReceiveDetail(@Param("offReceiveDetailList") List<OffReceiveDetail> offReceiveDetailList);
    

    /**
     * 通过办公用品领用主主键删除办公用品申请详情信息
     * 
     * @param id 办公用品领用主ID
     * @return 结果
     */
    public int deleteOffReceiveDetailByReceiveId(Long id);

    /**
     * 查询当天的领用数量
     * @return
     */
    int selectCount();

    /**
     * 根据流程id更新状态
     * @param offReceiveMain
     */
    int updateReceiveStatusByFlowId(OffReceiveMain offReceiveMain);

    /**
     * 获取物品领用信息集合
     * @param receiveReportVo
     * @return
     */
    List<OffReceiveReport> selectSupplyReceiveReportList(ReceiveReportVo receiveReportVo);

    /**
     * 查询公司信息集合列表
     * @param reportVo
     * @return
     */
    List<SysComVo> selectSysCompanyList(ReceiveReportVo reportVo);

    /**
     * 查询所有领用记录
     * @return
     */
    List<OffReceiveReport> selectAllReceiveList(@Param("itemIds") Set<Long>itemIds);

    /**
     * 根据用户id查询用户有哪些公司的物品管理员权限
     * @param userId 用户id
     * @param moduleType 模块类型
     * @param roleType 角色类型
     */
    List<AuthMain> getNewAuthForModuleAndRole(@Param("userId") Long userId, @Param("moduleType") String moduleType, @Param("roleType") String roleType);

    /**
     * 根据权限主表id查询附表信息
     * @param id
     * @return
     */
    List<AuthDetail> selectAuthDetailByMainId(Long id);

    /**
     * 根据权限主表id集合查询权限副表信息集合
     * @param newAuthForModuleAndRole
     * @return
     */
    List<AuthDetail> selectAuthDetailByMainIdList(@Param("newAuthForModuleAndRole") List<AuthMain> newAuthForModuleAndRole);

    /**
     * 查询当前用户是否有该公司流程发起权限
     * @param userId
     * @return
     */
    List<AuthMain> selectOaLaunchAuth(@Param("userId") Long userId, @Param("moduleType") String moduleType, @Param("roleType") String roleType);

    /**
     * 根据领用申请主表查询明细表
     * @param id
     * @return
     */
    List<OffReceiveDetail> selectOffReceiveDetailByReceiveId(Long id);

    /**
     * 根据流程id查询办公用品领用、物品领用、采购申请表单信息
     * @param processId
     * @return
     */
    OffReceivePurchaseDetail selectOffReceiveMainByProcessId(String processId);

    /**
     * 流程发起时保存表单json
     * @param offReceivePurchaseDetail
     */
    int insertOffReceivePurchaseInfo(OffReceivePurchaseDetail offReceivePurchaseDetail);

    /**
     * 根据流程id删除数据
     * @param processId
     */
    int deleteOffReceivePurchaseInfo(String processId);
}
