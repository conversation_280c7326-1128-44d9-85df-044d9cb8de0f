package org.ruoyi.core.esign3.service.impl;

import com.alibaba.fastjson.JSONObject;
import org.ruoyi.core.esign3.service.IMailService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.io.File;

/**
 * 报警记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-30
 */
@Service
public class MailServiceImpl implements IMailService
{
    @Resource
    private JavaMailSender mailSender;
    @Value("${spring.mail.username}")
    private String from;
    //发送人名称
    //private final String sendName = "电子投标保函平台";
    //标题
    private final String title = "电子保函待审核任务通知";


    /**
     * <AUTHOR>
     * @Description 发送内部邮件
     * @Date 2025/1/16 10:31
     * @Param [toEmail, message]
     * @return void
     **/
    @Override
    public void sendMessage(String sendName,String toEmail, String message) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(from, sendName);
            helper.setTo(toEmail);
            helper.setSubject(title);
            helper.setText(message, true);
            mailSender.send(mimeMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * <AUTHOR>
     * @Description 发送用户邮件
     * @Date 2025/1/16 10:31
     * @Param [toEmail, message, file, fileName]
     * @return String 返回发送结果，成功返回"success"，失败返回错误信息
     **/
    @Override
    public String sendUserMessage(String sendName,String toEmail, String message, File file, String fileName) {
        try {
            if (file == null || !file.exists()) {
                return "附件文件不存在或为空";
            }

            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(from, sendName);
            helper.setTo(toEmail);
            helper.setSubject("【发票通知】您的电子发票已生成并附上");
            helper.setText(message, true);

            FileSystemResource fsr = new FileSystemResource(file);
            //添加附件
            helper.addAttachment(fileName, fsr);
            mailSender.send(mimeMessage);

            return "success";
        } catch (Exception e) {
            e.printStackTrace();
            return "邮件发送失败: " + e.getMessage();
        }
    }
}
