package org.ruoyi.core.loanMonitor.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.ruoyi.core.loanMonitor.domain.LoanBalanceWarning;
import org.ruoyi.core.loanMonitor.domain.MarginUpdateLog;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;

/**
 * 保证金更新记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface MarginUpdateLogMapper 
{
    /**
     * 查询保证金更新记录
     * 
     * @param id 保证金更新记录主键
     * @return 保证金更新记录
     */
    // public MarginUpdateLog selectMarginUpdateLogById(Integer id);

    /**
     * 查询保证金更新记录列表
     * 
     * @param marginUpdateLog 保证金更新记录
     * @return 保证金更新记录集合
     */
    public List<MarginUpdateLog> selectMarginUpdateLogList(MarginUpdateLog marginUpdateLog);

    /**
     * 新增保证金更新记录
     * 
     * @param marginUpdateLog 保证金更新记录
     * @return 结果
     */
    public int insertMarginUpdateLog(MarginUpdateLog marginUpdateLog);

    /**
     * 修改保证金更新记录
     * 
     * @param marginUpdateLog 保证金更新记录
     * @return 结果
     */
    public int updateMarginUpdateLog(MarginUpdateLog marginUpdateLog);

    /**
     * 根据项目唯一编码查询保证金总额
     * @param projectCode 项目编码
     * @return 保证金总额
     */
    BigDecimal selectTotalMarginByProjectCode(Integer projectCode);

    /**
     * 根据项目唯一编码查询保证金减少总额
     * @param projectCode
     * @return
     */
    BigDecimal selectTotalDecreaseByProjectCode(Integer projectCode);

    /**
     * 根据项目立项id查询项目信息
     * @param projectCode
     * @return
     */
    OaProjectDeploy selectOaProjectDeployInfoById(Long projectCode);

}
