package org.ruoyi.core.meeting.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.ruoyi.core.meeting.domain.MeetingFile;
import org.ruoyi.core.meeting.domain.MeetingRoom;

import java.util.List;

/**
 * 会议室管理对象 hy_meeting_room
 *
 * <AUTHOR>
 * @date 2024-10-24
 */
@Data
public class MeetingRoomVo extends MeetingRoom
{
    @JSONField(serialize = false, deserialize = false)
    private List<Long> meetingRoomManagerIds;

    private List<SysUser> meetingRoomManagerList;

    private List<Long> meetingReminderConditionsList;

    private List<Long> meetingNotificationMethodList;

    @Excel(name = "会议室负责人")
    private String meetingRoomManagerListString;

    private String meetingReminderConditionsListString;

    private String meetingNotificationMethodListString;

    private String belongCompanyName;

    @JSONField(serialize = false, deserialize = false)
    private List<Long> fileIds;

    private List<MeetingFile> files;
}
