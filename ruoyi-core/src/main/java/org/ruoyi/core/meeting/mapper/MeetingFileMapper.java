package org.ruoyi.core.meeting.mapper;

import org.ruoyi.core.meeting.domain.MeetingFile;
import org.ruoyi.core.meeting.domain.vo.MeetingFileVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;

import java.util.List;

/**
 * 会议文件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface MeetingFileMapper
{
    /**
     * 查询会议文件
     *
     * @param id 会议文件主键
     * @return 会议文件
     */
    public MeetingFile selectMeetingFileById(Long id);

    /**
     * 查询会议文件列表
     *
     * @param meetingFile 会议文件
     * @return 会议文件集合
     */
    public List<MeetingFile> selectMeetingFileList(MeetingFile meetingFile);

    /**
     * 新增会议文件
     *
     * @param meetingFile 会议文件
     * @return 结果
     */
    public int insertMeetingFile(MeetingFile meetingFile);

    /**
     * 修改会议文件
     *
     * @param meetingFile 会议文件
     * @return 结果
     */
    public int updateMeetingFile(MeetingFile meetingFile);

    /**
     * 删除会议文件
     *
     * @param id 会议文件主键
     * @return 结果
     */
    public int deleteMeetingFileById(Long id);

    /**
     * 批量删除会议文件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMeetingFileByIds(Long[] ids);

    public int deleteByCorrelationId(MeetingFileVo personnelFile);

    public int correlationFile(MeetingFileVo personnelFile);
}
