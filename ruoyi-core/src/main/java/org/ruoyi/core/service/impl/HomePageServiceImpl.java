package org.ruoyi.core.service.impl;

import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.core.DateNumerationUtils;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.mapper.SysDictDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.util.Strings;
import org.ruoyi.core.domain.CustomLayout;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.DProfitData;
import org.ruoyi.core.domain.DVintageMonth;
import org.ruoyi.core.mapper.CustomLayoutMapper;
import org.ruoyi.core.mapper.DDataMapper;
import org.ruoyi.core.mapper.EChartsMapper;
import org.ruoyi.core.service.HomePageService;
import org.ruoyi.core.tool.DataDisposeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageServiceImpl.java
 * @Description 首页
 * @createTime 2022年07月13日 09:33:00
 */
@Service
@Slf4j
public class HomePageServiceImpl implements HomePageService {

    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    @Autowired
    private EchartsServiceImpl echartsService;
    @Autowired
    private EChartsMapper eChartsMapper;
    @Autowired
    private DDataMapper dDataMapper;

    @Autowired
    private CustomLayoutMapper customLayoutMapper;

    @Autowired
    private EchartsServiceImpl echartsServiceimpl;

    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;

    @Override
    @DataScope()
    public Map<String, Object> getHomePageData(DData dData) {

        HashMap<String, Object> homeData = new HashMap<>();
        SimpleDateFormat sp =new SimpleDateFormat("yyyy-MM-dd");
        String partnerNo = "partner_no";
        String fundNo = "fund_no";


        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
//        if (Strings.isNotEmpty(dData.getProductNo())) {
//            products = Arrays.asList(dData.getProductNo().split(","));
//        }
        products = sysSelectDataRefService.getProductCode1(dData);
        //获取合作方和资产方数量
        //合作方
        int partnerNum = dDataMapper.dictDataNumByType(partnerNo,platforms, custNos, partnerNos, fundNos,products,dData);
        //资产方
        int fundNum = dDataMapper.dictDataNumByType(fundNo,platforms, custNos, partnerNos, fundNos,products,dData);

        Calendar cal= Calendar.getInstance();
        cal.add(Calendar.DATE,-1);
        Date d=cal.getTime();
        String yesterday=sp.format(d);//获取昨天日期

        homeData.put("partnerCount",partnerNum);
        homeData.put("fundCount",fundNum);
        //新权限前置查询（可以查看的项目权限）
        String productCode = sysSelectDataRefService.getProductCode(dData);
        if (productCode != null) {
            dData.setProductNo(productCode);
        }
        //得到固定数值展示
        Map<String, Object> balanceData = this.getBalanceData(dData, yesterday);
        //固定数值数据放入map
        homeData.putAll(balanceData);
        //合作方数据
        Map<String, Object> partnerEchart =  this.partnerEchartData(dData);
        //资金方echart数据
        Map<String, Object> fundEchart =  this.fundEchartData(dData);
        //担保公司echart数据
        Map<String, Object> custEchart =  this.custEchartData(dData);
        //得到echart图数据 （）
        Map<String, Object> echartHomeData = this.homeEChartData(dData, yesterday);
        //vintage
        Map<String, Object> vintageDataMap = this.homeVintageEChart(dData, yesterday);
        //每日放还款金额
        Map<String, Object> map = this.repayprintAmount(dData, yesterday);
        //坏账数据计算
//        Map<String,Object> badDebtRate = this.badDebtData(dData);
        String nearDataDate = echartsService.getNearDataDate();
        DProfitData dProfitData = new DProfitData();
        dProfitData.setReconYear(nearDataDate);
        dProfitData.setPlatformNo(dData.getPlatformNo());
        dProfitData.setPartnerNo(dData.getPartnerNo());
        dProfitData.setFundNo(dData.getFundNo());
        dProfitData.setCustNo(dData.getCustNo());
        dProfitData.setProductNo(productCode);
        Map<String, Object> badDebtRateMap = echartsService.badDebtRateData(dProfitData);
        List<String> dataList = (List<String>) badDebtRateMap.get("dataList");
        List<Map<String,Object>> xaxisData = (List<Map<String, Object>>) badDebtRateMap.get("xaxisData");
        if (dataList.size() > 10) {
            //先翻转过来，因为数据是里X轴近的先展示
            Collections.reverse(dataList);
            Collections.reverse(xaxisData);
            //做切割，只要前十个
            dataList = dataList.subList(0, 10);
            xaxisData = xaxisData.subList(0, 10);
            //再翻转过去，数据达到正确格式
            Collections.reverse(dataList);
            Collections.reverse(xaxisData);
        }
        //fixme:
//        homeData.put("badDebtdataList", dataList);
        homeData.put("badDebtxaxisData", xaxisData);

        //合作方在贷余额堆叠
        Map<String, Object> partnerStack = echartsServiceimpl.partnerEchartLineStackLimit(platforms, custNos, partnerNos, fundNos, dData);
        //资金方在贷余额堆叠
        Map<String, Object> fundStack = echartsServiceimpl.fundEchartLineStackLimit(platforms, custNos, partnerNos, fundNos, dData);


        List<Map<String, Object>> list = echartsService.profitRankForIndex(dProfitData );
//        fixme:
//        homeData.put("profitRankStack", list);



        homeData.putAll(partnerStack);
        homeData.putAll(fundStack);
//        homeData.putAll(badDebtRateMap);
        homeData.putAll(fundEchart);
        homeData.putAll(partnerEchart);
        homeData.putAll(custEchart);
        homeData.putAll(vintageDataMap);
        homeData.putAll(echartHomeData);
        homeData.putAll(map);
        return homeData;
    }


    private Map<String,Object> stackPartner(){
        HashMap<String, Object> returnMap = new HashMap<>();






        return returnMap;
    }

    private Map<String, Object> badDebtData(DData dData) {
        HashMap<String, Object> returnMap = new HashMap<>();

        List<String> platforms = null;
        if (Strings.isNotEmpty(dData.getPlatformNo())) {
            platforms = Arrays.asList(dData.getPlatformNo().split(","));
        }
        List<String> custNos = null;
        if (Strings.isNotEmpty(dData.getCustNo())) {
            custNos = Arrays.asList(dData.getCustNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(dData.getPartnerNo())) {

            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(dData.getFundNo())) {

            fundNos = Arrays.asList(dData.getFundNo().split(","));
        }

        //获取最近有数据的月份
        String nearDataDate = echartsService.getNearDataDate();
        String tworeconYear =  nearDataDate.substring(0,4);
        String tworeconMonth = nearDataDate.substring(5,7);
        Map<String, Object> month = echartsService.getMonth();
        Object o = month.get(tworeconMonth);
        List<Map<String,Object>> profitDatas = eChartsMapper.getHomePageBadDebtData(platforms,custNos,partnerNos,fundNos,tworeconYear,o.toString(),dData);


        ArrayList<String> xaxisData = new ArrayList<>();
        ArrayList<String> dateData = new ArrayList<>();

        List<DProfitData> hzlList =new ArrayList<>();
        DProfitData hzl;


        //得到所有的编码Map
        Map<String, Object> dictMap = echartsService.getDictMap();
        for (Map<String, Object> profitData : profitDatas) {
        	hzl=new DProfitData();
        	hzl.setPartnerNo(profitData.get("partnerNo")+"");
        	hzl.setFundNo(profitData.get("fundNo")+"");
        	hzl.setPlatformNo(dictMap.get("partner_no_"+profitData.get("partnerNo")) +"+"+  dictMap.get("fund_no_"+profitData.get("fundNo")));
//            xaxisData.add(dictMap.get("partner_no_"+profitData.get("partnerNo")) +"+"+  dictMap.get("fund_no_"+profitData.get("fundNo"))   );
            if(null == profitData.get("dataa")){
            	hzl.setDataJanuary(new BigDecimal(0));
//                dateData.add("0.00");
//                profitData.put("dataa","0.00");
            }else {
            	BigDecimal fundAmt=eChartsMapper.getHomePageBadDebtDataFundBalance(profitData.get("partnerNo")+"",profitData.get("fundNo")+"",com.ruoyi.common.utils.DateUtils.findLastMonthEndDayByStr(tworeconYear+"-"+tworeconMonth+"-01"),dData);
            	BigDecimal huaizhangAmt= new BigDecimal(profitData.get("dataa").toString());
            	if(fundAmt !=null && fundAmt.compareTo(new BigDecimal(0))>0 && huaizhangAmt !=null && huaizhangAmt.compareTo(new BigDecimal(0))>0) {
//            		dateData.add(huaizhangAmt.multiply(new BigDecimal("100")).divide(fundAmt, 2, BigDecimal.ROUND_HALF_UP)+"");
            		hzl.setDataJanuary(huaizhangAmt.multiply(new BigDecimal("100")).divide(fundAmt, 2, BigDecimal.ROUND_HALF_UP));
            	}else {
            		hzl.setDataJanuary(new BigDecimal(0));
//            		dateData.add("0.00");
            	}
            }
//            profitData.put("dataa",profitData.get("dataa").toString());
            hzlList.add(hzl);
        }

        hzlList.sort(Comparator.comparing(DProfitData::getDataJanuary).reversed());

        for (int i=0;(i<10&&i<hzlList.size());i++) {
        	xaxisData.add(hzlList.get(i).getPlatformNo());
        	dateData.add(hzlList.get(i).getDataJanuary().setScale(2, BigDecimal.ROUND_HALF_UP)+"");
		}

        returnMap.put("badDebtxaxisData",xaxisData);
        returnMap.put("badDebtdataList",dateData);
        returnMap.put("badDebtMonth",tworeconMonth);


        return returnMap;
    }

    /**
     * repayprint数量
     *
     * @param dData     维数据
     * @param yesterday 昨天
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public  Map<String, Object> repayprintAmount(DData dData,String yesterday) {
        HashMap<String, Object> returnMap = new HashMap<>();
        //新增放款

        //新增还款
        //由于获取的是今天的时间所以取30天之前的日期作为开始日期
        Date date30 =new Date(System.currentTimeMillis()-24*60*60*1000*31L);
        SimpleDateFormat sp =new SimpleDateFormat("yyyy-MM-dd");
        String format30 = sp.format(date30);
        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(format30,yesterday);

        List<String> platforms = null;
//        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }

        //日数据
        List<Map<String, Object>> repayAmount = dDataMapper.getRepayAmount(platforms, custNos, partnerNos, fundNos, format30, yesterday, dData, products);
        //周
        List<Map<String, Object>> weekRepayAmount = dDataMapper.getWeekRepayAmount(platforms, custNos, partnerNos, fundNos,dData, products);
        //月
        List<Map<String, Object>> monthRepayAmount = dDataMapper.getMonthRepayAmount(platforms, custNos, partnerNos, fundNos, dData, products);
        //年
        List<Map<String, Object>> yearRepayAmount = dDataMapper.getYearRepayAmount(platforms, custNos, partnerNos, fundNos, dData, products);
        String mapKey = "reconDate";
        Map<String, Map<String, Object>> echartDataMap = DataDisposeUtil.listToMap(repayAmount, mapKey);
        //周数据转换
        Map<String, Map<String, Object>> echartWeekDataMap = DataDisposeUtil.listToMap(weekRepayAmount, mapKey);
        //月数据转换
        Map<String, Map<String, Object>> echartMonthDataMap = DataDisposeUtil.listToMap(monthRepayAmount, mapKey);
        //年数据转换
        Map<String, Map<String, Object>> echartYearDataMap = DataDisposeUtil.listToMap(yearRepayAmount, mapKey);
        //处理数据
        Map<String, Object> map = this.repayWMYData(echartWeekDataMap, echartMonthDataMap, echartYearDataMap);

        //新增贷款
        ArrayList<Object> addAmountList = new ArrayList<>();
        //新增还款
        ArrayList<Object> addRepayPrintAmountList = new ArrayList<>();



        for (String xixa : xixas) {
            if(null==echartDataMap.get(xixa)){
                //添加新增贷款
                addAmountList.add("0.00");
                //新增还款
                addRepayPrintAmountList.add("0.00");
            }else {
                Map<String, Object> dataMap = echartDataMap.get(xixa);

                //小数点往前挪动4位保留两位小数即除以10000
                BigDecimal addAmount = new BigDecimal(dataMap.get("addAmount").toString());
                BigDecimal addAmountDivide = addAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                //添加累计贷款本金余额
                addAmountList.add(addAmountDivide);
                BigDecimal addRepayPrintAmount = new BigDecimal(dataMap.get("addRepayPrintAmount").toString());
                BigDecimal addRepayPrintAmountDivide = addRepayPrintAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                //新增贷款笔数
                addRepayPrintAmountList.add(addRepayPrintAmountDivide);
            }
        }
        returnMap.putAll(map);
        returnMap.put("repayXixas",xixas);
        returnMap.put("repayfangData",addAmountList);
        returnMap.put("repayhuanData",addRepayPrintAmountList);


        return returnMap;
    }
    public Map<String,Object> repayWMYData(Map<String, Map<String, Object>> weekData,Map<String, Map<String, Object>> monthData,Map<String, Map<String, Object>> yearData){
        HashMap<String, Object> returnMap = new HashMap<>();
        //获取30周的X轴
        List<String> weekXaxisData = this.getWeekXaxisData(30);
        //获取24个月的X轴
        List<String> monthXaxisData = this.getMonthXaxisData(24);
        //获取5年的X轴
        List<String> yearXaxisData = this.getYearXaxisData(5);
        //处理周放还款
        Map<String, Object> weekDataMap = this.repayDataDispose(weekXaxisData, weekData);
        returnMap.put("weekRepayFangData",weekDataMap.get("fangData"));
        returnMap.put("weekRepayHuanData",weekDataMap.get("huanData"));
        returnMap.put("weekRepayXaxis",weekXaxisData);
        //处理月放还款
        Map<String, Object> monthDataMap = this.repayDataDispose(monthXaxisData, monthData);
        returnMap.put("monthRepayFangData",monthDataMap.get("fangData"));
        returnMap.put("monthRepayHuanData",monthDataMap.get("huanData"));
        returnMap.put("monthRepayXaxis",monthXaxisData);
        //处理月放还款
        Map<String, Object> yearDatamap = this.repayDataDispose(yearXaxisData, yearData);
        returnMap.put("yearRepayFangData",yearDatamap.get("fangData"));
        returnMap.put("yearRepayHuanData",yearDatamap.get("huanData"));
        returnMap.put("yearRepayXaxis",yearXaxisData);
        return returnMap;
    }


    /**
     * 放还款数据处理
     *
     * @param xixas xixas
     * @param data  数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String, Object>  repayDataDispose(List<String> xixas,Map<String, Map<String, Object>> data){
        HashMap<String, Object> returnMap = new HashMap<>();
        //新增贷款
        ArrayList<Object> addAmountList = new ArrayList<>();
        //新增还款
        ArrayList<Object> addRepayPrintAmountList = new ArrayList<>();

        for (String xixa : xixas) {
            if(null==data.get(xixa)){
                //添加新增贷款
                addAmountList.add("0.00");
                //新增还款
                addRepayPrintAmountList.add("0.00");
            }else {
                Map<String, Object> dataMap = data.get(xixa);

                //小数点往前挪动4位保留两位小数即除以10000
                BigDecimal addAmount = new BigDecimal(dataMap.get("addAmount").toString());
                BigDecimal addAmountDivide = addAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                //添加累计贷款本金余额
                addAmountList.add(addAmountDivide);
                BigDecimal addRepayPrintAmount = new BigDecimal(dataMap.get("addRepayPrintAmount").toString());
                BigDecimal addRepayPrintAmountDivide = addRepayPrintAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                //新增贷款笔数
                addRepayPrintAmountList.add(addRepayPrintAmountDivide);
            }
        }
        returnMap.put("fangData",addAmountList);
        returnMap.put("huanData",addRepayPrintAmountList);

        return  returnMap;

    }
    private Map<String, Object> partnerEchartData(DData dData) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }

//        HashMap<String, Object> roleSql = new HashMap<>();
//        roleSql.put("dataScope",EchartsServiceImpl.roleStringCost(dData.getParams().get("dataScope").toString()));
//        dData.setParams(roleSql);
        //得到所有合作方名字
        String dictType = "partner_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);
        //得到所有数据
        List<Map<String, Object>> partnerEchartData = eChartsMapper.getPartnerEchartData(platforms, custNos, partnerNos, fundNos,products,dData);
        if(CollectionUtils.isNotEmpty(partnerEchartData)){
            //数据转换
            Map<String, Object> map = EchartsServiceImpl.listToMap(partnerEchartData);
            List<Map<String, Object>> dictAndData = EchartsServiceImpl.dictAddData(map,dictData);
            // 降序
            List<Map<String, Object>> sortedByHeightDescList = dictAndData.stream().sorted((h1, h2) -> (new BigDecimal(h2.get("count").toString())).compareTo(new BigDecimal(h1.get("count").toString()))).collect(Collectors.toList());

            //数据分割
            Map<String, List> countData = this.dataDispose(sortedByHeightDescList);


            //柱状图数据
            returnMap.put("partnerPillarEchart",countData.get("data"));
            //X轴数据
            returnMap.put("partnerXaxis",countData.get("xaxis"));
            //饼图数据
            returnMap.put("partnerPieData",countData.get("pie"));
            return returnMap;
        }
        return returnMap;
    }


    private Map<String, Object> fundEchartData(DData dData) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
//        HashMap<String, Object> roleSql = new HashMap<>();
//        roleSql.put("dataScope",EchartsServiceImpl.roleStringCost(dData.getParams().get("dataScope").toString()));
//        dData.setParams(roleSql);
        //得到所有合作方名字
        String dictType = "fund_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);
        //得到所有数据
        List<Map<String, Object>> fundEchartData = eChartsMapper.getFundEchartData(platforms, custNos, partnerNos, fundNos,products,dData);
        if(CollectionUtils.isNotEmpty(fundEchartData)){
            //数据转换
            Map<String, Object> map = EchartsServiceImpl.listToMap(fundEchartData);
            List<Map<String, Object>> dictAndData = EchartsServiceImpl.dictAddData(map,dictData);
            // 降序
            List<Map<String, Object>> sortedByHeightDescList = dictAndData.stream().sorted((h1, h2) -> (new BigDecimal(h2.get("count").toString())).compareTo(new BigDecimal(h1.get("count").toString()))).collect(Collectors.toList());

            //数据分割
            Map<String, List> countData = this.dataDispose(sortedByHeightDescList);

            //柱状图数据
            returnMap.put("fundPillarEchart",countData.get("data"));
            //X轴数据
            returnMap.put("fundXaxis",countData.get("xaxis"));
            //饼图数据
            returnMap.put("fundPieData",countData.get("pie"));
            return returnMap;
        }
        return returnMap;
    }

    private Map<String, Object> custEchartData(DData dData) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
        //得到所有合作方名字
        String dictType = "cust_no";
        List<Map<String, Object>> dictData = eChartsMapper.getDictData(dictType);
        //得到所有数据
        List<Map<String, Object>> fundEchartData = eChartsMapper.getCustEchartData(platforms, custNos, partnerNos, fundNos,products,dData);
        if(CollectionUtils.isNotEmpty(fundEchartData)){
            //数据转换
            Map<String, Object> map = EchartsServiceImpl.listToMap(fundEchartData);
            List<Map<String, Object>> dictAndData = EchartsServiceImpl.dictAddData(map,dictData);
            // 降序
            List<Map<String, Object>> sortedByHeightDescList = dictAndData.stream().sorted((h1, h2) -> (new BigDecimal(h2.get("count").toString())).compareTo(new BigDecimal(h1.get("count").toString()))).collect(Collectors.toList());

            //数据分割
            Map<String, List> countData = this.dataDispose(sortedByHeightDescList);

            //柱状图数据
            returnMap.put("custPillarEchart",countData.get("data"));
            //X轴数据
            returnMap.put("custXaxis",countData.get("xaxis"));
            //饼图数据
            returnMap.put("custPieData",countData.get("pie"));
            return returnMap;
        }
        return returnMap;
    }
    /**
     * 利润echart数据
     *
     * @param dData 维数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    @Override
    @DataScope(moduleName = "profit")
    public  Map<String,Object> profitEChartData(DData dData){
        HashMap<String, Object> profitData = new HashMap<>();
        //获取当前月以及一年之前的月
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        Date parse = new Date();
        String startTime = simpleDateFormat.format(DateUtils.addMonths(parse,  - 13));
        String endTime = simpleDateFormat.format(DateUtils.addMonths(parse,  - 1));

        //获取X轴数据 也就是两个时间段的所有月份
        List<String> xAxis = DateNumerationUtils.getMonthBetweenDate(startTime, endTime);
        //取出时间参数 截取年份 进数据库查询数据
        String startYear = startTime.substring(0, 4);
        String endYear = endTime.substring(0, 4);
        ArrayList<String> reconYear = new ArrayList<>();
        reconYear.add(startYear);
        reconYear.add(endYear);
        List<String> platforms = null;
        if (Strings.isNotEmpty(dData.getPlatformNo())) {
            platforms = Arrays.asList(dData.getPlatformNo().split(","));
        }
        List<String> partnerNos = null;
        if (Strings.isNotEmpty(dData.getPartnerNo())) {

            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
        }
        List<String> fundNos = null;
        if (Strings.isNotEmpty(dData.getFundNo())) {

            fundNos = Arrays.asList(dData.getFundNo().split(","));
        }

        String statisticalIndex = "1";
        List<Map<String,Object>> profitCalculateData = eChartsMapper.getProfitBystatisticalIndexData(platforms,partnerNos,fundNos,statisticalIndex,reconYear,dData);


        //数据封装  指标_年份_月为key 对应的月份值为value
        HashMap<String, Object> profitDataMap = echartsService.profitDataListToMap(profitCalculateData);
        //利润
        ArrayList<Object> profitList = new ArrayList<>();
        for (String xAxi : xAxis) {
            //获取利润数据
            if(profitDataMap.containsKey("1_"+xAxi) && null != profitDataMap.get("1_"+xAxi)){
                profitList.add(profitDataMap.get("1_"+xAxi));
            }else {
                profitList.add("0.00");
            }
        }
        //利润
        HashMap<String, Object> profitMap = new HashMap<>();
//        profitMap.put("name","利润");
        profitMap.put("type","bar");
//        profitMap.put("stack","abc");
        profitMap.put("data",profitList);

        //每月利润
        ArrayList<Map<String,Object>> profitEChartList = new ArrayList<>();
        profitEChartList.add(profitMap);

        profitData.put("profitData",profitEChartList);
        profitData.put("profitXAxis",xAxis);
        return profitData;
    }

    @Override
    public Map<String, Object> getLoginCoustomLayout(LoginUser loginUser) {
       Map<String,Object>  returnMap = new HashMap<>();
      Map<String,Object> customMap =  customLayoutMapper.getByLoginUserId(loginUser.getUserId());
       if(null == customMap){
           CustomLayout customLayout = new CustomLayout();

           customLayout.setBalance("true");
           customLayout.setRepayCount("true");
           customLayout.setAddAmount("false");
           customLayout.setPartnerPie("true");
           customLayout.setPartnerBar("true");
           customLayout.setFundPie("true");
           customLayout.setFundBar("true");
           customLayout.setCustPie("true");
           customLayout.setCustBar("true");
           customLayout.setVintage("true");
           customLayout.setProfit("true");
           customLayout.setBadDebt("true");
           customLayout.setBalancePartnerStack("true");
           customLayout.setBalanceFundStack("true");
           customLayout.setUserId(loginUser.getUserId());
           customLayoutMapper.insertCustomLayout(customLayout);


           returnMap.put("balance",true);
           returnMap.put("repayCount",true);
           returnMap.put("addAmounton",false);
           returnMap.put("partnerpieon",true);
           returnMap.put("partnerbar",true);
           returnMap.put("fundpieon",true);
           returnMap.put("fundbar",true);
           returnMap.put("custpieon",true);
           returnMap.put("custbar",true);
           returnMap.put("vintageon",true);
           returnMap.put("profiton",true);
           returnMap.put("badDebtDiv",true);
           returnMap.put("balancePartnerStack",true);
           returnMap.put("balanceFundStack",true);

       }else {

           returnMap.put("balance",Boolean.parseBoolean(customMap.get("balance").toString()));
           returnMap.put("repayCount",Boolean.parseBoolean(customMap.get("repayCount").toString()));
           returnMap.put("addAmounton",Boolean.parseBoolean(customMap.get("addAmount").toString()));
           returnMap.put("partnerpieon",Boolean.parseBoolean(customMap.get("partnerPie").toString()));
           returnMap.put("partnerbar",Boolean.parseBoolean(customMap.get("partnerBar").toString()));
           returnMap.put("fundpieon",Boolean.parseBoolean(customMap.get("fundPie").toString()));
           returnMap.put("fundbar",Boolean.parseBoolean(customMap.get("fundBar").toString()));
           returnMap.put("custpieon",Boolean.parseBoolean(customMap.get("custPie").toString()));
           returnMap.put("custbar",Boolean.parseBoolean(customMap.get("custBar").toString()));
           returnMap.put("vintageon",Boolean.parseBoolean(customMap.get("vintage").toString()));
           returnMap.put("profiton",Boolean.parseBoolean(customMap.get("profit").toString()));
           returnMap.put("badDebtDiv",Boolean.parseBoolean(customMap.get("badDebt").toString()));
           returnMap.put("balancePartnerStack",Boolean.parseBoolean(customMap.get("balancePartnerStack").toString()));
           returnMap.put("balanceFundStack",Boolean.parseBoolean(customMap.get("balanceFundStack").toString()));

       }
        return returnMap;
    }

    @Override
    public void updateLayoutByUserId(CustomLayout customLayout) {


        customLayoutMapper.updateDataByUserId(customLayout);
    }

    /**
     * 获取首页数值数据
     *
     * @param dData     参数实体
     * @param yesterday 昨天时间日期
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> getBalanceData(DData dData , String yesterday){
        HashMap<String, Object> balanceData = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");


        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
        //获取昨日贷款本金
        //获取新增贷款本金
        Map<String,Object> totalData = new HashMap<>();
        totalData = dDataMapper.getFixedData(platforms,custNos,partnerNos,fundNos,products,dData);

        //贷款余额
        if( null == totalData || null == totalData.get("totalBalanceAmount")){
            balanceData.put("totalBalanceAmount","0.00");
        }else {
            double v = Double.parseDouble(totalData.get("totalBalanceAmount").toString());
            balanceData.put("totalBalanceAmount",v);
        }

        //累计贷款金额
        if( null == totalData  || null == totalData.get("totalAmount") ){
            balanceData.put("totalAmount","0.00");
        }else {
            double v = Double.parseDouble(totalData.get("totalAmount").toString());
            balanceData.put("totalAmount",v);
        }
        //累计贷款笔数
        if( null == totalData  || null==totalData.get("totalCount") ){
            balanceData.put("totalCount","0");
        }else {
            balanceData.put("totalCount",totalData.get("totalCount"));
        }
        //新增贷款本金
        if( null == totalData  || null == totalData.get("addAmount")){
            balanceData.put("addAmount","0.00");
        }else {
            double v = Double.parseDouble(totalData.get("addAmount").toString());
            balanceData.put("addAmount",v);
        }
        //新增贷款笔数
        if( null == totalData  || null==totalData.get("addCount")){
            balanceData.put("addCount","0");
        }else {
            balanceData.put("addCount",totalData.get("addCount"));
        }

        return balanceData;
    }

    /**
     * echart数据
     *
     * @param dData     维数据
     * @param yesterday 昨天
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> homeEChartData(DData dData,String yesterday){
        HashMap<String, Object> homeEChart = new HashMap<>();
        //参数
        List<String> platforms = null;
        //        if (Strings.isNotEmpty(dData.getPlatformNo())) {
//            platforms = Arrays.asList(dData.getPlatformNo().split(","));
//        }
        List<String> custNos = null;
//        if (Strings.isNotEmpty(dData.getCustNo())) {
//            custNos = Arrays.asList(dData.getCustNo().split(","));
//        }
        List<String> partnerNos = null;
//        if (Strings.isNotEmpty(dData.getPartnerNo())) {
//
//            partnerNos = Arrays.asList(dData.getPartnerNo().split(","));
//        }
        List<String> fundNos = null;
//        if (Strings.isNotEmpty(dData.getFundNo())) {
//
//            fundNos = Arrays.asList(dData.getFundNo().split(","));
//        }
        List<String> products = null;
        if (Strings.isNotEmpty(dData.getProductNo())) {
            products = Arrays.asList(dData.getProductNo().split(","));
        }
        String year =  yesterday.substring(0,4);
        String month = yesterday.substring(0,7);
        String month1 = yesterday.substring(5,7);
//        List<Map<String, Object>> yearNear = dDataMapper.quertyearnearDate(platforms, custNos, partnerNos, fundNos, dData,year);
//        List<Map<String, Object>> monthNear = dDataMapper.quertmonthnearDate(platforms, custNos, partnerNos, fundNos, dData,month);
        List<Map<String, Object>> week = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dData);
        List<Map<String, Object>> monthnew = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dData);
        List<Map<String, Object>> yearnew = dDataMapper.getnearDayData(platforms, custNos, partnerNos, fundNos,products,dData);
        //本周周末
        String weekendTimeSection = com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(0);

        //获取三个月之前的时间
        String beginTime = DateNumerationUtils.getMonthBefore(yesterday, 3);
        //X轴 时间
        List<String> xixas = DateNumerationUtils.getBetweenDate(beginTime,yesterday);
        //获取echart数据
        List<Map<String, Object>> maps  = dDataMapper.homeEChartData(platforms, custNos, partnerNos, fundNos,products, beginTime, yesterday,dData);

        //获取90周的X轴
        List<String> weekXaxisData = this.getWeekendXaxisData(90);
        //获取周数据
        week.removeAll(Collections.singleton(null));
        List<Map<String, Object>> weeksData =  dDataMapper.getWeekDataList(platforms, custNos, partnerNos, fundNos,products,dData,weekXaxisData);
        if(week.size()>0){
            week.get(0).put("reconDate",weekendTimeSection);
            weeksData.add(week.get(0));
        }

        //获取月数据
        //获取24个月的X轴
        List<String> monthXaxisData = this.getMonthendXaxisData(24);
        List<Map<String, Object>> monthData =  dDataMapper.getMonthDataList(platforms, custNos, partnerNos, fundNos,products,dData, monthXaxisData );
            String lastDayOfMonth = com.ruoyi.common.utils.DateUtils.getLastDayOfMonth(Integer.parseInt(year), Integer.parseInt(month1));
        monthnew.removeAll(Collections.singleton(null));
            if(monthnew.size()>0){
                monthnew.get(0).put("reconDate",lastDayOfMonth);
                monthData.add(monthnew.get(0));
            }



        //获取年数据
        List<String> yearXaxisData = this.getYearendXaxisData(5);
        List<Map<String, Object>> yearData =  dDataMapper.getYearDataList(platforms, custNos, partnerNos, fundNos,products,dData,yearXaxisData);
        yearnew.removeAll(Collections.singleton(null));
        if(yearnew.size()>0){
            yearnew.get(0).put("reconDate",year+"-12-31");
            yearData.add(yearnew.get(0));
        }


        homeEChart.put("weekend",weekXaxisData);
        homeEChart.put("monthend",monthXaxisData);
        homeEChart.put("yearend",yearXaxisData);
        //获取笔数周数据
        List<Map<String, Object>> weekscount =  dDataMapper.getWeekDatacountList(platforms, custNos, partnerNos, fundNos,products,dData);
        //获取笔数月数据
        List<Map<String, Object>> monthcount =  dDataMapper.getMonthDatacountList(platforms, custNos, partnerNos, fundNos,products,dData);
        //获取笔数年数据
        List<Map<String, Object>> yearcount =  dDataMapper.getYearDatacountList(platforms, custNos, partnerNos, fundNos,products,dData);



        //添加累计贷款余额
        ArrayList<Object> balanceAList = new ArrayList<>();
        //新增贷款笔数
        ArrayList<Object> addCountList = new ArrayList<>();
        String mapKey = "reconDate";
        //将 List<Map<String, Object>> 格式转换为Map<String, Map<String, Object>> 方便用日期获取数据
        Map<String, Map<String, Object>> echartDataMap = DataDisposeUtil.listToMap(maps, mapKey);
        //转换周数据
        Map<String, Map<String, Object>> weekMapData = DataDisposeUtil.listToMap(weeksData, mapKey);

        Map<String, Map<String, Object>> weekMapcountData = DataDisposeUtil.listToMap(weekscount, mapKey);
        //转换月数据
        Map<String, Map<String, Object>> monthMapData =  DataDisposeUtil.listToMap(monthData, mapKey);

        Map<String, Map<String, Object>> monthMapcountData = DataDisposeUtil.listToMap(monthcount, mapKey);
        //转换年数据
        Map<String, Map<String, Object>> yearMapData =  DataDisposeUtil.listToMap(yearData, mapKey);

        Map<String, Map<String, Object>> yearMapcountData = DataDisposeUtil.listToMap(yearcount, mapKey);
        //处理日周月数据 返回x轴数据和处理完后的展示数据
        Map<String, Object> map = this.wmyData(weekMapData, monthMapData, yearMapData,weekMapcountData,monthMapcountData,yearMapcountData);
        homeEChart.putAll(map);

        for (String xixa : xixas) {
            if(null==echartDataMap.get(xixa)){
                //添加累计贷款本金余额
                balanceAList.add("0.00");
                //新增贷款笔数
                addCountList.add("0");
            }else {
                Map<String, Object> dataMap = echartDataMap.get(xixa);
                //小数点往前挪动4位保留两位小数即除以10000
                BigDecimal balanceAmount = new BigDecimal(dataMap.get("balanceAmount").toString());
                BigDecimal divide = balanceAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                //添加累计贷款本金余额
                balanceAList.add(divide);
                //新增贷款笔数
                addCountList.add(dataMap.get("addCount"));
            }
        }

        HashMap<String, Object> balanceData = new HashMap<>();
//        balanceData.put("name","贷款余额");
        balanceData.put("type","bar");
        balanceData.put("data",balanceAList);

        //新增贷款笔数
        HashMap<String, Object> addCountData = new HashMap<>();
//        addCountData.put("name","新增贷款笔数");
        addCountData.put("type","bar");
        addCountData.put("data",addCountList);

        //1、添加累计贷款余额
        ArrayList<Map<String, Object>> balanceAndTotalData = new ArrayList<>();
        balanceAndTotalData.add(balanceData);

        //2、新增贷款笔数
        ArrayList<Map<String, Object>> addAmountAndTotalData = new ArrayList<>();
        addAmountAndTotalData.add(addCountData);

        homeEChart.put("eChartBalanceAmount",balanceAndTotalData);
        homeEChart.put("eChartAddCount",addAmountAndTotalData);
        homeEChart.put("homeAmountXixas",xixas);

        return homeEChart;
    }

    /**
     * 处理周月年数据
     *
     * @param weekMapData  周地图数据
     * @param monthMapData 月地图数据
     * @param yearMapData  年地图数据
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> wmyData(Map<String, Map<String, Object>> weekMapData,Map<String, Map<String, Object>> monthMapData,Map<String, Map<String, Object>> yearMapData,
        Map<String, Map<String, Object>> weekcountData,Map<String, Map<String, Object>> monthcountData,Map<String, Map<String, Object>> yearcountData
    ){
        HashMap<String, Object> returnMap = new HashMap<>();

        //获取90周的X轴
        List<String> weekXaxisData = this.getWeekendXaxisData(90);
        //获取24个月的X轴
        List<String> monthXaxisData = this.getMonthendXaxisData(24);
        //获取5年的X轴
        List<String> yearXaxisData = this.getYearendXaxisData(5);



        //获取90周的X轴
        List<String> weekXaxisDatacount = this.getWeekXaxisData(90);
        //获取24个月的X轴
        List<String> monthXaxisDatacount = this.getMonthXaxisData(24);
        //获取5年的X轴
        List<String> yearXaxisDatacount = this.getYearXaxisData(5);
        //分割周数据
        Map<String, Object> weekMap = this.dataDispose(weekXaxisData, weekMapData,weekXaxisDatacount,weekcountData);
        returnMap.put("weekBalanceAmount",weekMap.get("balanceAmount"));
        returnMap.put("weekaddCount",weekMap.get("addCount"));
        returnMap.put("weekXixas",weekXaxisDatacount);
        //分割月数据
        Map<String, Object> monthMap = this.dataDispose(monthXaxisData, monthMapData,monthXaxisDatacount,monthcountData);
        returnMap.put("monthBalanceAmount",monthMap.get("balanceAmount"));
        returnMap.put("monthaddCount",monthMap.get("addCount"));
        returnMap.put("monthXixas",monthXaxisDatacount);
        //分割年数据
        Map<String, Object> yearMap = this.dataDispose(yearXaxisData, yearMapData,yearXaxisDatacount,yearcountData);
        returnMap.put("yearBalanceAmount",yearMap.get("balanceAmount"));
        returnMap.put("yearaddCount",yearMap.get("addCount"));
        returnMap.put("yearXixas",yearXaxisDatacount);
        return returnMap;
    }

    /**
     * 分割日周月年数据
     *
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> dataDispose( List<String> xixas,Map<String, Map<String, Object>> data,List<String> countxixas,Map<String, Map<String, Object>> countdata){
        HashMap<String, Object> returnMap = new HashMap<>();
        //添加累计贷款余额
        ArrayList<Object> balanceAList = new ArrayList<>();
        //新增贷款笔数
        ArrayList<Object> addCountList = new ArrayList<>();
        for (String xixa : xixas) {
            if(null==data.get(xixa)){
                //添加累计贷款本金余额
                balanceAList.add("0.00");
            }else {
                Map<String, Object> dataMap = data.get(xixa);
                //小数点往前挪动4位保留两位小数即除以10000
                BigDecimal balanceAmount = new BigDecimal(dataMap.get("balanceAmount").toString());
                BigDecimal divide = balanceAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
                //添加累计贷款本金余额
                balanceAList.add(divide);

            }
        }

        for (String xixa : countxixas) {
            if(null==countdata.get(xixa)){
                //新增贷款笔数
                addCountList.add("0");
            }else {
                Map<String, Object> dataMap = countdata.get(xixa);
                //新增贷款笔数
                addCountList.add(dataMap.get("addCount"));
            }
        }





        HashMap<String, Object> balanceData = new HashMap<>();
//        balanceData.put("name","贷款余额");
        balanceData.put("type","bar");
        balanceData.put("data",balanceAList);

        //新增贷款笔数
        HashMap<String, Object> addCountData = new HashMap<>();
//        addCountData.put("name","新增贷款笔数");
        addCountData.put("type","bar");
        addCountData.put("data",addCountList);

        //1、添加累计贷款余额
        ArrayList<Map<String, Object>> balanceAndTotalData = new ArrayList<>();
        balanceAndTotalData.add(balanceData);

        //2、新增贷款笔数
        ArrayList<Map<String, Object>> addAmountAndTotalData = new ArrayList<>();
        addAmountAndTotalData.add(addCountData);

        returnMap.put("balanceAmount",balanceAndTotalData);
        returnMap.put("addCount",addAmountAndTotalData);
        return returnMap;
    }



    public Map<String,Object> homeVintageEChart(DData dData,String yesterday){
        //获取一个月之前的时间
        String beginTime = DateNumerationUtils.getMonthBefore(yesterday, 1);

        DVintageMonth dVintageMonth = new DVintageMonth();
        dVintageMonth.setPlatformNo(dData.getPlatformNo());
        dVintageMonth.setCustNo(dData.getCustNo());
        dVintageMonth.setPartnerNo(dData.getPartnerNo());
        dVintageMonth.setFundNo(dData.getFundNo());
        //本月-1
        dVintageMonth.setReconMonth(beginTime);
        dVintageMonth.setVintageDay(Long.parseLong("30"));

        //赋予前置查询到的项目权限
        if (dData.getProductNo() != null) {
            dVintageMonth.setProductNo(dData.getProductNo());
        }
        Map<String, Object> vintageEChartData = echartsService.getVintageEChartData(dVintageMonth);
        return vintageEChartData;
    }


    /**
     * 获得数据
     *
     * @param data 数据
     * @return {@link List}<{@link String}>
     */
    public  Map<String,List> dataDispose(List<Map<String, Object>> data){

        HashMap<String,List> slMap = new HashMap<>();
        ArrayList<String> dataList= new ArrayList<>();
        ArrayList<String> xaxisList= new ArrayList<>();
        ArrayList<Map<String,Object>> pieList= new ArrayList<>();
        BigDecimal bigDecimal = new BigDecimal("0");


        for(int i = 0;i<data.size();i++){
            HashMap<String, Object> pieMap = new HashMap<>();
            if(i<9){
                if(null==data.get(i).get("count")){
                    dataList.add("0.00");
                    pieMap.put("value","0.00");
                }else {

                    BigDecimal balanceAmount = new BigDecimal(data.get(i).get("count").toString());
                    BigDecimal divide = balanceAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);

                    dataList.add(divide.toString());
//                    pieMap.put("value",data.get(i).get("count").toString());
                    pieMap.put("value",divide.toString());
                }
                pieMap.put("name",data.get(i).get("dictName").toString());
                pieList.add(pieMap);
                xaxisList.add(data.get(i).get("dictName").toString());
            }else {
                if(null==data.get(i).get("count")){
                   bigDecimal = bigDecimal.add(new BigDecimal("0.00"));
                }else {
                    bigDecimal = bigDecimal.add(new BigDecimal(data.get(i).get("count").toString()));
                }
            }
        }
        BigDecimal balanceAmount = new BigDecimal(bigDecimal.toString());
        BigDecimal divide = balanceAmount.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP);
        if(data.size()>9){
            if(!divide.toString().equals("0")){
                dataList.add(divide.toString());
                xaxisList.add("其他");
            }


        }
        HashMap<String, Object> pieMap2 = new HashMap<>();
        if(!bigDecimal.toString().equals("0")){
            pieMap2.put("name","其他");
//            pieMap2.put("value",bigDecimal);
            pieMap2.put("value",divide);
        }

        pieList.add(pieMap2);
        slMap.put("data",dataList);
        slMap.put("xaxis",xaxisList);
        slMap.put("pie",pieList);
        return slMap;
    }

    /**
     * 周xaxis数据
     *
     * @param weeks 周
     * @return {@link List}<{@link String}>
     */
    public List<String> getWeekXaxisData(int weeks){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        weeks = weeks-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = weeks; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getWeekTimeSection(i));
        }
        return datas;
    }

    /**
     * 获取月
     *
     * @param months 个月
     * @return {@link List}<{@link String}>
     */
    public List<String> getMonthXaxisData(int months){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        months = months-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = months; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastMonth(i));
        }
        return datas;
    }

    /**
     * 获取年
     *
     * @param years 年
     * @return {@link List}<{@link String}>
     */
    public List<String> getYearXaxisData(int years){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        years = years-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = years; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastYear(i));
        }
        return datas;
    }



    /**
     * 周xaxis数据
     *
     * @param weeks 周
     * @return {@link List}<{@link String}>
     */
    public List<String> getWeekendXaxisData(int weeks){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        weeks = weeks-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = weeks; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getWeekendTimeSection(i));
        }
        return datas;
    }

    /**
     * 获取月
     *
     * @param months 个月
     * @return {@link List}<{@link String}>
     */
    public List<String> getMonthendXaxisData(int months){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        months = months-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = months; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastMonthEndDate(i));
        }
        return datas;
    }

    /**
     * 获取年
     *
     * @param years 年
     * @return {@link List}<{@link String}>
     */
    public List<String> getYearendXaxisData(int years){
        //由于不包括本周 所以得-1 然后在得到数组后再获取本周的日期放入数组即可
        years = years-1;
        ArrayList<String> datas = new ArrayList<>();
        for (int i = years; i>=0 ;i--){
            datas.add(com.ruoyi.common.utils.DateUtils.getLastYearEnd(i));
        }
        return datas;
    }
}
