package org.ruoyi.core.cwproject.domain.dto;

import com.ruoyi.common.enums.AckFlagStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 提成基数响应参数列表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProfitUnConfirmList {
    /** 项目名称ID*/
    private Long projectId;
    /** 提成基数确认表ID*/
    private Long projectAckId;
    /** 项目名称*/
    private String projectName;
    /** 担保公司*/
    private String custName;
    /** 汇款公司*/
    private String incomeCustName;
    /** 收入金额 元*/
    private BigDecimal incomeAmt;
    /** 提成返费金额 元*/
    private BigDecimal feeAmt2;
    /** 提成毛利未确认累计金额*/
    private BigDecimal aggregateAnmtmt;
    /** 所属部门*/
    private Integer userFlag;
    /** 会计 */
    private List<String> accountant;
    /** 出纳 */
    private List<String> cashier;
    /** 业务 */
    private List<String> salesman;
    /** 状态 */
    private AckFlagStatus ackFlagStatus;

    /** 确认时间 */
    private String ackDate;

    /** 项目类型 */
    private String projectType;

    /** OA项目名称*/
    private Long oaProjectDeployId;
}
