package org.ruoyi.core.cwproject.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.ruoyi.core.cwproject.domain.vo.ProjectAckDetailsVo;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProfitUnConfirmDetailsList {
    /** 提成基数详情 */
    private List<ProjectAckDetailsVo> projectAckDetailsVos;
    /** 项目名称*/
    private String projectName;
    /** 担保公司*/
    private String custName;
    /** 汇款公司*/
    private String incomeCustName;
    /** 会计 */
    private List<String> accountant;
    /** 出纳 */
    private List<String> cashier;
    /** 业务 */
    private List<String> salesman;
    /** 合计 */
    /** 未确认 */
    private AmountTotalListDto totalListDto;

    //项目类型
    private String projectType;
}
