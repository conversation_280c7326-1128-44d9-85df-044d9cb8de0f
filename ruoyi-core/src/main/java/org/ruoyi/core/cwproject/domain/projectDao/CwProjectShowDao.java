package org.ruoyi.core.cwproject.domain.projectDao;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.ruoyi.core.cwproject.domain.CwProjectCust;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CwProjectShowDao  extends BaseEntity {
  /** 项目id */
  @Excel(name = "项目id")
  private Long projectId;

  /** 项目名称 */
  @Excel(name = "项目名称")
  private String projectName;

  /** 担保公司 */
  @Excel(name = "担保公司")
  private String custName;

  /** 汇款公司 */
  @Excel(name = "汇款公司")
  private String incomeCustName;

  @Excel(name = "返费表")
  private List<CwProjectCust> cwProjectCusts;

//  /** 返费公司名称 */
//  @Excel(name = "返费公司名称")
//  private String custNameFan;
//
//  /** 费率 % */
//  @Excel(name = "费率 %")
//  private BigDecimal rate;
//
//  /** 税率 % */
//  @Excel(name = "税率 %")
//  private BigDecimal taxRate;

  /** 会计 % */
  @Excel(name = "会计")
  private List<Long> accountants;

  /** 出纳 % */
  @Excel(name = "出纳")
  private List<Long> cashiers;

  /** 业务 % */
  @Excel(name = "业务")
  private List<Long> salesmans;
}
