package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo;
import org.ruoyi.core.oasystem.domain.OaTrader;
import org.ruoyi.core.oasystem.domain.bo.UpdateNewOaTraderBo;
import org.ruoyi.core.oasystem.domain.bo.OaTraderBo;
import org.ruoyi.core.oasystem.domain.vo.OaTraderVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaTraderService.java
 * @Description TODO
 * @createTime 2023年06月28日 09:53:00
 */
public interface IOaTraderService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaTraderBo selectOaTraderById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaTrader 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<OaTrader> selectOaTraderList(OaTrader oaTrader);

    //根据查询条件对列表进行过滤，上面的是不支持过滤的
    public List<OaTraderVo> selectOaTraderListByParam(OaTrader oaTrader, String selectType, List<Long> companyIdList, List<Long> filterOaApplyId);

    /**
     * 新增【请填写功能名称】
     *
     * @param oaTrader 【请填写功能名称】
     * @return 结果
     */
    public int insertOaTrader(OaTrader oaTrader, LoginUser loginUser);
    /**
     * 修改【请填写功能名称】
     *
     * @param oaTrader 【请填写功能名称】
     * @return 结果
     */
    public int updateOaTrader(OaTrader oaTrader, LoginUser loginUser);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */

    public int deleteOaTraderByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaTraderById(Long id);

    int updateEnable(OaTrader user,LoginUser loginUser);

    Map<String, Object> checkOaTrader(OaTrader oaTrader);

    List<Map<String, Object>> getDataByParams(OaTrader oaTrader);

    //查询总条数
    Long selectOaTraderListTotal(OaTrader oaTrader, List<Long> companyIdList);

    //根据过滤条件查询总条数
    Long selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(OaTrader oaTrader, List<Long> companyIdList, List<Long> filterOaApplyId);

    List<OaTraderVo> selectOaTraderListNew(OaTrader oaTrader, List<Long> companyIdList, String isEnable);

    List<AccountSetsInfoPo> selectAccountInfo(List<Long> companyIdList, String queryAllFlag);

    int insertOaTraderNew(OaTrader oaTrader, LoginUser loginUser);

    int updateOaTraderNew(UpdateNewOaTraderBo updateNewOaTraderBo, LoginUser loginUser);
}
