package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.OaProcessClassification;
import org.ruoyi.core.oasystem.domain.TreeSelectData;
import org.ruoyi.core.oasystem.domain.vo.CopyProcessVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaProcessClassificationService.java
 * @Description TODO
 * @createTime 2023年08月07日 11:25:00
 */
public interface IOaProcessClassificationService {
    /**
     * 查询OA系统-流程分类
     *
     * @param id OA系统-流程分类主键
     * @return OA系统-流程分类
     */
    public OaProcessClassification selectOaProcessClassificationById(Long id);
    /**
     * 查询OA系统-流程分类列表
     *
     * @param oaProcessClassification OA系统-流程分类
     * @return OA系统-流程分类
     */
    public List<OaProcessClassification> selectOaProcessClassificationList(OaProcessClassification oaProcessClassification);

    /**
     * 新增OA系统-流程分类
     *
     * @param oaProcessClassification OA系统-流程分类
     * @return 结果
     */
    public int insertOaProcessClassification(OaProcessClassification oaProcessClassification);

    /**
     * 修改OA系统-流程分类
     *
     * @param oaProcessClassification OA系统-流程分类
     * @return 结果
     */
    public int updateOaProcessClassification(OaProcessClassification oaProcessClassification);

    /**
     * 批量删除OA系统-流程分类
     *
     * @param ids 需要删除的OA系统-流程分类主键
     * @return 结果
     */
    public int deleteOaProcessClassificationByIds(Long[] ids);
    /**
     * 删除OA系统-流程分类信息
     *
     * @param id OA系统-流程分类主键
     * @return 结果
     */
    public int deleteOaProcessClassificationById(Long id);

    String checkDeptNameUnique(OaProcessClassification oaProcessClassification);

    int selectNormalChildrenDeptById(Long id);

    boolean hasChildByDeptId(Long ids);

    boolean checkDeptExistUser(Long ids);

    List<TreeSelectData> buildTreeSelect(List<OaProcessClassification> oaProcessClassifications);


    public List<OaProcessClassification> getRoleData(LoginUser loginUser, OaProcessClassification oaProcessClassification);

    /**
     * <AUTHOR>
     * @Description 复制模板
     * @Date 2023/10/10 9:31
     * @Param [vo]
     * @return void
     **/
    void copyProcess(CopyProcessVo vo);

    List<OaProcessClassification> selectOaProcessClassificationListFroNewAuthority(OaProcessClassification oaProcessClassification, Long userId);
}
