package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesMain;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface OaVoucherRulesMainMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaVoucherRulesMain selectOaVoucherRulesMainById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaVoucherRulesMain 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<OaVoucherRulesMain> selectOaVoucherRulesMainList(@Param("oaVoucherRulesMain") OaVoucherRulesMain oaVoucherRulesMain, @Param("companyIdList") List<Long> companyIdList);

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaVoucherRulesMain 【请填写功能名称】
     * @return 结果
     */
    public int insertOaVoucherRulesMain(OaVoucherRulesMain oaVoucherRulesMain);

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaVoucherRulesMain 【请填写功能名称】
     * @return 结果
     */
    public int updateOaVoucherRulesMain(OaVoucherRulesMain oaVoucherRulesMain);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaVoucherRulesMainById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaVoucherRulesMainByIds(Long[] ids);

    Long selectDataTotal(OaVoucherRulesMain oaVoucherRulesMain);

    //加入新的查询条件，公司id，过滤后的主表id
    List<OaVoucherRulesMain> selectOaVoucherRulesMainListByOaVoucherRulesMainAndCompanyIdList(@Param("oaVoucherRulesMain") OaVoucherRulesMain oaVoucherRulesMain, @Param("companyIdList") List<Long> companyIdList, @Param("filterOaVoucherRulesMainId") List<Long> filterOaVoucherRulesMainId);

    //根据id找主表信息
    OaVoucherRulesMain selectOaVoucherRulesMainById1(Long oaVoucherRulesMainId);

    //查找记账凭证规则主表总条数
    Long selectOaVoucherRulesMainListTotal(@Param("oaVoucherRulesMain") OaVoucherRulesMain oaVoucherRulesMain, @Param("companyIdList") List<Long> companyIdList);

    //查找记账凭证规则主表总条数，加入新的查询条件。主要是查询我的审批和我的提交视图的信息
    Long selectOaVoucherRulesMainListTotalByOaVoucherRulesMainAndCompanyIdList(@Param("oaVoucherRulesMain") OaVoucherRulesMain oaVoucherRulesMain, @Param("companyIdList") List<Long> companyIdList, @Param("filterOaVoucherRulesMainId") List<Long> filterOaVoucherRulesMainId);
}
