package org.ruoyi.core.yqzl.domain.util;

import lombok.Data;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.StringReader;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用XML报文解析工具类
 * 支持将XML报文自动转换为实体类对象
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public class XmlMessageParser {

    /**
     * 解析XML报文为单个实体对象
     *
     * @param xmlContent XML报文内容
     * @param clazz 目标实体类
     * @param <T> 实体类类型
     * @return 解析后的实体对象
     * @throws Exception 解析异常
     */
    public static <T> T parseXmlToEntity(String xmlContent, Class<T> clazz) throws Exception {
        SAXReader saxReader = new SAXReader();
        Document document = saxReader.read(new StringReader(xmlContent));
        Element rootElement = document.getRootElement();

        // 创建实体对象实例
        T entity = clazz.newInstance();

        // 解析根元素下的直接子元素
        parseElementToEntity(rootElement, entity);

        return entity;
    }

    /**
     * 解析XML报文为实体对象列表（适用于包含list结构的XML）
     *
     * @param xmlContent XML报文内容
     * @param clazz 目标实体类
     * @param <T> 实体类类型
     * @return 解析后的实体对象列表
     * @throws Exception 解析异常
     */
    public static <T> List<T> parseXmlToEntityList(String xmlContent, Class<T> clazz) throws Exception {
        SAXReader saxReader = new SAXReader();
        Document document = saxReader.read(new StringReader(xmlContent));
        Element rootElement = document.getRootElement();

        List<T> entityList = new ArrayList<>();

        // 查找list元素
        Element listElement = rootElement.element("list");
        if (listElement != null) {
            // 解析每个row元素
            List<Element> rowElements = listElement.elements("row");
            for (Element rowElement : rowElements) {
                T entity = clazz.newInstance();
                parseElementToEntity(rowElement, entity);
                entityList.add(entity);
            }
        } else {
            // 如果没有list结构，尝试解析根元素
            T entity = clazz.newInstance();
            parseElementToEntity(rootElement, entity);
            entityList.add(entity);
        }

        return entityList;
    }

    /**
     * 解析XML报文的响应结构（包含action、userName等信息）
     *
     * @param xmlContent XML报文内容
     * @param clazz 目标实体类
     * @param <T> 实体类类型
     * @return 包含解析结果的响应对象
     * @throws Exception 解析异常
     */
    public static <T> XmlParseResult<T> parseXmlResponse(String xmlContent, Class<T> clazz) throws Exception {
        SAXReader saxReader = new SAXReader();
        Document document = saxReader.read(new StringReader(xmlContent));
        Element rootElement = document.getRootElement();

        XmlParseResult<T> result = new XmlParseResult<>();

        // 解析基本信息
        Element actionElement = rootElement.element("action");
        if (actionElement != null) {
            result.setAction(actionElement.getTextTrim());
        }

        Element userNameElement = rootElement.element("userName");
        if (userNameElement != null) {
            result.setUserName(userNameElement.getTextTrim());
        }

        Element statusElement = rootElement.element("status");
        if (statusElement != null) {
            result.setStatus(statusElement.getTextTrim());
        }

        Element statusTextElement = rootElement.element("statusText");
        if (statusTextElement != null) {
            result.setStatusText(statusTextElement.getTextTrim());
        }

        Element accountNoElement = rootElement.element("accountNo");
        if (accountNoElement != null) {
            result.setAccountNo(accountNoElement.getTextTrim());
        }

        Element accountNameElement = rootElement.element("accountName");
        if (accountNameElement != null) {
            result.setAccountName(accountNameElement.getTextTrim());
        }

        Element openBankNameElement = rootElement.element("openBankName");
        if (openBankNameElement != null) {
            result.setOpenBankName(openBankNameElement.getTextTrim());
        }

        Element totalRecordsElement = rootElement.element("totalRecords");
        if (totalRecordsElement != null) {
            result.setTotalRecords(Integer.valueOf(totalRecordsElement.getTextTrim()));
        }

        Element returnRecordsElement = rootElement.element("returnRecords");
        if (returnRecordsElement != null) {
            result.setReturnRecords(Integer.valueOf(returnRecordsElement.getTextTrim()));
        }

        Element totalCountElement = rootElement.element("totalCount");
        if (totalCountElement != null) {
            result.setTotalCount(Integer.valueOf(totalCountElement.getTextTrim()));
        }

        // 解析数据内容
        Element listElement = rootElement.element("list");
        if (listElement != null) {
            // 解析列表数据
            List<T> dataList = new ArrayList<>();
            List<Element> rowElements = listElement.elements("row");
            for (Element rowElement : rowElements) {
                T entity = clazz.newInstance();
                parseElementToEntity(rowElement, entity);
                dataList.add(entity);
            }
            result.setDataList(dataList);
        } else {
            // 检查是否有单个row元素
            Element rowElement = rootElement.element("row");
            if (rowElement != null) {
                // 如果有单个row元素，将其放入dataList中
                List<T> dataList = new ArrayList<>();
                T entity = clazz.newInstance();
                parseElementToEntity(rowElement, entity);
                dataList.add(entity);
                result.setDataList(dataList);
            } else {
                // 解析单个对象数据
                T entity = clazz.newInstance();
                parseElementToEntity(rootElement, entity);
                result.setSingleData(entity);
            }
        }

        return result;
    }

    /**
     * 将XML元素解析为实体对象
     *
     * @param element XML元素
     * @param entity 目标实体对象
     * @param <T> 实体类类型
     * @throws Exception 解析异常
     */
    private static <T> void parseElementToEntity(Element element, T entity) throws Exception {
        Class<?> clazz = entity.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            String xmlElementName = getXmlElementName(fieldName);

            // 查找对应的XML元素
            Element fieldElement = element.element(xmlElementName);
            if (fieldElement != null) {
                String value = fieldElement.getTextTrim();
                if (value != null && !value.isEmpty()) {
                    // 根据字段类型进行转换
                    Object convertedValue = convertValue(value, field.getType());
                    field.set(entity, convertedValue);
                }
            }
        }
    }

    /**
     * 获取字段对应的XML元素名
     *
     * @param fieldName Java字段名
     * @return XML元素名
     */
    private static String getXmlElementName(String fieldName) {
        // 处理特殊字段名映射（与XmlMessageGenerator保持一致）
        if ("abstractField".equals(fieldName)) {
            return "abstract";
        }
        return fieldName;
    }

    /**
     * 根据目标类型转换字符串值
     *
     * @param value 字符串值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    private static Object convertValue(String value, Class<?> targetType) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        value = value.trim();

        try {
            if (targetType == String.class) {
                return unescapeXml(value);
            } else if (targetType == Integer.class || targetType == int.class) {
                return Integer.valueOf(value);
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.valueOf(value);
            } else if (targetType == BigDecimal.class) {
                return new BigDecimal(value);
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.valueOf(value);
            } else if (targetType == Float.class || targetType == float.class) {
                return Float.valueOf(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.valueOf(value);
            } else {
                // 默认返回字符串
                return unescapeXml(value);
            }
        } catch (NumberFormatException e) {
            throw new RuntimeException("无法将值 '" + value + "' 转换为类型 " + targetType.getSimpleName(), e);
        }
    }

    /**
     * XML特殊字符反转义
     *
     * @param text 转义后的文本
     * @return 原始文本
     */
    private static String unescapeXml(String text) {
        if (text == null) {
            return null;
        }
        return text.replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
    }

    /**
     * 验证XML格式是否正确
     *
     * @param xmlContent XML内容
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidXml(String xmlContent) {
        try {
            SAXReader saxReader = new SAXReader();
            saxReader.read(new StringReader(xmlContent));
            return true;
        } catch (DocumentException e) {
            return false;
        }
    }

    /**
     * XML解析结果包装类
     *
     * @param <T> 数据类型
     */
    @Data
    public static class XmlParseResult<T> {
        private String action;
        private String userName;
        private String status;
        private String statusText;
        private List<T> dataList;
        private T singleData;
        /**
         * 账户
         */
        private String accountNo;
        /**
         * 账户名称
         */
        private String accountName;
        /**
         * 开户行名称
         */
        private String openBankName;
        /**
         * 总记录数
         */
        private Integer totalRecords;
        /**
         * 返回记录数
         */
        private Integer returnRecords;

        /**
         *总记录数
         * @return
         */
        private Integer totalCount;
    }
}
