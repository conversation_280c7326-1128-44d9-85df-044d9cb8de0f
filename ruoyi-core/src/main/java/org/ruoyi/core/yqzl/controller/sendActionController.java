package org.ruoyi.core.yqzl.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.ruoyi.core.xmglproject.domain.*;
import org.ruoyi.core.xmglproject.service.IXmglProjectService;
import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.service.ICiticAccountBalanceRowService;
import org.ruoyi.core.yqzl.service.ICiticAccountTransactionService;
import org.ruoyi.core.yqzl.service.ICiticReceiptDetailService;
import org.ruoyi.core.yqzl.service.ICiticTransferOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * 项目立项管理Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/sendAction")
public class sendActionController extends BaseController
{
    @Autowired
    private ICiticAccountBalanceRowService citicAccountBalanceRowService;
    @Autowired
    private ICiticAccountTransactionService citicAccountTransactionService;
    @Autowired
    private ICiticReceiptDetailService citicReceiptDetailService;
    @Autowired
    private CiticXml citicXml;

    @PostMapping("/sendActionOfOA")
    public String queryReceiptHandler(@RequestBody String reqContent) throws Exception {
        // 使用GBK编码发送XML请求，因为XML声明中指定了encoding="GBK"
        String res  = HttpUtils.sendPost("http://*************:22001/open/sendActionOfOA", reqContent, Constants.GBK);
        SAXReader saxReader = new SAXReader();
        Document document = saxReader.read(new StringReader(res));
        Element rootElement = document.getRootElement();
        if (!"AAAAAAA".equals(rootElement.element("status").getText())) {
            // SDK接口报错逻辑...
            throw new ServiceException(res);
        }
        String responseContent = rootElement.element("responseContent").getText();

        String s = new String(Base64.getDecoder().decode(responseContent), "GBK");
        XmlMessageParser.XmlParseResult<CiticAccountBalanceRow> citicAccountBalanceRowXmlParseResult = XmlMessageParser.parseXmlResponse(s, CiticAccountBalanceRow.class);
        if (!"AAAAAAA".equals(citicAccountBalanceRowXmlParseResult.getStatus())) {
            logger.info("[{}] 中信银行返回错误：{}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), citicAccountBalanceRowXmlParseResult.getStatus()+ ":"+ citicAccountBalanceRowXmlParseResult.getStatusText());
        }
        return s;

//        citicAccountBalanceRowService.GenerationDailyAccountBalanceRow();
//        citicAccountTransactionService.GenerationDailyAccountTransaction();
//        citicReceiptDetailService.dowloadReceiptDetail();
//
//        String responseXMl = citicXml.responseXMl("DLEDCDTD", reqContent);
//        return  "responseXMl";
    }
}
