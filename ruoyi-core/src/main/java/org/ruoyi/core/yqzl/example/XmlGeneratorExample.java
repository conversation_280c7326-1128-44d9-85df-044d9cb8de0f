package org.ruoyi.core.yqzl.example;

import lombok.Data;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponse;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponseRow;
import org.ruoyi.core.yqzl.domain.util.XmlMessageGenerator;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * XML生成器使用示例
 * 展示通用的row字段处理功能
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class XmlGeneratorExample {

    /**
     * 示例1：处理包含单个row对象的响应类
     */
    public static void testSingleRowResponse() {
        // 创建响应对象
        TransferOrderResponse response = new TransferOrderResponse();
        response.setStatus("SUCCESS");
        response.setStatusText("交易成功");
        response.setSucTotalNum(1);
        response.setSucTotalAmt(new BigDecimal("1000.00"));
        response.setErrTotalNum(0);
        response.setErrTotalAmt(new BigDecimal("0.00"));

        // 创建row对象
        TransferOrderResponseRow row = new TransferOrderResponseRow();
        row.setClientID("12345678901234567890");
        row.setStatus("SUCCESS");
        row.setStatusText("转账成功");
        response.setRow(row);

        // 生成XML
        String xml = XmlMessageGenerator.generateXmlMessageWithRowList("DLINTTRN", "testUser", response);
        System.out.println("单个row对象XML:");
        System.out.println(xml);
    }

    /**
     * 示例2：处理包含List的响应类
     */
    @Data
    public static class BatchResponse {
        private String status;
        private String statusText;
        private List<TransferOrderResponseRow> dataList;
    }

    public static void testListRowResponse() {
        // 创建批量响应对象
        BatchResponse response = new BatchResponse();
        response.setStatus("SUCCESS");
        response.setStatusText("批量处理成功");

        // 创建数据列表
        List<TransferOrderResponseRow> dataList = new ArrayList<>();
        
        TransferOrderResponseRow row1 = new TransferOrderResponseRow();
        row1.setClientID("11111111111111111111");
        row1.setStatus("SUCCESS");
        row1.setStatusText("第一笔转账成功");
        dataList.add(row1);

        TransferOrderResponseRow row2 = new TransferOrderResponseRow();
        row2.setClientID("22222222222222222222");
        row2.setStatus("FAILURE");
        row2.setStatusText("第二笔转账失败");
        dataList.add(row2);

        response.setDataList(dataList);

        // 生成XML
        String xml = XmlMessageGenerator.generateXmlMessageWithRowList("DLBATCH", "testUser", response);
        System.out.println("List对象XML:");
        System.out.println(xml);
    }

    /**
     * 示例3：处理包含多种row字段的复杂响应类
     */
    @Data
    public static class ComplexResponse {
        private String action;
        private String userName;
        private String status;
        private TransferOrderResponseRow mainRow;
        private List<TransferOrderResponseRow> detailRows;
    }

    public static void testComplexRowResponse() {
        ComplexResponse response = new ComplexResponse();
        response.setAction("COMPLEX");
        response.setUserName("complexUser");
        response.setStatus("SUCCESS");

        // 主要row对象
        TransferOrderResponseRow mainRow = new TransferOrderResponseRow();
        mainRow.setClientID("MAIN000000000000000001");
        mainRow.setStatus("SUCCESS");
        mainRow.setStatusText("主要交易成功");
        response.setMainRow(mainRow);

        // 详细row列表
        List<TransferOrderResponseRow> detailRows = new ArrayList<>();
        
        TransferOrderResponseRow detail1 = new TransferOrderResponseRow();
        detail1.setClientID("DETAIL0000000000000001");
        detail1.setStatus("SUCCESS");
        detail1.setStatusText("详细交易1成功");
        detailRows.add(detail1);

        TransferOrderResponseRow detail2 = new TransferOrderResponseRow();
        detail2.setClientID("DETAIL0000000000000002");
        detail2.setStatus("SUCCESS");
        detail2.setStatusText("详细交易2成功");
        detailRows.add(detail2);

        response.setDetailRows(detailRows);

        // 生成XML
        String xml = XmlMessageGenerator.generateXmlMessageWithRowList("COMPLEX", "testUser", response);
        System.out.println("复杂row对象XML:");
        System.out.println(xml);
    }

    public static void main(String[] args) {
        System.out.println("=== XML生成器通用row处理示例 ===\n");
        
        testSingleRowResponse();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testListRowResponse();
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        testComplexRowResponse();
    }
}
