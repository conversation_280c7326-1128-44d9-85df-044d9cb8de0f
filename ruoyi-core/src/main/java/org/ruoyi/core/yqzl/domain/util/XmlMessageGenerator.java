package org.ruoyi.core.yqzl.domain.util;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;

/**
 * 通用XML报文生成工具类
 * 支持自动生成银行交易相关的XML报文
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public class XmlMessageGenerator {

    /**
     * 生成转账请求XML报文（支持任意实体类）
     *
     * @param action 交易动作代码
     * @param userName 登录用户名
     * @param dataList 任意实体类数据列表
     * @param <T> 实体类类型
     * @return 完整的XML报文字符串
     */
    public static <T> String generateXmlRequestList(String action, String userName, List<T> dataList) {
        StringBuilder xml = new StringBuilder();

        // XML声明和根元素开始
        xml.append("<?xml version=\"1.0\" encoding=\"GBK\"?>\n");
        xml.append("<stream>\n");
        xml.append("<action>").append(escapeXml(action)).append("</action>\n");
        xml.append("<userName>").append(escapeXml(userName)).append("</userName>\n");
        xml.append("<list name=\"userDataList\">\n");

        // 生成每个row数据
        if (dataList != null && !dataList.isEmpty()) {
            for (T entity : dataList) {
                xml.append(generateRowXml(entity));
            }
        }

        // 结束标签
        xml.append("</list>\n");
        xml.append("</stream>");

        return xml.toString();
    }

    /**
     * 生成单个row的XML内容（支持任意实体类）
     *
     * @param entity 任意实体对象
     * @param <T> 实体类类型
     * @return row的XML字符串
     */
    private static <T> String generateRowXml(T entity) {
        StringBuilder rowXml = new StringBuilder();
        rowXml.append("<row>\n");

        // 使用反射获取所有字段并生成XML
        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(entity);
                String fieldName = getXmlFieldName(field.getName());
//                String comment = getFieldComment(field.getName());

                rowXml.append("<").append(fieldName).append(">");
                if (value != null) {
                    rowXml.append(escapeXml(value.toString()));
                }
                rowXml.append("</").append(fieldName).append(">");
//                if (comment != null && !comment.isEmpty()) {
//                    rowXml.append("<!--").append(comment).append("-->");
//                }
                rowXml.append("\n");
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("生成XML时发生错误", e);
        }

        rowXml.append("</row>\n");
        return rowXml.toString();
    }

    public static <T> String generateXmlRequest(String action, String userName, T data) {
        StringBuilder xml = new StringBuilder();

        // XML声明和根元素开始
        xml.append("<?xml version=\"1.0\" encoding=\"GBK\"?>\n");
        xml.append("<stream>\n");
        xml.append("<action>").append(escapeXml(action)).append("</action>\n");
        xml.append("<userName>").append(escapeXml(userName)).append("</userName>\n");
        // 生成每个row数据
        xml.append(generateXml(data));
        // 结束标签
        xml.append("</stream>");
        return xml.toString();
    }

    private static <T> String generateXml(T entity) {
        StringBuilder rowXml = new StringBuilder();

        // 使用反射获取所有字段并生成XML
        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(entity);
                String fieldName = getXmlFieldName(field.getName());
                rowXml.append("<").append(fieldName).append(">");
                if (value != null) {
                    rowXml.append(escapeXml(value.toString()));
                }
                rowXml.append("</").append(fieldName).append(">");
                rowXml.append("\n");
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("生成XML时发生错误", e);
        }

        return rowXml.toString();
    }

    /**
     * 获取字段对应的XML标签名
     *
     * @param fieldName Java字段名
     * @return XML标签名
     */
    private static String getXmlFieldName(String fieldName) {
        // 处理特殊字段名映射
        if ("abstractField".equals(fieldName)) {
            return "abstract";
        }
        return fieldName;
    }


    /**
     * 生成通用XML报文（支持任意实体类，使用默认注释）
     *
     * @param action 交易动作代码
     * @param userName 登录用户名
     * @param dataList 任意实体类数据列表
     * @param <T> 实体类类型
     * @return 完整的XML报文字符串
     */
    public static <T> String generateXmlMessageList(String action, String userName, List<T> dataList) {
        return generateXmlRequestList(action, userName, dataList);
    }

    public static <T> String generateXmlMessage(String action, String userName, T data) {
        return generateXmlRequest(action, userName, data);
    }

    /**
     * 生成带有rowList的复杂XML报文（专门用于CiticReceiptDownLoadQuery等复杂结构）
     * 将对象的普通字段放在userName标签下，rowList内容套在row标签里
     *
     * @param action 交易动作代码
     * @param userName 登录用户名
     * @param data 包含rowList字段的实体对象
     * @param <T> 实体类类型
     * @return 完整的XML报文字符串
     */
    public static <T> String generateXmlMessageWithRowList(String action, String userName, T data) {
        StringBuilder xml = new StringBuilder();

        // XML声明和根元素开始
        xml.append("<?xml version=\"1.0\" encoding=\"GBK\"?>\n");
        xml.append("<stream>\n");
        xml.append("<action>").append(escapeXml(action)).append("</action>\n");
        xml.append("<userName>").append(escapeXml(userName)).append("</userName>\n");

        // 生成普通字段（排除rowList字段）
        xml.append(generateMainFieldsXml(data));

        // 生成rowList内容
        xml.append(generateRowListXml(data));

        // 结束标签
        xml.append("</stream>");
        return xml.toString();
    }

    /**
     * 生成主要字段的XML（排除row字段）
     *
     * @param entity 实体对象
     * @param <T> 实体类类型
     * @return 主要字段的XML字符串（不包含空值字段）
     */
    private static <T> String generateMainFieldsXml(T entity) {
        StringBuilder xml = new StringBuilder();

        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();

                // 跳过row相关字段和List类型字段
                if ("row".equals(fieldName) || fieldName.toLowerCase().contains("row") ||
                    List.class.isAssignableFrom(field.getType())) {
                    continue;
                }

                Object value = field.get(entity);

                // 检查字段值是否为null
                if (value == null) {
                    continue;
                }

                // 如果是String类型，检查是否为空字符串或只包含空白字符
                if (value instanceof String) {
                    String stringValue = (String) value;
                    if (stringValue.trim().isEmpty()) {
                        continue;
                    }
                }

                // 只有当字段值不为null且不为空时，才添加XML标签
                String xmlFieldName = getXmlFieldName(fieldName);
                xml.append("<").append(xmlFieldName).append(">");
                xml.append(escapeXml(value.toString()));
                xml.append("</").append(xmlFieldName).append(">\n");
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("生成主要字段XML时发生错误", e);
        }

        return xml.toString();
    }

    /**
     * 生成rowList的XML内容（通用版本）
     * 支持以下情况：
     * 1. 如果字段名为"row"且不是List类型，将该对象作为单个row处理
     * 2. 如果字段是List类型，将List中的每个元素作为row处理
     * 3. 如果字段名包含"row"或"list"关键字，也会被处理
     *
     * @param entity 实体对象
     * @param <T> 实体类类型
     * @return rowList的XML字符串
     */
    @SuppressWarnings("unchecked")
    private static <T> String generateRowListXml(T entity) {
        StringBuilder xml = new StringBuilder();
        boolean hasDataList = false;

        try {
            Field[] fields = entity.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object value = field.get(entity);

                // 跳过null值
                if (value == null) {
                    continue;
                }

                // 情况1：处理List类型字段
                if (List.class.isAssignableFrom(field.getType())) {
                    List<?> rowList = (List<?>) value;
                    if (!rowList.isEmpty()) {
                        if (!hasDataList) {
                            xml.append("<list name=\"userDataList\">\n");
                            hasDataList = true;
                        }

                        for (Object rowItem : rowList) {
                            xml.append(generateRowXml(rowItem));
                        }
                    }
                }
                // 情况2：处理名为"row"的单个对象字段
                else if ("row".equals(fieldName) || fieldName.toLowerCase().contains("row")) {
                    if (!hasDataList) {
                        xml.append("<list name=\"userDataList\">\n");
                        hasDataList = true;
                    }
                    xml.append(generateRowXml(value));
                }
            }

            // 如果有数据列表，则关闭list标签
            if (hasDataList) {
                xml.append("</list>\n");
            }

        } catch (IllegalAccessException e) {
            throw new RuntimeException("生成rowList XML时发生错误", e);
        }

        return xml.toString();
    }

    /**
     * XML特殊字符转义
     *
     * @param text 原始文本
     * @return 转义后的文本
     */
    private static String escapeXml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&apos;");
    }


    /**
     * 通用实体验证方法（使用反射检查必填字段）
     *
     * @param entity 任意实体对象
     * @param requiredFields 必填字段名称数组
     * @param <T> 实体类类型
     * @return 验证结果消息，null表示验证通过
     */
    public static <T> String validateEntity(T entity, String... requiredFields) {
        if (entity == null) {
            return "实体对象不能为空";
        }

        try {
            Class<?> clazz = entity.getClass();
            for (String fieldName : requiredFields) {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(entity);

                if (value == null) {
                    return "字段 " + fieldName + " 不能为空";
                }

                if (value instanceof String && ((String) value).trim().isEmpty()) {
                    return "字段 " + fieldName + " 不能为空字符串";
                }

                if (value instanceof BigDecimal && ((BigDecimal) value).compareTo(BigDecimal.ZERO) <= 0) {
                    return "字段 " + fieldName + " 必须大于0";
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return "验证过程中发生错误: " + e.getMessage();
        }

        return null; // 验证通过
    }
}
