package org.ruoyi.core.yqzl.service.impl;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import org.dom4j.DocumentException;
import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;
import org.ruoyi.core.yqzl.domain.CiticTransferOrder;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponse;
import org.ruoyi.core.yqzl.domain.rep.TransferOrderResponseRow;
import org.ruoyi.core.yqzl.domain.util.CiticXml;
import org.ruoyi.core.yqzl.domain.util.XmlMessageParser;
import org.ruoyi.core.yqzl.domain.vo.CiticTransferOrderVo;
import org.ruoyi.core.yqzl.mapper.CiticTransferOrderMapper;
import org.ruoyi.core.yqzl.service.ICiticTransferOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 中信银行支付转账Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class CiticTransferOrderServiceImpl implements ICiticTransferOrderService
{
    @Autowired
    private CiticTransferOrderMapper citicTransferOrderMapper;
    @Autowired
    private CiticXml citicXml;

    /**
     * 查询中信银行支付转账
     *
     * @param id 中信银行支付转账主键
     * @return 中信银行支付转账
     */
    @Override
    public CiticTransferOrder selectCiticTransferOrderById(Long id)
    {
        return citicTransferOrderMapper.selectCiticTransferOrderById(id);
    }

    /**
     * 查询中信银行支付转账列表
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 中信银行支付转账
     */
    @Override
    public List<CiticTransferOrder> selectCiticTransferOrderList(CiticTransferOrderVo citicTransferOrder)
    {
        return citicTransferOrderMapper.selectCiticTransferOrderList(citicTransferOrder);
    }

    /**
     * 新增中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    @Override
    public int insertCiticTransferOrder(CiticTransferOrder citicTransferOrder) {
        citicTransferOrder.setClientID(String.valueOf(System.currentTimeMillis()));
        return citicTransferOrderMapper.insertCiticTransferOrder(citicTransferOrder);
    }

    /**
     * 修改中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    @Override
    public int updateCiticTransferOrder(CiticTransferOrder citicTransferOrder)
    {
        return citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
    }

    /**
     * 批量删除中信银行支付转账
     *
     * @param ids 需要删除的中信银行支付转账主键
     * @return 结果
     */
    @Override
    public int deleteCiticTransferOrderByIds(Long[] ids)
    {
        return citicTransferOrderMapper.deleteCiticTransferOrderByIds(ids);
    }

    /**
     * 删除中信银行支付转账信息
     *
     * @param id 中信银行支付转账主键
     * @return 结果
     */
    @Override
    public int deleteCiticTransferOrderById(Long id)
    {
        return citicTransferOrderMapper.deleteCiticTransferOrderById(id);
    }

    @Override
    public int passTransferOrderById(Long id) throws Exception {
        CiticTransferOrder citicTransferOrder = selectCiticTransferOrderById(id);
        initiateTransfer(citicTransferOrder);
        return citicTransferOrderMapper.updateCiticTransferOrder(citicTransferOrder);
    }

    @Override
    public void initiateTransfer(CiticTransferOrder citicTransferOrder) throws Exception {
        List<CiticTransferOrder> citicTransferOrders = new ArrayList<>();
        citicTransferOrders.add(citicTransferOrder);
        String responseXMl = citicXml.responseXMlList("DLINTTRN", citicTransferOrders);
        XmlMessageParser.XmlParseResult<TransferOrderResponse> transferOrderResponse = XmlMessageParser.parseXmlResponse(responseXMl, TransferOrderResponse.class);
        System.out.println(transferOrderResponse);

        // 获取解析后的TransferOrderResponse对象
        TransferOrderResponse response = transferOrderResponse.getSingleData();
        if (response != null && response.getRow() != null) {
            System.out.println("解析到的row列表大小: " + response.getRow().size());
            for (TransferOrderResponseRow row : response.getRow()) {
                System.out.println("Row - ClientID: " + row.getClientID() + ", Status: " + row.getStatus());
            }
        }
    }
}
