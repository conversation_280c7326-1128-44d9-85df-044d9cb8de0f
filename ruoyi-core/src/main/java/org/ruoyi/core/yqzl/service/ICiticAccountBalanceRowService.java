package org.ruoyi.core.yqzl.service;

import org.ruoyi.core.yqzl.domain.CiticAccountBalanceRow;

import java.util.List;

/**
 * 中信银行账户余额查询结果Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ICiticAccountBalanceRowService
{
    /**
     * 查询中信银行账户余额查询结果
     *
     * @param id 中信银行账户余额查询结果主键
     * @return 中信银行账户余额查询结果
     */
    public CiticAccountBalanceRow selectCiticAccountBalanceRowById(Long id);

    /**
     * 查询中信银行账户余额查询结果列表
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 中信银行账户余额查询结果集合
     */
    public List<CiticAccountBalanceRow> selectCiticAccountBalanceRowList(CiticAccountBalanceRow citicAccountBalanceRow);

    /**
     * 新增中信银行账户余额查询结果
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 结果
     */
    public int insertCiticAccountBalanceRow(CiticAccountBalanceRow citicAccountBalanceRow);

    /**
     * 修改中信银行账户余额查询结果
     *
     * @param citicAccountBalanceRow 中信银行账户余额查询结果
     * @return 结果
     */
    public int updateCiticAccountBalanceRow(CiticAccountBalanceRow citicAccountBalanceRow);

    /**
     * 批量删除中信银行账户余额查询结果
     *
     * @param ids 需要删除的中信银行账户余额查询结果主键集合
     * @return 结果
     */
    public int deleteCiticAccountBalanceRowByIds(Long[] ids);

    /**
     * 删除中信银行账户余额查询结果信息
     *
     * @param id 中信银行账户余额查询结果主键
     * @return 结果
     */
    public int deleteCiticAccountBalanceRowById(Long id);

    public int GenerationDailyAccountBalanceRow() throws Exception;
}
