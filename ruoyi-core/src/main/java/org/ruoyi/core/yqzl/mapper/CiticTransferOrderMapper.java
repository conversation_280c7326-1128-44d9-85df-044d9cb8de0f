package org.ruoyi.core.yqzl.mapper;

import org.ruoyi.core.yqzl.domain.CiticTransferOrder;
import org.ruoyi.core.yqzl.domain.vo.CiticTransferOrderVo;

import java.util.List;

/**
 * 中信银行支付转账Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface CiticTransferOrderMapper
{
    /**
     * 查询中信银行支付转账
     *
     * @param id 中信银行支付转账主键
     * @return 中信银行支付转账
     */
    public CiticTransferOrder selectCiticTransferOrderById(Long id);

    /**
     * 查询中信银行支付转账列表
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 中信银行支付转账集合
     */
    public List<CiticTransferOrder> selectCiticTransferOrderList(CiticTransferOrderVo citicTransferOrder);

    /**
     * 新增中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    public int insertCiticTransferOrder(CiticTransferOrder citicTransferOrder);

    /**
     * 修改中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    public int updateCiticTransferOrder(CiticTransferOrder citicTransferOrder);

    /**
     * 删除中信银行支付转账
     *
     * @param id 中信银行支付转账主键
     * @return 结果
     */
    public int deleteCiticTransferOrderById(Long id);

    /**
     * 批量删除中信银行支付转账
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCiticTransferOrderByIds(Long[] ids);
}
