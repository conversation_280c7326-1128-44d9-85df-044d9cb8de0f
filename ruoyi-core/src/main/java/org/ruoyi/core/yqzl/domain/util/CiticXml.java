package org.ruoyi.core.yqzl.domain.util;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.http.HttpUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.util.Base64;
import java.util.List;

@Component
public class CiticXml {

    @Value("${yqzl.ip}")
    private String yqzlIp;

    @Value("${yqzl.userName}")
    private String yqzlUserName;

    public String getXmlMessage(String res) throws DocumentException, UnsupportedEncodingException {
        SAXReader saxReader = new SAXReader();
        Document document = saxReader.read(new StringReader(res));
        Element rootElement = document.getRootElement();
        if (!"AAAAAAA".equals(rootElement.element("status").getText())) {
            // SDK接口报错逻辑...
            throw new ServiceException(res);
        }

        String responseContent = rootElement.element("responseContent").getText();
        return new String(Base64.getDecoder().decode(responseContent), "GBK");
    }

    /**
     * 银行接口返回XML报文
     * @param action 对应请求代码
     * @param query 请求参数
     * @return
     * @throws DocumentException
     * @throws UnsupportedEncodingException
     */
    public String responseXMl(String action,Object query) throws DocumentException, UnsupportedEncodingException {
        String reqContent = XmlMessageGenerator.generateXmlMessage(action, yqzlUserName, query);
        String res  = HttpUtils.sendPost(yqzlIp + "/open/sendActionOfOA", reqContent, Constants.GBK);
        return this.getXmlMessage(res);
    }

    /**
     *  银行接口返回XML报文。外层还有特定标签 + <row>
     * @param action
     * @param query
     * @return
     * @throws DocumentException
     * @throws UnsupportedEncodingException
     */
    public String responseXMlWithRowList(String action,Object query) throws DocumentException, UnsupportedEncodingException {
        String reqContent = XmlMessageGenerator.generateXmlMessageWithRowList(action, yqzlUserName, query);
        String res  = HttpUtils.sendPost(yqzlIp + "/open/sendActionOfOA", reqContent, Constants.GBK);
        return this.getXmlMessage(res);
    }

    /**
     * 银行接口返回XML报文（支持任意类型的List<row>）
     * @param action 对应请求代码
     * @param dataList 请求参数列表（支持任意类型的List）
     * @param <T> 列表元素的类型
     * @return XML响应内容
     * @throws DocumentException
     * @throws UnsupportedEncodingException
     */
    public <T> String responseXMlList(String action, List<T> dataList) throws DocumentException, UnsupportedEncodingException {
        String reqContent = XmlMessageGenerator.generateXmlMessageList(action, yqzlUserName, dataList);
        String res  = HttpUtils.sendPost(yqzlIp + "/open/sendActionOfOA", reqContent, Constants.GBK);
        return this.getXmlMessage(res);
    }
}
