package org.ruoyi.core.yqzl.domain.rep;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 中信银行-支付转账响应
 */
@Data
public class TransferOrderResponse {
    /**
     * 交易状态 char(7)
     */
    private String status;

    /**
     * 交易状态信息 varchar(254)
     */
    private String statusText;

    /**
     * 提交成功总笔数 int
     */
    private Integer sucTotalNum;

    /**
     * 提交成功总金额 decimal(15,2)
     */
    private BigDecimal sucTotalAmt;

    /**
     * 提交失败总笔数 int
     */
    private Integer errTotalNum;

    /**
     * 提交失败总金额 decimal(15,2)
     */
    private BigDecimal errTotalAmt;

    private TransferOrderResponseRow row;
}
