package org.ruoyi.core.lyx.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.financial.controller.JsonResult;
import com.ruoyi.financial.domain.FinancialSubject;
import com.ruoyi.financial.domain.FinancialVoucher;
import com.ruoyi.financial.domain.FinancialVoucherDetails;
import com.ruoyi.financial.mapper.FinancialSubjectMapper;
import com.ruoyi.financial.service.IFinancialOpenService;
import org.ruoyi.core.lyx.domain.*;
import org.ruoyi.core.lyx.mapper.LyxDayCheckMapper;
import org.ruoyi.core.lyx.mapper.LyxDayVoucharDynamicMapper;
import org.ruoyi.core.lyx.mapper.LyxDayVoucharMapper;
import org.ruoyi.core.lyx.service.ILyxDayVoucharService;
import org.ruoyi.core.lyx.util.CreateVoucherUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
@Service
public class LyxDayVoucharServiceImpl implements ILyxDayVoucharService
{
    @Autowired
    private LyxDayVoucharMapper lyxDayVoucharMapper;
    
    @Autowired
    private IFinancialOpenService financialOpenService;
    @Autowired
    private FinancialSubjectMapper financialSubjectMapper;
    @Autowired
    private LyxDayVoucharDynamicMapper lyxDayVoucharDynamicMapper;

    @Autowired
    private LyxDayCheckMapper lyxDayCheckMapper;
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public LyxDayVouchar selectLyxDayVoucharById(Long id)
    {
        return lyxDayVoucharMapper.selectLyxDayVoucharById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<LyxDayVouchar> selectLyxDayVoucharList(LyxDayVouchar lyxDayVouchar)
    {
        return lyxDayVoucharMapper.selectLyxDayVoucharList(lyxDayVouchar);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertLyxDayVouchar(LyxDayVouchar lyxDayVouchar)
    {
        return lyxDayVoucharMapper.insertLyxDayVouchar(lyxDayVouchar);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateLyxDayVouchar(LyxDayVouchar lyxDayVouchar)
    {
        return lyxDayVoucharMapper.updateLyxDayVouchar(lyxDayVouchar);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteLyxDayVoucharByIds(Long[] ids)
    {
        return lyxDayVoucharMapper.deleteLyxDayVoucharByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteLyxDayVoucharById(Long id)
    {
        return lyxDayVoucharMapper.deleteLyxDayVoucharById(id);
    }

    @Override
    public Map<String, Object> createDayVouchar(DayVoucharVo dayVoucharVo) {
    Map<String, Object> returnMap = new HashMap<>();
        CreateVoucherUtil createVoucherUtil = new CreateVoucherUtil();
        FinancialVoucher financialVoucher = createVoucherUtil.handleVoucherRulesData();

        //一级科目id 银行存款
        Integer yhck = createVoucherUtil.addSubject("1", "0", "资产", "银行存款", "借", financialVoucher.getAccountSetsId().toString(), "",false,"");
        //一级科目id 财务费用
        Integer cwfy = createVoucherUtil.addSubject("1", "0", "损益", "财务费用", "借", financialVoucher.getAccountSetsId().toString(), "",false,"");
        //一级科目id 应收账款
        Integer yszk = createVoucherUtil.addSubject("1", "0", "资产", "应收账款", "借", financialVoucher.getAccountSetsId().toString(), "",false,"");
        //一级科目信息
        FinancialSubject yhckSub = financialSubjectMapper.selectById(yhck);
        FinancialSubject cwfySub = financialSubjectMapper.selectById(cwfy);
        FinancialSubject yszkSub = financialSubjectMapper.selectById(yszk);


        List<FinancialVoucherDetails> detailsList = new ArrayList<>();
        //添加二级科目
        //判断金额是否为0
        //中信大厦
        if(new BigDecimal(dayVoucharVo.getZxds().toString()).compareTo(BigDecimal.ZERO) >0){
            Integer zxds = createVoucherUtil.addSubject("2", yszk.toString(), "资产", "中信大厦", "借", financialVoucher.getAccountSetsId().toString(), yszkSub.getCode(),false,"");
            FinancialSubject zxdsSub = financialSubjectMapper.selectById(zxds);
            FinancialVoucherDetails details1 = new FinancialVoucherDetails();
            details1.setDebitAmount(dayVoucharVo.getZxds().doubleValue());
            details1.setSubjectId(zxds);
            details1.setSubjectName(zxdsSub.getCode()+"-"+yszkSub.getName()+"-"+zxdsSub.getName());
            details1.setSubjectCode(zxdsSub.getCode());
            details1.setSummary(dayVoucharVo.getVoucharAbstract());
            detailsList.add(details1);

        }
        //现金
        Integer kcxj = createVoucherUtil.addSubject("1","0", "资产", "库存现金", "借", financialVoucher.getAccountSetsId().toString(), "",false,"");
        FinancialSubject kcxjSub = financialSubjectMapper.selectById(kcxj);
        if(new BigDecimal(dayVoucharVo.getCash().toString()).compareTo(BigDecimal.ZERO)>0){
            Integer RMBsub = createVoucherUtil.addSubject("2",kcxj.toString(), "资产", "RMB", "借", financialVoucher.getAccountSetsId().toString(), "",false,"");
            FinancialSubject xjSub = financialSubjectMapper.selectById(RMBsub);
            FinancialVoucherDetails details1 = new FinancialVoucherDetails();
            details1.setDebitAmount(dayVoucharVo.getCash().doubleValue());
            details1.setSubjectId(RMBsub);
            details1.setSubjectName(xjSub.getCode()+"-"+kcxjSub.getName()+"-"+xjSub.getName());
            details1.setSubjectCode(xjSub.getCode());
            details1.setSummary(dayVoucharVo.getVoucharAbstract());
            detailsList.add(details1);

        }

        //史总卡到账
        if(new BigDecimal(dayVoucharVo.getSzkdz().toString()).compareTo(BigDecimal.ZERO)>0){
            Integer szkdz = createVoucherUtil.addSubject("2", yhck.toString(), "资产", "樊楼民生行POS机到帐", "借", financialVoucher.getAccountSetsId().toString(), yhckSub.getCode(),true,"*********");
            FinancialSubject szkdzSub = financialSubjectMapper.selectById(szkdz);
            FinancialVoucherDetails details1 = new FinancialVoucherDetails();
            details1.setDebitAmount(dayVoucharVo.getSzkdz().doubleValue());
            details1.setSubjectId(szkdz);
            details1.setSubjectName(szkdzSub.getCode()+"-"+yhckSub.getName()+"-"+szkdzSub.getName());
            details1.setSubjectCode(szkdzSub.getCode());
            details1.setSummary(dayVoucharVo.getVoucharAbstract());
            detailsList.add(details1);

        }
        //民生银行pos
        if(new BigDecimal(dayVoucharVo.getMsyhPOS().toString()).compareTo(BigDecimal.ZERO)>0){
            Integer msyhpos = createVoucherUtil.addSubject("2", yhck.toString(), "资产", "樊楼民生行", "借", financialVoucher.getAccountSetsId().toString(), yhckSub.getCode(),true,"*********");
            FinancialSubject msyhposSub = financialSubjectMapper.selectById(msyhpos);
            FinancialVoucherDetails details1 = new FinancialVoucherDetails();
            details1.setDebitAmount(dayVoucharVo.getMsyhPOS().doubleValue());
            details1.setSubjectId(msyhpos);
            details1.setSubjectName(msyhposSub.getCode()+"-"+yhckSub.getName()+"-"+msyhposSub.getName());
            details1.setSubjectCode(msyhposSub.getCode());
            details1.setSummary(dayVoucharVo.getVoucharAbstract());
            detailsList.add(details1);

        }
        //手续费
        if(new BigDecimal(dayVoucharVo.getServiceCharge().toString()).compareTo(BigDecimal.ZERO)>0){
            Integer sxf = createVoucherUtil.addSubject("2", cwfy.toString(), "损益", "手续费", "借", financialVoucher.getAccountSetsId().toString(), cwfySub.getCode(),false,"");
            FinancialSubject sxfSub = financialSubjectMapper.selectById(sxf);
            FinancialVoucherDetails details1 = new FinancialVoucherDetails();
            details1.setDebitAmount(dayVoucharVo.getServiceCharge().doubleValue());
            details1.setSubjectId(sxf);
            details1.setSubjectName(sxfSub.getCode()+"-"+cwfySub.getName()+"-"+sxfSub.getName());
            details1.setSubjectCode(sxfSub.getCode());
            details1.setSummary(dayVoucharVo.getVoucharAbstract());
            detailsList.add(details1);

        }
        List<DayVoucharDynamicVo> dynamicVoList = dayVoucharVo.getDynamicVoList();
        if(null!=dayVoucharVo.getDynamicVoList() && dynamicVoList.size()>0){
            for (DayVoucharDynamicVo dayVoucharDynamicVo : dynamicVoList) {
                if(new BigDecimal(dayVoucharDynamicVo.getAmount().toString()).compareTo(BigDecimal.ZERO)>0){
                    Integer dtsrx = createVoucherUtil.addSubject("2", yszk.toString(), "资产", dayVoucharDynamicVo.getName(), "借", financialVoucher.getAccountSetsId().toString(), yszkSub.getCode(),false,"");
                    FinancialSubject dtsrxSub = financialSubjectMapper.selectById(dtsrx);
                    FinancialVoucherDetails details1 = new FinancialVoucherDetails();
                    details1.setDebitAmount(dayVoucharDynamicVo.getAmount().doubleValue());
                    details1.setSubjectId(dtsrx);
                    details1.setSubjectName(dtsrxSub.getCode()+"-"+yszkSub.getName()+"-"+dtsrxSub.getName());
                    details1.setSubjectCode(dtsrxSub.getCode());
                    details1.setSummary(dayVoucharVo.getVoucharAbstract());
                    detailsList.add(details1);
                }
            }
        }
        //主营业务收入
        Integer zyywsr = createVoucherUtil.addSubject("1", "0", "损益", "主营业务收入", "贷", financialVoucher.getAccountSetsId().toString(), "",false,"");
        //一级科目信息
        FinancialSubject zyywsrSub = financialSubjectMapper.selectById(zyywsr);

        //饭卡收入 改为二级科目
        Integer fksr = createVoucherUtil.addSubject("2", zyywsr.toString(), "损益", "饭卡收入", "贷", financialVoucher.getAccountSetsId().toString(), "",false,"");

        FinancialSubject fksrSub = financialSubjectMapper.selectById(fksr);
        FinancialVoucherDetails details3 = new FinancialVoucherDetails();
        details3.setCreditAmount(dayVoucharVo.getFkIncome().doubleValue());
        details3.setSubjectId(fksr);
        details3.setSubjectName(fksrSub.getCode()+"-"+zyywsrSub.getName()+"-"+fksrSub.getName());
        details3.setSubjectCode(fksrSub.getCode());
        details3.setSummary(dayVoucharVo.getVoucharAbstract());
        detailsList.add(details3);

        //餐费收入 改为二级科目2024-12-26
        Integer cfsr = createVoucherUtil.addSubject("2", zyywsr.toString(), "损益", "餐费收入", "贷", financialVoucher.getAccountSetsId().toString(), "",false,"");

        FinancialSubject cfsrSub = financialSubjectMapper.selectById(cfsr);
        FinancialVoucherDetails details4 = new FinancialVoucherDetails();
        details4.setCreditAmount(dayVoucharVo.getCfIncome().doubleValue());
        details4.setSubjectId(cfsr);
        details4.setSubjectName(cfsrSub.getCode()+"-"+zyywsrSub.getName()+"-"+cfsrSub.getName());
        details4.setSubjectCode(cfsrSub.getCode());
        details4.setSummary(dayVoucharVo.getVoucharAbstract());
        detailsList.add(details4);

        financialVoucher.setDetails(detailsList);
        //
        //o表示oa系统
        financialVoucher.setSource("O");
        //生成凭证接口
        JsonResult jsonResult = financialOpenService.thirdAddVoucher(financialVoucher);

        FinancialVoucher financialVoucher1 = (FinancialVoucher)jsonResult.getData();
        Long aLong = Long.valueOf(financialVoucher1.getId());

        //新增记录
        LyxDayVouchar lyxDayVouchar = new LyxDayVouchar();
        lyxDayVouchar.setCollectionDate(dayVoucharVo.getCollectionDate());
        lyxDayVouchar.setProofCreateTime(financialVoucher.getVoucherDate());
        lyxDayVouchar.setDocumenter(financialVoucher.getCreateMember().toString());
        lyxDayVouchar.setDayAbstract(dayVoucharVo.getVoucharAbstract());
        lyxDayVouchar.setYszkZhongxin(dayVoucharVo.getZxds());
        lyxDayVouchar.setCash(dayVoucharVo.getCash());
        lyxDayVouchar.setYhckSzkdz(dayVoucharVo.getSzkdz());
        lyxDayVouchar.setYhckMsyhpos(dayVoucharVo.getMsyhPOS());
        lyxDayVouchar.setCwfySxf(dayVoucharVo.getServiceCharge());
        lyxDayVouchar.setBorrowTotal(new BigDecimal(financialVoucher.getDebitAmount()));
        lyxDayVouchar.setFankaTotal(dayVoucharVo.getFkIncome());
        lyxDayVouchar.setCanfeiTotal(dayVoucharVo.getCfIncome());
        lyxDayVouchar.setLoanTotal(new BigDecimal(financialVoucher.getCreditAmount()));
        lyxDayVouchar.setDayCheckId(dayVoucharVo.getDayCheckId());
        lyxDayVouchar.setVoucherId(aLong);
        int i = lyxDayVoucharMapper.insertLyxDayVouchar(lyxDayVouchar);
        List<LyxDayVoucharDynamic> voucharDynamicList = new ArrayList<>();


        for (DayVoucharDynamicVo dayVoucharDynamicVo : dynamicVoList) {
            LyxDayVoucharDynamic lyxDayVoucharDynamic = new LyxDayVoucharDynamic();
            lyxDayVoucharDynamic.setDayVoucharId(lyxDayVouchar.getId());
            lyxDayVoucharDynamic.setDtsrxName(dayVoucharDynamicVo.getName());
            lyxDayVoucharDynamic.setAmount(dayVoucharDynamicVo.getAmount());
            voucharDynamicList.add(lyxDayVoucharDynamic);
        }
        lyxDayVoucharDynamicMapper.batchInsert(voucharDynamicList);


        return returnMap;
    }


    @Override
    public Map<String, Object> queryDayVoucharData(LyxDayVouchar lyxDayVouchar) {
        HashMap<String, Object> returnMap = new HashMap<>();
        List<String> strings = this.queryAllField();
        String selectMonth = lyxDayVouchar.getSelectMonth();
        List<Map<String,Object>> dataList =   this.queryData1(selectMonth);
        returnMap.put("fieldList",strings);
        returnMap.put("dataList",dataList);
        return returnMap;
    }


    /**
     * 获取所有的字段名
     * @return {@link List}<{@link String}>
     */
    public List<String> queryAllField(){
        LyxDayVouchar lyxDayVouchar = new LyxDayVouchar();
        List<String> strings = lyxDayVoucharDynamicMapper.queryFildList();
        Map map = JSON.parseObject(JSON.toJSONString(lyxDayVouchar), Map.class);
        Set set = map.keySet();
        boolean b = strings.addAll(set);

        return strings;
    }
    public List<Map<String,Object>> queryData1(String selectMonth){


        List<String> dayCheckId = lyxDayCheckMapper.getDayCheckId(selectMonth);
        if (null != dayCheckId && dayCheckId.size()>0 ){
            List<Map<String, Object>> maps = lyxDayVoucharMapper.queryDataByselectMonth(dayCheckId);
            List<String> strings = this.queryAllField();
            for (Map<String, Object> map : maps) {
                for (String string : strings) {
                    map.put(string,"X");
                }
                //查询每条数据的动态收入项金额
                List<Map<String, Object>> incomeList =  lyxDayVoucharDynamicMapper.queryIncomeDataByDayId(map.get("id").toString());
                Map<String, Object> listToMap = this.listToMap(incomeList);
                map.putAll(listToMap);

                //查询每条数据的动态收入项金额
                map.put("otherFieldLid",incomeList);
            }
            return maps;
        }
        return new ArrayList<>();
    }

    /**
     * listList<Map<String, Object>>转为map
     * @param dataList
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String,Object> listToMap(List<Map<String, Object>> dataList){
        HashMap<String, Object> returnMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            returnMap.put(map.get("name").toString(),map.get("avalues"));
        }
        return  returnMap;
    }
}
