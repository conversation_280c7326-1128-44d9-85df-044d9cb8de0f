package org.ruoyi.core.lyx.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.lyx.domain.LyxDayVouchar;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface LyxDayVoucharMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxDayVouchar selectLyxDayVoucharById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<LyxDayVouchar> selectLyxDayVoucharList(LyxDayVouchar lyxDayVouchar);

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxDayVouchar(LyxDayVouchar lyxDayVouchar);

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxDayVouchar 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxDayVouchar(LyxDayVouchar lyxDayVouchar);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDayVoucharById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLyxDayVoucharByIds(Long[] ids);

    List<LyxDayVouchar> selectDataByDayId(@Param("dayCheckId") Long dayCheckId);

    List<Map<String, Object>> queryDataByselectMonth(@Param("dayCheckId") List<String> dayCheckId);
}
