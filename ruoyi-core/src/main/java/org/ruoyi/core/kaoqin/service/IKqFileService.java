package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.KqFile;
import org.ruoyi.core.personnel.domain.vo.PersonnelFileVo;

import java.util.List;

/**
 * 考勤管理-文件Service接口
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface IKqFileService
{
    /**
     * 查询考勤管理-文件
     *
     * @param id 考勤管理-文件主键
     * @return 考勤管理-文件
     */
    public KqFile selectKqFileById(Long id);

    /**
     * 查询考勤管理-文件列表
     *
     * @param kqFile 考勤管理-文件
     * @return 考勤管理-文件集合
     */
    public List<KqFile> selectKqFileList(KqFile kqFile);

    /**
     * 新增考勤管理-文件
     *
     * @param kqFile 考勤管理-文件
     * @return 结果
     */
    public int insertKqFile(KqFile kqFile);

    /**
     * 修改考勤管理-文件
     *
     * @param kqFile 考勤管理-文件
     * @return 结果
     */
    public int updateKqFile(KqFile kqFile);

    /**
     * 批量删除考勤管理-文件
     *
     * @param ids 需要删除的考勤管理-文件主键集合
     * @return 结果
     */
    public int deleteKqFileByIds(Long[] ids);

    /**
     * 删除考勤管理-文件信息
     *
     * @param id 考勤管理-文件主键
     * @return 结果
     */
    public int deleteKqFileById(Long id);

    public int insertKqFiles(List<KqFile> files);

    public int deleteByCorrelationId(KqFile file);

    public int correlationFile(KqFile kqFile);
}
