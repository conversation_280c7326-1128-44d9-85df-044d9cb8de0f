package org.ruoyi.core.kaoqin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 请假从对象 kq_ask_leave_slave
 *
 * <AUTHOR>
 * @date 2024-06-27
 */
@Data
public class AskLeaveSlave extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联主表id */
    @Excel(name = "关联主表id")
    private Long leaveId;

    /** 假种 */
    @Excel(name = "假种")
    private String leaveType;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 开始时间段 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "开始时间段", width = 30, dateFormat = "HH:mm")
    private Date startTimePeriod;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 结束时间段 */
    @JsonFormat(pattern = "HH:mm")
    @Excel(name = "结束时间段", width = 30, dateFormat = "HH:mm")
    private Date endTimePeriod;

    /** 总计时长 */
    @Excel(name = "总计时长")
    private Double times;

    private String processId;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("leaveId", getLeaveId())
            .append("leaveType", getLeaveType())
            .append("startTime", getStartTime())
            .append("startTimePeriod", getStartTimePeriod())
            .append("endTime", getEndTime())
            .append("endTimePeriod", getEndTimePeriod())
            .append("times", getTimes())
            .toString();
    }
}
