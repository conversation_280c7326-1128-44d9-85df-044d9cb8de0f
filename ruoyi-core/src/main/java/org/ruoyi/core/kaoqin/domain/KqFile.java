package org.ruoyi.core.kaoqin.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 考勤管理-文件对象 kq_file
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
public class KqFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 关联数据id */
    @Excel(name = "关联数据id")
    private Long correlationId;

    /** 关联类型 1奖惩 */
    @Excel(name = "关联类型 1奖惩 2.出差")
    private String fileType;

    /** 文件名 */
    @Excel(name = "文件名")
    private String fileName;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String fileUrl;

    /** 状态 0.逻辑删除 1.未删除 */
    @Excel(name = "状态 0.逻辑删除 1.未删除")
    private String fileState;

    private List<Long> ids;
}
