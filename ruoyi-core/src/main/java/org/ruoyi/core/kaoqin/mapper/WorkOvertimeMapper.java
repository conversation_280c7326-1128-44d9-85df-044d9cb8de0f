package org.ruoyi.core.kaoqin.mapper;

import org.ruoyi.core.kaoqin.domain.WorkOvertime;
import org.ruoyi.core.kaoqin.domain.vo.WorkOvertimeVo;

import java.util.List;

/**
 * 加班申请Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface WorkOvertimeMapper
{
    /**
     * 查询加班申请
     *
     * @param id 加班申请主键
     * @return 加班申请
     */
    public WorkOvertime selectWorkOvertimeById(Long id);

    public WorkOvertime selectWorkOvertimeByHandleId(Long id);

    public WorkOvertime selectWorkOvertimeByProcessId(String id);

    /**
     * 查询加班申请列表
     *
     * @param workOvertime 加班申请
     * @return 加班申请集合
     */
    public List<WorkOvertime> selectWorkOvertimeList(WorkOvertimeVo workOvertime);

    /**
     * 新增加班申请
     *
     * @param workOvertime 加班申请
     * @return 结果
     */
    public int insertWorkOvertime(WorkOvertime workOvertime);


    public int getCountByCreateTime(String createTime);

    /**
     * 修改加班申请
     *
     * @param workOvertime 加班申请
     * @return 结果
     */
    public int updateWorkOvertime(WorkOvertime workOvertime);

    /**
     * 删除加班申请
     *
     * @param id 加班申请主键
     * @return 结果
     */
    public int deleteWorkOvertimeById(Long id);

    /**
     * 批量删除加班申请
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOvertimeByIds(Long[] ids);

    int passWorkOvertimeById(Long id);
    int unpassWorkOvertimeById(Long id);

    public int voidWorkOvertime(WorkOvertime workOvertime);

    public int inspectionTime(WorkOvertimeVo workOvertime);
}
