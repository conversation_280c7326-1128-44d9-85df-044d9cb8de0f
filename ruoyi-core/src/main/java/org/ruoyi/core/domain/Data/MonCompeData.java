package org.ruoyi.core.domain.Data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@ToString
public class MonCompeData {

    /** 主键 */
    private Long id;

    /**  外部系统平台名称 */
    @Excel(name = "合作方编码",dictType = "platform_no")
    private String platformNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码",dictType = "partner_no")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码",dictType = "fund_no")
    private String fundNo;

    /** 统计终止月份 */
    private String reconDate;

    private BigDecimal addCompensatePrintAmount;
    /** 当期新增-代偿利息（元） */


    private BigDecimal addCompensateIntAmount;


    /** 每月代偿金额 */

    private BigDecimal addCompensateIntAmountSum;

    /** 代偿率 */
    private String compenRate;
    /** 月初本金在贷余额 */
    private BigDecimal totalFundBalanceAmt;

    public BigDecimal getAddCompensateIntAmountSum() {
        return addCompensateIntAmountSum;
    }

    public void setAddCompensateIntAmountSum(BigDecimal addCompensateIntAmountSum) {
        this.addCompensateIntAmountSum = addCompensateIntAmountSum;
    }

    public String getReconDate() {
        return reconDate;
    }

    public BigDecimal getTotalFundBalanceAmt() {
        return totalFundBalanceAmt;
    }

    public void setTotalFundBalanceAmt(BigDecimal totalFundBalanceAmt) {
        this.totalFundBalanceAmt = totalFundBalanceAmt;
    }

    public void setReconDate(String reconDate) {
        this.reconDate = reconDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPlatformNo() {
        return platformNo;
    }

    public void setPlatformNo(String platformNo) {
        this.platformNo = platformNo;
    }

    public String getPartnerNo() {
        return partnerNo;
    }

    public void setPartnerNo(String partnerNo) {
        this.partnerNo = partnerNo;
    }

    public String getFundNo() {
        return fundNo;
    }

    public void setFundNo(String fundNo) {
        this.fundNo = fundNo;
    }



    public BigDecimal getAddCompensatePrintAmount() {
        return addCompensatePrintAmount;
    }

    public void setAddCompensatePrintAmount(BigDecimal addCompensatePrintAmount) {
        this.addCompensatePrintAmount = addCompensatePrintAmount;
    }

    public BigDecimal getAddCompensateIntAmount() {
        return addCompensateIntAmount;
    }

    public void setAddCompensateIntAmount(BigDecimal addCompensateIntAmount) {
        this.addCompensateIntAmount = addCompensateIntAmount;
    }

    public String getCompenRate() {
        return compenRate;
    }

    public void setCompenRate(String compenRate) {
        this.compenRate = compenRate;
    }
}
