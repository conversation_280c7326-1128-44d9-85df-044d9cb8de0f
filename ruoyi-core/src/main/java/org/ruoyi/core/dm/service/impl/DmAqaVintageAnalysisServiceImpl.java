package org.ruoyi.core.dm.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.system.service.INewAuthorityService;
import org.ruoyi.core.dm.domain.DmAqaVintageAnalysis;
import org.ruoyi.core.dm.domain.vo.DmAqaFpdAnalysisVo;
import org.ruoyi.core.dm.domain.vo.DmAqaVintageAnalysisVo;
import org.ruoyi.core.dm.mapper.DmAqaVintageAnalysisMapper;
import org.ruoyi.core.dm.service.IDmAqaVintageAnalysisService;
import org.ruoyi.core.domain.DData;
import org.ruoyi.core.domain.DProjectParameter;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.oasystem.service.IOaProjectDeployService;
import org.ruoyi.core.service.impl.SysSelectDataRefServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 产品vintage分析Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@Service
public class DmAqaVintageAnalysisServiceImpl implements IDmAqaVintageAnalysisService
{
    @Autowired
    private DmAqaVintageAnalysisMapper dmAqaVintageAnalysisMapper;
    @Autowired
    private SysSelectDataRefServiceImpl sysSelectDataRefService;
    @Autowired
    private INewAuthorityService newAuthorityService;

    /**
     * 查询产品vintage分析
     *
     * @param id 产品vintage分析主键
     * @return 产品vintage分析
     */
    @Override
    public DmAqaVintageAnalysis selectDmAqaVintageAnalysisById(Long id)
    {
        return dmAqaVintageAnalysisMapper.selectDmAqaVintageAnalysisById(id);
    }

    /**
     * 查询产品vintage分析列表
     *
     * @param dmAqaVintageAnalysis 产品vintage分析
     * @return 产品vintage分析
     */
    @Override
    public List<DmAqaVintageAnalysisVo> selectDmAqaVintageAnalysisList(DmAqaVintageAnalysisVo dmAqaVintageAnalysis)
    {
        //bug 会自动分页
        Long pageSize = dmAqaVintageAnalysis.getPageSize();
        dmAqaVintageAnalysis.setPageSize(null);
        //根据---系统,担保公司,合作方,资金方来查找产品
//        ProjectCompanyRelevance projectCompanyRelevance = new ProjectCompanyRelevance();
//        projectCompanyRelevance.setModuleTypeOfNewAuth("DATAREPORT");
//        projectCompanyRelevance.setUnitType("3");
//        List<Map<String, Object>> maps = sysSelectDataRefService.queryCompanyByProjectId(projectCompanyRelevance, getLoginUser());

        LoginUser loginUser = getLoginUser();
        List<Long> auProjectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(loginUser.getUser().getUserId(), AuthModuleEnum.DATAREPORT.getCode());


        if(auProjectIds.isEmpty()){
            return new ArrayList<DmAqaVintageAnalysisVo>();
        }
//        List<String> values = maps.stream().flatMap(map -> map.values().stream().map(Object::toString)).collect(Collectors.toList());
        dmAqaVintageAnalysis.setAuProjectIds(auProjectIds);

        List<DProjectParameter> dProjectParameters = dmAqaVintageAnalysisMapper.selectDProjectParameter(dmAqaVintageAnalysis);
        Set<String> productNos = dProjectParameters.stream().map(DProjectParameter::getProductNo).collect(Collectors.toSet());

        if(dmAqaVintageAnalysis.getProductNos() != null && !dmAqaVintageAnalysis.getProductNos().isEmpty()){
            dmAqaVintageAnalysis.getProductNos().retainAll(productNos);
        } else {
            dmAqaVintageAnalysis.setProductNos(productNos);
        }
        if (dmAqaVintageAnalysis.getProductNos().isEmpty()){
            return new ArrayList<DmAqaVintageAnalysisVo>();
        }

        if(dmAqaVintageAnalysis.getSortMap() != null){
            Map<String, String> underscoreMap = new HashMap<>();
            for (String key : dmAqaVintageAnalysis.getSortMap().keySet()) {
                String newKey = convertToUnderscore(key);
                if(newKey.equals("product_name")){
                    underscoreMap.put("CONVERT(LEFT(product_name, 1) USING gbk) COLLATE gbk_chinese_ci", dmAqaVintageAnalysis.getSortMap().get(key));
                } else {
                    underscoreMap.put(newKey, dmAqaVintageAnalysis.getSortMap().get(key));
                }
            }
            dmAqaVintageAnalysis.setSortMap(underscoreMap);
        }

        dmAqaVintageAnalysis.setPageSize(pageSize);
        PageUtil.startPage();
        return dmAqaVintageAnalysisMapper.selectDmAqaVintageAnalysisList(dmAqaVintageAnalysis);
    }


    @Override
    public Map<String,Object> selectDmAqaVintageAnalysisEcharts(DmAqaVintageAnalysisVo dmAqaVintageAnalysis)
    {


        Map<String,Object> hashMap = new HashMap<String,Object>();
        ArrayList<Object> arrayList = new ArrayList<>();
        if(dmAqaVintageAnalysis.getCustNos().isEmpty() &&
           dmAqaVintageAnalysis.getFundNos().isEmpty() &&
           dmAqaVintageAnalysis.getPartnerNos().isEmpty() &&
           dmAqaVintageAnalysis.getSystemNos().isEmpty() &&
           dmAqaVintageAnalysis.getProductNos().isEmpty()
        ){
            return hashMap;
        }
        List<DProjectParameter> dProjectParameters = dmAqaVintageAnalysisMapper.selectDProjectParameter(dmAqaVintageAnalysis);
        Set<String> productNos = dProjectParameters.stream().map(DProjectParameter::getProductNo).collect(Collectors.toSet());

        if(dmAqaVintageAnalysis.getProductNos() != null && !dmAqaVintageAnalysis.getProductNos().isEmpty()){
            dmAqaVintageAnalysis.getProductNos().retainAll(productNos);
        } else {
            dmAqaVintageAnalysis.setProductNos(productNos);
        }
        if (dmAqaVintageAnalysis.getProductNos().isEmpty()){
            return hashMap;
        }

        List<DmAqaVintageAnalysisVo> dmAqaVintageAnalysisVos = dmAqaVintageAnalysisMapper.selectDmAqaVintageAnalysisList(dmAqaVintageAnalysis)
                                                                .stream().sorted(Comparator.comparing(DmAqaVintageAnalysisVo::getLoanMonth))
                                                                .collect(Collectors.toList());;
        dmAqaVintageAnalysisVos.forEach(vo -> {

            Map<String,Object> dataMap = new HashMap<String,Object>();
            BigDecimal[] data= {
                    vo.getMob1(),vo.getMob2(),vo.getMob3(),vo.getMob4(),vo.getMob5(),vo.getMob6(),vo.getMob7(),vo.getMob8(),vo.getMob9(),vo.getMob10(),vo.getMob11(),vo.getMob12(),
                    vo.getMob13(),vo.getMob14(),vo.getMob15(),vo.getMob16(),vo.getMob17(),vo.getMob18(),vo.getMob19(),vo.getMob20(),vo.getMob21(),vo.getMob22(),vo.getMob23(),vo.getMob24(),
            };
            dataMap.put("name",vo.getLoanMonth());
            dataMap.put("data",data);
            arrayList.add(dataMap);
        });

        Map<String, DmAqaVintageAnalysisVo> collect = dmAqaVintageAnalysisVos.stream().collect(Collectors.toMap(
                DmAqaVintageAnalysisVo::getLoanMonth,
                vo -> vo,
                (existing, replacement) -> replacement
        ));
        Map<String,Map<String,BigDecimal>> mobMap = new HashMap<String,Map<String,BigDecimal>>();
        mobMap.put("MOB1",new HashMap<String,BigDecimal>());
        mobMap.put("MOB2",new HashMap<String,BigDecimal>());
        mobMap.put("MOB3",new HashMap<String,BigDecimal>());
        mobMap.put("MOB4",new HashMap<String,BigDecimal>());
        mobMap.put("MOB5",new HashMap<String,BigDecimal>());
        mobMap.put("MOB6",new HashMap<String,BigDecimal>());
        mobMap.put("MOB7",new HashMap<String,BigDecimal>());
        mobMap.put("MOB8",new HashMap<String,BigDecimal>());
        mobMap.put("MOB9",new HashMap<String,BigDecimal>());
        mobMap.put("MOB10",new HashMap<String,BigDecimal>());
        mobMap.put("MOB11",new HashMap<String,BigDecimal>());
        mobMap.put("MOB12",new HashMap<String,BigDecimal>());
        mobMap.put("MOB13",new HashMap<String,BigDecimal>());
        mobMap.put("MOB14",new HashMap<String,BigDecimal>());
        mobMap.put("MOB15",new HashMap<String,BigDecimal>());
        mobMap.put("MOB16",new HashMap<String,BigDecimal>());
        mobMap.put("MOB17",new HashMap<String,BigDecimal>());
        mobMap.put("MOB18",new HashMap<String,BigDecimal>());
        mobMap.put("MOB19",new HashMap<String,BigDecimal>());
        mobMap.put("MOB20",new HashMap<String,BigDecimal>());
        mobMap.put("MOB21",new HashMap<String,BigDecimal>());
        mobMap.put("MOB22",new HashMap<String,BigDecimal>());
        mobMap.put("MOB23",new HashMap<String,BigDecimal>());
        mobMap.put("MOB24",new HashMap<String,BigDecimal>());
        dmAqaVintageAnalysisVos.forEach(vo -> {
            mobMap.get("MOB1").put(vo.getLoanMonth(), vo.getMob1());
            mobMap.get("MOB2").put(vo.getLoanMonth(), vo.getMob2());
            mobMap.get("MOB3").put(vo.getLoanMonth(), vo.getMob3());
            mobMap.get("MOB4").put(vo.getLoanMonth(), vo.getMob4());
            mobMap.get("MOB5").put(vo.getLoanMonth(), vo.getMob5());
            mobMap.get("MOB6").put(vo.getLoanMonth(), vo.getMob6());
            mobMap.get("MOB7").put(vo.getLoanMonth(), vo.getMob7());
            mobMap.get("MOB8").put(vo.getLoanMonth(), vo.getMob8());
            mobMap.get("MOB9").put(vo.getLoanMonth(), vo.getMob9());
            mobMap.get("MOB10").put(vo.getLoanMonth(), vo.getMob10());
            mobMap.get("MOB11").put(vo.getLoanMonth(), vo.getMob11());
            mobMap.get("MOB12").put(vo.getLoanMonth(), vo.getMob12());
            mobMap.get("MOB13").put(vo.getLoanMonth(), vo.getMob13());
            mobMap.get("MOB14").put(vo.getLoanMonth(), vo.getMob14());
            mobMap.get("MOB15").put(vo.getLoanMonth(), vo.getMob15());
            mobMap.get("MOB16").put(vo.getLoanMonth(), vo.getMob16());
            mobMap.get("MOB17").put(vo.getLoanMonth(), vo.getMob17());
            mobMap.get("MOB18").put(vo.getLoanMonth(), vo.getMob18());
            mobMap.get("MOB19").put(vo.getLoanMonth(), vo.getMob19());
            mobMap.get("MOB20").put(vo.getLoanMonth(), vo.getMob20());
            mobMap.get("MOB21").put(vo.getLoanMonth(), vo.getMob2());
            mobMap.get("MOB22").put(vo.getLoanMonth(), vo.getMob21());
            mobMap.get("MOB23").put(vo.getLoanMonth(), vo.getMob21());
            mobMap.get("MOB24").put(vo.getLoanMonth(), vo.getMob21());
        });
        hashMap.put("data",arrayList);
        hashMap.put("allMonth",collect);
        hashMap.put("mob",mobMap);
        return hashMap;
    }

    /**
     * 新增产品vintage分析
     *
     * @param dmAqaVintageAnalysis 产品vintage分析
     * @return 结果
     */
    @Override
    public int insertDmAqaVintageAnalysis(DmAqaVintageAnalysis dmAqaVintageAnalysis)
    {
        return dmAqaVintageAnalysisMapper.insertDmAqaVintageAnalysis(dmAqaVintageAnalysis);
    }

    /**
     * 修改产品vintage分析
     *
     * @param dmAqaVintageAnalysis 产品vintage分析
     * @return 结果
     */
    @Override
    public int updateDmAqaVintageAnalysis(DmAqaVintageAnalysis dmAqaVintageAnalysis)
    {
        return dmAqaVintageAnalysisMapper.updateDmAqaVintageAnalysis(dmAqaVintageAnalysis);
    }

    /**
     * 批量删除产品vintage分析
     *
     * @param ids 需要删除的产品vintage分析主键
     * @return 结果
     */
    @Override
    public int deleteDmAqaVintageAnalysisByIds(Long[] ids)
    {
        return dmAqaVintageAnalysisMapper.deleteDmAqaVintageAnalysisByIds(ids);
    }

    /**
     * 删除产品vintage分析信息
     *
     * @param id 产品vintage分析主键
     * @return 结果
     */
    @Override
    public int deleteDmAqaVintageAnalysisById(Long id)
    {
        return dmAqaVintageAnalysisMapper.deleteDmAqaVintageAnalysisById(id);
    }

    public static String convertToUnderscore(String camelCase) {
        StringBuilder result = new StringBuilder();
        for (char c : camelCase.toCharArray()) {
            if (Character.isUpperCase(c)) {
                result.append("_").append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }
}
