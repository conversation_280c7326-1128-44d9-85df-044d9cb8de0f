package org.ruoyi.core.modules.fcdataquery.mapper;

import org.ruoyi.core.modules.fcdataquery.po.CompensateDetailPo;
import org.ruoyi.core.modules.fcdataquery.vo.CompensateDetailVo;

import java.util.List;

/**
 * 运营部-代偿款Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface FcCompensateMapper
{
    /**
     * <AUTHOR>
     * @Description 获取凭证明细
     * @Date 2024/11/20 14:42
     * @Param [compensateDetailVo]
     * @return java.util.List<org.ruoyi.core.modules.fcdataquery.po.CompensateDetailPo>
     **/
    List<CompensateDetailPo> getVoucherDetailByProject(CompensateDetailVo compensateDetailVo);
}
