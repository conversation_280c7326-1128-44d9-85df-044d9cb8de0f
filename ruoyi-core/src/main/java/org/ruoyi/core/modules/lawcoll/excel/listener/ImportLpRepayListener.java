package org.ruoyi.core.modules.lawcoll.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.modules.lawcoll.domain.LawCollCheck;
import org.ruoyi.core.modules.lawcoll.domain.LawCollCheckRepay;
import org.ruoyi.core.modules.lawcoll.excel.importE.ImportLpRepay;
import org.ruoyi.core.modules.lawcoll.mapper.LawCollCheckRepayMapper;
import org.ruoyi.core.modules.lawcoll.utils.PatternUtil;
import org.ruoyi.core.modules.lawcoll.vo.LpVo;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模板的读取类
 *
 * <AUTHOR> Zhuang
 */
// 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
@Slf4j
public class ImportLpRepayListener implements ReadListener<ImportLpRepay> {
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    private List<ImportLpRepay> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);


    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */

    private LpVo lpVo;
    private LawCollCheck lawCollCheck;
    private LawCollCheckRepayMapper lawCollCheckRepayMapper;


    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param lawCollCheckRepayMapper
     */
    public ImportLpRepayListener(LpVo lpVo, LawCollCheck lawCollCheck, LawCollCheckRepayMapper lawCollCheckRepayMapper) {
        this.lpVo = lpVo;
        this.lawCollCheck = lawCollCheck;
        this.lawCollCheckRepayMapper = lawCollCheckRepayMapper;
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        String sheetName = context.readSheetHolder().getSheetName();
        List<String> headList = new ArrayList<>();
        String errMsg = "";
        if(lpVo.getCust().equals("ahzj")){
            String[] mustExistHeads = {"借据编号", "真实回款金额（元）", "回款日期", "回款途径", "委托逾期天数"};
            headList = new ArrayList<>(Arrays.asList(mustExistHeads));
            errMsg = "未在工作表<span style=\"color:red\">【"+sheetName+"】</span>中找到所有字段：借据编号、真实回款金额（元）、回款日期、回款途径、委托逾期天数";
        }else{
            String[] mustExistHeads = {"借据编号", "真实回款金额（元）", "回款诉讼费（元）", "回款日期"};
            headList = new ArrayList<>(Arrays.asList(mustExistHeads));
            errMsg = "未在工作表<span style=\"color:red\">【"+sheetName+"】</span>中找到所有字段：借据编号、真实回款金额（元）、回款诉讼费（元）、回款日期";
        }

        List<String> allHeads = headMap.values().stream().map(ReadCellData::getStringValue).collect(Collectors.toList());
        for(int i=0; i < headMap.size(); i++){
            ReadCellData<?> readCellData = headMap.get(i);
            if(null == readCellData || StringUtils.isBlank(readCellData.getStringValue())){
                continue;
            }
            if(!headList.contains(readCellData.getStringValue())){
                allHeads.remove(readCellData.getStringValue());
            }
        }
        headList.removeAll(allHeads);
        if(headList.size()>0){
            JSONObject responseJson = new JSONObject();
            responseJson.put("errDesc", "headError");
            responseJson.put("errMsg", errMsg);
            throw new ServiceException(responseJson.toJSONString(), 200);
        }
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. It is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ImportLpRepay data, AnalysisContext context) {
        try {
            log.info("解析到一条数据:{}", JSON.toJSONString(data));
            if(StringUtils.isBlank(data.getApplyNo()) || !PatternUtil.patternLoanNo(data.getApplyNo())){//借据号不合格，跳过
                return;
            }
            data.setSheetName(context.readSheetHolder().getSheetName());
            cachedDataList.add(data);
            // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
                // 存储完成清理 list
                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            }
        } catch (Exception e) {
            e.printStackTrace();
            String sheetName = context.readSheetHolder().getSheetName();
            throw new ServiceException("【"+sheetName+"】读取失败");
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
        cachedDataList.clear();
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        List<LawCollCheckRepay> repays = new ArrayList<>();
        cachedDataList.forEach(e->{
            LawCollCheckRepay lawCollCheckRepay = new LawCollCheckRepay();
            BeanUtils.copyProperties(e, lawCollCheckRepay);
            lawCollCheckRepay.setImportIdentify(lawCollCheck.getImportIdentify());
            repays.add(lawCollCheckRepay);
        });
        if(!CollectionUtils.isEmpty(repays)){
            lawCollCheckRepayMapper.insertLawCollCheckRepay(repays);
            log.info("存储数据库成功！");
        }
    }
}
