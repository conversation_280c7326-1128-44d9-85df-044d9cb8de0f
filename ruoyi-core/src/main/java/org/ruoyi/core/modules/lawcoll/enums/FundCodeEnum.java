package org.ruoyi.core.modules.lawcoll.enums;

/**
 * Description:  产品枚举
 * <AUTHOR>
 * @since 2022/08/25 14:44
 */
public enum FundCodeEnum {
    FBBK("富邦银行"),//富邦
    BBWBK("北部湾银行"),//北部湾
    ;

    private String fundName;

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    FundCodeEnum(String fundName) {
        this.fundName = fundName;
    }

    @Override
    public String toString() {
        return "FundCodeEnum{" +
                "fundName='" + fundName + '\'' +
                '}';
    }

    /**
     * 获取产品
     * @param val
     * @return
     */
    public static String getValue(String val){
        for (FundCodeEnum fundCodeEnum: FundCodeEnum.values()) {
            if(fundCodeEnum.getFundName().equals(val)){
                return fundCodeEnum.name();
            }
        }
        return "";
    }
}
