package org.ruoyi.core.modules.fcdataquery.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.modules.fcdataquery.domain.FcExcludeDetail;
import org.ruoyi.core.modules.fcdataquery.mapper.FcExcludeDetailMapper;
import org.ruoyi.core.modules.fcdataquery.service.IFcExcludeDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 财务剔除明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class FcExcludeDetailServiceImpl implements IFcExcludeDetailService
{
    @Autowired
    private FcExcludeDetailMapper fcExcludeDetailMapper;

    /**
     * 查询财务剔除明细
     * 
     * @param id 财务剔除明细主键
     * @return 财务剔除明细
     */
    @Override
    public FcExcludeDetail selectFcExcludeDetailById(Long id)
    {
        return fcExcludeDetailMapper.selectFcExcludeDetailById(id);
    }

    /**
     * 查询财务剔除明细列表
     * 
     * @param fcExcludeDetail 财务剔除明细
     * @return 财务剔除明细
     */
    @Override
    public List<FcExcludeDetail> selectFcExcludeDetailList(FcExcludeDetail fcExcludeDetail)
    {
        return fcExcludeDetailMapper.selectFcExcludeDetailList(fcExcludeDetail);
    }

    /**
     * 新增财务剔除明细
     * 
     * @param fcExcludeDetail 财务剔除明细
     * @return 结果
     */
    @Override
    public int insertFcExcludeDetail(FcExcludeDetail fcExcludeDetail)
    {
        fcExcludeDetail.setCreateTime(DateUtils.getNowDate());
        return fcExcludeDetailMapper.insertFcExcludeDetail(fcExcludeDetail);
    }

    /**
     * 修改财务剔除明细
     * 
     * @param fcExcludeDetail 财务剔除明细
     * @return 结果
     */
    @Override
    public int updateFcExcludeDetail(FcExcludeDetail fcExcludeDetail)
    {
        fcExcludeDetail.setUpdateTime(DateUtils.getNowDate());
        return fcExcludeDetailMapper.updateFcExcludeDetail(fcExcludeDetail);
    }

    /**
     * 批量删除财务剔除明细
     * 
     * @param ids 需要删除的财务剔除明细主键
     * @return 结果
     */
    @Override
    public int deleteFcExcludeDetailByIds(Long[] ids)
    {
        return fcExcludeDetailMapper.deleteFcExcludeDetailByIds(ids);
    }

    /**
     * 删除财务剔除明细信息
     * 
     * @param id 财务剔除明细主键
     * @return 结果
     */
    @Override
    public int deleteFcExcludeDetailById(Long id)
    {
        return fcExcludeDetailMapper.deleteFcExcludeDetailById(id);
    }
}
