package org.ruoyi.core.superviseInformation.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformationUsed;
import org.ruoyi.core.superviseInformation.service.ISuperviseInformationUsedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资料用印Controller
 *
 * <AUTHOR>
 * @date 2023-12-01
 *
 */
@RestController
@RequestMapping("/supervise/information/used")
public class SuperviseInformationUsedController extends BaseController
{
    @Autowired
    private ISuperviseInformationUsedService informationUsedService;

    /**
     * 查询资料用印列表
     */
    //@PreAuthorize("@ss.hasPermi('information:used:list')")
    @GetMapping("/list")
    public TableDataInfo list(SuperviseInformationUsed informationUsed)
    {
        //startPage();
        List<SuperviseInformationUsed> list = informationUsedService.selectInformationUsedList(informationUsed);
        return getDataTable(list);
    }

    /**
     * 导出资料用印列表
     */
    //@PreAuthorize("@ss.hasPermi('information:used:export')")
    @Log(title = "资料用印", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SuperviseInformationUsed informationUsed)
    {
        List<SuperviseInformationUsed> list = informationUsedService.selectInformationUsedList(informationUsed);
        ExcelUtil<SuperviseInformationUsed> util = new ExcelUtil<SuperviseInformationUsed>(SuperviseInformationUsed.class);
        util.exportExcel(response, list, "资料用印数据");
    }

    /**
     * 获取资料用印详细信息
     */
    //@PreAuthorize("@ss.hasPermi('information:used:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(informationUsedService.selectInformationUsedById(id));
    }

    /**
     * 新增资料用印
     */
    //@PreAuthorize("@ss.hasPermi('information:used:add')")
    @Log(title = "资料用印", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SuperviseInformationUsed informationUsed)
    {
        return toAjax(informationUsedService.insertInformationUsed(informationUsed));
    }

    /**
     * 修改资料用印
     */
    //@PreAuthorize("@ss.hasPermi('information:used:edit')")
    @Log(title = "资料用印", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SuperviseInformationUsed informationUsed)
    {
        return toAjax(informationUsedService.updateInformationUsed(informationUsed));
    }

    /**
     * 删除资料用印
     */
    //@PreAuthorize("@ss.hasPermi('information:used:remove')")
    @Log(title = "资料用印", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(informationUsedService.deleteInformationUsedByIds(ids));
    }

}
