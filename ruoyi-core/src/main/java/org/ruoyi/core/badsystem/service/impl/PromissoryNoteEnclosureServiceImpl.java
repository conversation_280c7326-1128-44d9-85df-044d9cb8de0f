package org.ruoyi.core.badsystem.service.impl;

import org.ruoyi.core.badsystem.domain.PromissoryNoteEnclosure;
import org.ruoyi.core.badsystem.mapper.PromissoryNoteEnclosureMapper;
import org.ruoyi.core.badsystem.service.IPromissoryNoteEnclosureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 不良系统-借据-附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class PromissoryNoteEnclosureServiceImpl implements IPromissoryNoteEnclosureService
{
    @Autowired
    private PromissoryNoteEnclosureMapper promissoryNoteEnclosureMapper;

    /**
     * 查询不良系统-借据-附件
     *
     * @param id 不良系统-借据-附件主键
     * @return 不良系统-借据-附件
     */
    @Override
    public PromissoryNoteEnclosure selectPromissoryNoteEnclosureById(Long id)
    {
        return promissoryNoteEnclosureMapper.selectPromissoryNoteEnclosureById(id);
    }

    /**
     * 查询不良系统-借据-附件列表
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 不良系统-借据-附件
     */
    @Override
    public List<PromissoryNoteEnclosure> selectPromissoryNoteEnclosureList(PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        return promissoryNoteEnclosureMapper.selectPromissoryNoteEnclosureList(promissoryNoteEnclosure);
    }

    /**
     * 新增不良系统-借据-附件
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 结果
     */
    @Override
    public int insertPromissoryNoteEnclosure(PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        return promissoryNoteEnclosureMapper.insertPromissoryNoteEnclosure(promissoryNoteEnclosure);
    }

    /**
     * 修改不良系统-借据-附件
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 结果
     */
    @Override
    public int updatePromissoryNoteEnclosure(PromissoryNoteEnclosure promissoryNoteEnclosure)
    {
        return promissoryNoteEnclosureMapper.updatePromissoryNoteEnclosure(promissoryNoteEnclosure);
    }

    /**
     * 批量删除不良系统-借据-附件
     *
     * @param ids 需要删除的不良系统-借据-附件主键
     * @return 结果
     */
    @Override
    public int deletePromissoryNoteEnclosureByIds(Long[] ids)
    {
        return promissoryNoteEnclosureMapper.deletePromissoryNoteEnclosureByIds(ids);
    }

    /**
     * 删除不良系统-借据-附件信息
     *
     * @param id 不良系统-借据-附件主键
     * @return 结果
     */
    @Override
    public int deletePromissoryNoteEnclosureById(Long id)
    {
        return promissoryNoteEnclosureMapper.deletePromissoryNoteEnclosureById(id);
    }

    /**
     * 批量新增不良系统-借据-附件
     *
     * @param promissoryNoteEnclosure 不良系统-借据-附件
     * @return 结果
     */
    public int batchPromissoryNoteEnclosure(List<PromissoryNoteEnclosure> promissoryNoteEnclosure){
        return promissoryNoteEnclosureMapper.batchPromissoryNoteEnclosure(promissoryNoteEnclosure);
    }
}
