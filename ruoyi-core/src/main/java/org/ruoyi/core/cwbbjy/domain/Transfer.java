package org.ruoyi.core.cwbbjy.domain;

import java.util.Map;

/**
 * 传输数据
 */
public class Transfer {
    private String  zhiShiBaoUrl;

    private Map<String, String> mapLists;



    public String getZhiShiBaoUrl() {
        return zhiShiBaoUrl;
    }

    public void setZhiShiBaoUrl(String zhiShiBaoUrl) {
        this.zhiShiBaoUrl = zhiShiBaoUrl;
    }

    public Map<String, String> getMapLists() {
        return mapLists;
    }

    public void setMapLists(Map<String, String> mapLists) {
        this.mapLists = mapLists;
    }

    @Override
    public String toString() {
        return "Transfer{" +
                "zhiShiBaoUrl='" + zhiShiBaoUrl + '\'' +
                ", mapLists=" + mapLists +
                '}';
    }
}
