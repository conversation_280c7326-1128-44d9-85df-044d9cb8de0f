package org.ruoyi.core.xmglproject.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.xmglproject.domain.XmglContactWay;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IXmglContactWayService.java
 * @Description wangzeyu
 * @createTime 2022年12月16日 14:36:00
 */
public interface IXmglContactWayService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public XmglContactWay selectXmglContactWayById(Long id);
    /**
     * 查询【请填写功能名称】列表
     *
     * @param xmglContactWay 【请填写功能名称】
     * @return 【请填写功能名称】
     */

    public List<XmglContactWay> selectXmglContactWayList(XmglContactWay xmglContactWay);

    /**
     * 新增【请填写功能名称】
     *
     * @param xmglContactWay 【请填写功能名称】
     * @return 结果
     */

    public int insertXmglContactWay(XmglContactWay xmglContactWay);
    /**
     * 修改【请填写功能名称】
     *
     * @param xmglContactWay 【请填写功能名称】
     * @return 结果
     */

    public int updateXmglContactWay(XmglContactWay xmglContactWay);
    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteXmglContactWayByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteXmglContactWayById(Long id);
}
