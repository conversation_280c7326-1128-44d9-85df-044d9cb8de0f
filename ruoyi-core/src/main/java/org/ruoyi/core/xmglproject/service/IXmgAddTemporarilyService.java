package org.ruoyi.core.xmglproject.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.CompanyTypeMappingVo;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.ruoyi.core.oasystem.domain.OaProjectDeploy;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.xmglproject.domain.*;

import java.util.List;

public interface IXmgAddTemporarilyService {
    /**
     * 查询新增立项项目临时
     *
     * @param id 新增立项项目临时主键
     * @return 新增立项项目临时
     */
    public XmglAddTemporarily selectXmglAddTemporarilyById(Long id);

    /**
     * 查询新增立项项目临时列表
     *
     * @param xmglAddTemporarily 新增立项项目临时
     * @return 新增立项项目临时集合
     */
    public List<XmglAddTemporarily> selectXmglAddTemporarilyList(XmglAddTemporarily xmglAddTemporarily);

    /**
     * 新增新增立项项目临时表
     *
     * @param xmglAddTemporarily 新增立项项目临时表
     * @return 结果
     */
    public int insertXmglAddTemporarily(XmglAddTemporarily xmglAddTemporarily);

    /**
     * 根据addUuid查询新增过程中创建的数据
     * @param addUuid
     * @return
     */
    List<XmglAddTemporarily> selectXmglAddTemporarilyByAddUuid(String addUuid);

    /**
     * 根据项目名称id查询临时表中是否存在新创建的数据
     * @param deployId
     * @return
     */
    List<XmglAddTemporarily> selectXmglAddTemporarilyByDeployId(Long deployId);

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的项目信息
     * @param deployIdList
     */
    void deleteOaProjectDeployByIds(List<Long> deployIdList);

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的公司信息
     * @param companyIdList
     */
    void deleteCompanyInfoByCompanyIds(List<Long> companyIdList);

    /**
     * 新增立项项目审批不通过时，根据项目名称id删除创建的公司类型信息
     * @param companyTypeList
     */
    void deleteCompanyTypeByIds(List<Long> companyTypeList);

    /**
     * 根据项目名称id集合更新项目名称状态
     * @param deployIdList
     */
    void updateOaProjectDeployCheckStatusByIds(List<Long> deployIdList);

    /**
     * 根据公司id集合更新项目公司状态
     * @param companyIdList
     */
    void updateCompanyCheckStatusByIds(List<Long> companyIdList);

    /**
     * 根据项目id集合查询数据
     * @param deployIdList
     * @return
     */
    List<OaProjectDeploy> selectOaProjectDeployInfoById(List<Long> deployIdList);

    /**
     * 根据公司id集合查询数据
     * @param companyIdList
     * @return
     */
    List<CompanyTypeMappingVo> selectSysCompanyInfoById(List<Long> companyIdList);

    /**
     * 获取当前登陆人有哪些发起流程权限的公司
     * @return
     */
    List<SysCompany> selectLoginCompanyInfoByUserId(Long userId);

    /**
     * 用户发起流程时校验是否有流程模板
     * @param oaProcessTemplate
     * @return
     */
    AjaxResult getXmglProcessFlow(OaProcessTemplate oaProcessTemplate);

    /**
     * 根据公司id集合查询公司信息
     * @param companyIdList
     * @return
     */
    List<SysCompanyVo> selectSysCompanyInfoListById(List<Long> companyIdList);

    /**
     * 根据本次统一批次新增的项目名称id查询立项项目id
     * @param deployIdList
     * @return
     */
    List<XmglDeployProject> selectXmglProjectInfoListByDeployIds(List<Long> deployIdList);

    /**
     * 根据公司id集合获取所有公司的所有公司类型
     * @param allList
     * @return
     */
    List<Long> selectProjectCompanyRelevanceListByIds(List<Long> allList, Long deployId);

    /**
     * 根据项目id查询授权明细表数据
     * @param applyIds
     * @param type
     */
    List<AuthDetail> selectAuthDetailByDeployId(List<Long> applyIds, String type);
}
