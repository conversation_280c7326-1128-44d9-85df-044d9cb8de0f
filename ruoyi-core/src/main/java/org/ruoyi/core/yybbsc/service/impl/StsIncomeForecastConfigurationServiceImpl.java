package org.ruoyi.core.yybbsc.service.impl;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.ruoyi.core.yybbsc.domain.vo.ConfigurationByParameterCodeVo;
import org.ruoyi.core.yybbsc.domain.vo.ConfigurationVo;
import org.ruoyi.core.yybbsc.mapper.StsIncomeForecastConfigurationMapper;
import org.ruoyi.core.yybbsc.service.IStsIncomeForecastConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收入预测报配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
public class StsIncomeForecastConfigurationServiceImpl implements IStsIncomeForecastConfigurationService {
    @Autowired
    private StsIncomeForecastConfigurationMapper stsIncomeForecastConfigurationMapper;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public List<ConfigurationVo> selectStsIncomeForecastConfigurationList() {
        List<ConfigurationVo> configurationVos = stsIncomeForecastConfigurationMapper.selectConfigurationVoList();
        if (configurationVos.size() == 0) {
            ConfigurationVo configurationVo1 = new ConfigurationVo();
            configurationVo1.setParameterName("技术服务费参数");
            configurationVo1.setParameterCode("0");
            configurationVo1.setUpdateTime(null);
            configurationVo1.setLastUpdateUserName(null);
            ConfigurationVo configurationVo2 = new ConfigurationVo();
            configurationVo2.setParameterName("保证金成本参数");
            configurationVo2.setParameterCode("1");
            configurationVo2.setUpdateTime(null);
            configurationVo2.setLastUpdateUserName(null);
            configurationVos.add(configurationVo1);
            configurationVos.add(configurationVo2);
            return configurationVos;
        } else {
            if (configurationVos.size() == 1) {
                ConfigurationVo configurationVo = configurationVos.get(0);
                if ("0".equals(configurationVo.getParameterCode())) {
                    ConfigurationVo configurationVo2 = new ConfigurationVo();
                    configurationVo2.setParameterName("保证金成本参数");
                    configurationVo2.setParameterCode("1");
                    configurationVo2.setUpdateTime(null);
                    configurationVo2.setLastUpdateUserName(null);
                    configurationVos.add(configurationVo2);
                } else if ("1".equals(configurationVo.getParameterCode())){
                    ConfigurationVo configurationVo1 = new ConfigurationVo();
                    configurationVo1.setParameterName("技术服务费参数");
                    configurationVo1.setParameterCode("0");
                    configurationVo1.setUpdateTime(null);
                    configurationVo1.setLastUpdateUserName(null);
                    configurationVos.add(configurationVo1);
//                    configurationVos.add(configurationVo);
//                    configurationVos.remove(0);
                }
            }
            return configurationVos.stream().sorted(Comparator.comparing(ConfigurationVo::getParameterCode)).collect(Collectors.toList());
        }
    }

    @Override
    public List<ConfigurationByParameterCodeVo> selectStsIncomeForecastConfigurationListByParameterCode(String parameterCode) {
        List<ConfigurationByParameterCodeVo> configurationByParameterCodeVos = stsIncomeForecastConfigurationMapper.selectConfigurationByParameterCodeVoListByParameterCode(parameterCode);
        for (ConfigurationByParameterCodeVo cbpcv:configurationByParameterCodeVos) {
            List<String> dateRange = new ArrayList<>();
//            String loanMonth = cbpcv.getLoanMonth();
//            String dateRangeStart = loanMonth.substring(0, 4) + "-" + loanMonth.substring(5, 7);
//            String dateRangeEnd= loanMonth.substring(8, 12) + "-" + loanMonth.substring(13, 15);
            String dateRangeStart = cbpcv.getLoanMonthStart();
            String dateRangeEnd= cbpcv.getLoanMonthEnd();
            dateRange.add(dateRangeStart);
            dateRange.add(dateRangeEnd);
            cbpcv.setDateRange(dateRange);
        }
        return configurationByParameterCodeVos;
    }

    @Override
    public Boolean checkConfiguration(List<ConfigurationByParameterCodeVo> configurations) {
        int a = 0;
        for (ConfigurationByParameterCodeVo cbpcv:configurations) {
            //找到当前对象的开始时间
            String startDate = cbpcv.getDateRange().get(0);
            String endDate = cbpcv.getDateRange().get(1);
            //筛选出不同于本校验对象的其他对象，组成一个list
            List<ConfigurationByParameterCodeVo> collect = configurations.stream().filter(t -> t != cbpcv).collect(Collectors.toList());
            for (ConfigurationByParameterCodeVo otherObj:collect) {
                //对这个list进行遍历，与时间进行对比。对比规则：当前对象的开始时间如果在其他对象的开始和结束时间内，则肯定是错误的。
                if (DateUtil.isIn(DateUtils.parseDate(startDate), DateUtils.parseDate(otherObj.getDateRange().get(0)), DateUtils.parseDate(otherObj.getDateRange().get(1)))
                || DateUtil.isIn(DateUtils.parseDate(endDate), DateUtils.parseDate(otherObj.getDateRange().get(0)), DateUtils.parseDate(otherObj.getDateRange().get(1)))) {
                    a++;
//                    int i = configurations.indexOf(cbpcv);
//                    int i1 = configurations.indexOf(otherObj);
                    break;
                }
            }
        }
        if (a == 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeConfiguration(List<ConfigurationByParameterCodeVo> configurations, LoginUser loginUser) {
        List<ConfigurationByParameterCodeVo> addList = configurations.stream().filter(t -> t.getId() == null).collect(Collectors.toList());
        List<ConfigurationByParameterCodeVo> updateList = configurations.stream().filter(t -> t.getId() != null).collect(Collectors.toList());
        String nickName = loginUser.getUser().getNickName();
//        int rows = 0;
        if (updateList.size() != 0) {
            //更新集合不为0，那么就更新
            //删除不在更新集合内的数据
            List<Long> idList = stsIncomeForecastConfigurationMapper.selectDeleteIdListByParameterCodeAndId(updateList.get(0).getParameterCode(), updateList);
            if (idList.size() != 0) {
                int i = stsIncomeForecastConfigurationMapper.deleteConfigurationByIdList(idList);
            }
            //更新
            updateList.forEach(t -> {
                t.setLoanMonthStart(t.getDateRange().get(0));
                t.setLoanMonthEnd(t.getDateRange().get(1));
                t.setUpdateBy(nickName);
                t.setUpdateTime(DateUtils.getNowDate());
            });
            int rows = stsIncomeForecastConfigurationMapper.updateStsIncomeForecastConfigurationList(updateList);
        }
        if (addList.size() != 0) {
            SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH,false);
            StsIncomeForecastConfigurationMapper mapper = sqlSession.getMapper(StsIncomeForecastConfigurationMapper.class);
            //新增集合不为0，那么就新增
            for (ConfigurationByParameterCodeVo cbpcv:addList) {
                Date nowDate = DateUtils.getNowDate();
                cbpcv.setLoanMonthStart(cbpcv.getDateRange().get(0));
                cbpcv.setLoanMonthEnd(cbpcv.getDateRange().get(1));
                cbpcv.setCreateBy(nickName);
                cbpcv.setUpdateBy(nickName);
                cbpcv.setCreateTime(nowDate);
                cbpcv.setUpdateTime(nowDate);
                int i = mapper.insertStsIncomeForecastConfiguration(cbpcv);
            }
            sqlSession.commit();
            sqlSession.clearCache();
            sqlSession.close();
        }
        return 1;
    }
}
