package org.ruoyi.core.yybbsc.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.util.Date;

public class ImportDataTemplate {
    /**
     * 担保方名称
     */
    @Excel(name = "担保方名称")
    private String guaranteeName;
    /**
     * 资产方名称
     */
    @Excel(name = "资产方名称")
    private String assetName;
    /**
     * 资金方名称   产品名称
     */
    @Excel(name = "资金方名称")
    private String fundName;
    /**
     * 产品名称
     */
    @Excel(name = "产品名称")
    private String productName;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reconDate;

}
