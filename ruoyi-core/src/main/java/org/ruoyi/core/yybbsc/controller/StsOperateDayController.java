package org.ruoyi.core.yybbsc.controller;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.yybbsc.constant.YybbscConstant;
import org.ruoyi.core.yybbsc.domain.StsOperateDay;
import org.ruoyi.core.yybbsc.domain.vo.StsOperateDayVo;
import org.ruoyi.core.yybbsc.service.IStsOperateDayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 每日运营统计Controller
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/yybbsc/day")
public class StsOperateDayController extends BaseController
{
    @Autowired
    private IStsOperateDayService stsOperateDayService;

    /**
     * 查询每日运营报表
     */
    @GetMapping("/list")
    @SuppressWarnings("unchecked")
    public TableDataInfo list(String productNo, Integer pageNum, Integer pageSize, String isAsc, String orderByColumn)
    {
        Map<String, Object> sMap = stsOperateDayService.selectStsOperateDayList(productNo, pageNum, pageSize, isAsc, orderByColumn);
        List<StsOperateDay> resp = (List<StsOperateDay>) sMap.get(YybbscConstant.RESP);
        for (StsOperateDay stsOperateDay : resp) {
            if (StringUtils.isNotEmpty(stsOperateDay.getRemarks())){
                if (stsOperateDay.getRemarks().length()>6){
                    String substring = stsOperateDay.getRemarks().substring(0, 5);
                    stsOperateDay.setRemark(substring+"...");
                }else {
                    stsOperateDay.setRemark(stsOperateDay.getRemarks());
                }
            }
        }
        Long total = (Long) sMap.get(YybbscConstant.TOTAL);
        TableDataInfo pageDataTable = getDataTableByService(resp, total);
        return pageDataTable;
    }

    /**
     * 新增每日运营报表
     */
    @PostMapping
    public AjaxResult add(@RequestBody StsOperateDay stsOperateDay)
    {
        return toAjax(stsOperateDayService.insertStsOperateDay(stsOperateDay));
    }

    /**
     * 导入每日运营统计列表
     */
    // @PreAuthorize("@ss.hasPermi('yybbsc:day:export')")
    @PostMapping("/importData")
    public StsOperateDay importData(MultipartFile file, String reconDate, String productNo) throws Exception
    {
        return stsOperateDayService.stsOperateDayByImportExcel(file, reconDate, productNo);
    }

     /**
      * 导出每日运营统计列表
      */
     @PostMapping("/export")
     public void export(HttpServletResponse response, StsOperateDay stsOperateDay)
     {
         List<StsOperateDay> stsOperateDays = stsOperateDayService.selectStsOperateDayListExport(stsOperateDay.getProductNo(),stsOperateDay.getIsAsc());
         ExcelUtil<StsOperateDay> util = new ExcelUtil<StsOperateDay>(StsOperateDay.class);
         util.exportExcel(response, stsOperateDays, YybbscConstant.MRYYBB);
     }

    /**
     * 核对每日运营统计列表
     */
    @PostMapping("/checkData")
    public List<StsOperateDayVo> checkData(MultipartFile file, String productNo) throws Exception
    {
        return stsOperateDayService.stsOperateDayCheckData(file, productNo);
    }
    /**
     * 修改每日运营报表
     */
    @PutMapping("/updateStsOperateDay")
    @PreAuthorize("@ss.hasPermi('yybbsc:day:updateStsOperateDay')")
    public AjaxResult updateStsOperateDay(@RequestBody StsOperateDay stsOperateDay){
        int length = stsOperateDay.getRemarks().length();
        if (500<length){
            return AjaxResult.error("长度不能超过500");
          }
       stsOperateDayService.updateStsOperateDay(stsOperateDay);
        return AjaxResult.success(stsOperateDay);
    }
}
