package org.ruoyi.core.personnel.domain.vo;

import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import lombok.Data;

import java.util.List;

/**
 * 人员架构
 */
@Data
public class PersonnelOrganizationVo {

    private Long id;
    /**
     * 父级id
     */
    private Long pid;
    /**
     * 前端显示名称
     */
    private String label;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 兼岗
     */
    private List<SysUserPostVo> postList;

    /**
     * 管理人数
     */
    private int numbers;
    /**
     *
     */

    /** 状态（0正常 1停用） */
    private String status;


    private List<PersonnelOrganizationVo> children;
    /**
     * 组织架构 (部门)
     * @param id
     * @param pid
     * @param label
     */
    public PersonnelOrganizationVo(Long id, Long pid, String label,String  deptName,String postName,String companyName,String status,List<SysUserPostVo> postList) {
        this.id = id;
        this.pid = pid;
        this.label = label;
        this.deptName = deptName;
        this.postName = postName;
        this.companyName = companyName;
        this.status = status;
        this.postList = postList;
    }


}
