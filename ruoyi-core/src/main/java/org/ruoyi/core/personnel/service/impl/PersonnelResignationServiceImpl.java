package org.ruoyi.core.personnel.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserPostMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysPostService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import org.ruoyi.core.information.domain.vo.PageUtil;
import org.ruoyi.core.personnel.domain.PersonnelArchives;
import org.ruoyi.core.personnel.domain.PersonnelProcess;
import org.ruoyi.core.personnel.domain.PersonnelResignation;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOnboardingVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelResignationVo;
import org.ruoyi.core.personnel.domain.vo.ProcessEndTime;import org.ruoyi.core.personnel.mapper.PersonnelResignationMapper;
import org.ruoyi.core.personnel.service.IPersonnelArchivesService;
import org.ruoyi.core.personnel.service.IPersonnelProcessService;
import org.ruoyi.core.personnel.service.IPersonnelResignationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 人员离职Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@Service
public class PersonnelResignationServiceImpl implements IPersonnelResignationService
{
    @Autowired
    private PersonnelResignationMapper personnelResignationMapper;
    @Autowired
    private ISysPostService sysPostService;
    @Autowired
    private SysUserRoleMapper userRoleMapper;
    @Autowired
    private IPersonnelArchivesService personnelArchivesService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IPersonnelProcessService personnelProcessService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserPostMapper sysUserPostMapper;
    /**
     * 查询人员离职
     *
     * @param id 人员离职主键
     * @return 人员离职
     */
    @Override
    public PersonnelResignationVo selectPersonnelResignationById(Long id)
    {
        PersonnelResignationVo resignation = personnelResignationMapper.selectPersonnelResignationById(id);
        if (resignation.getHandover() != null){
            String[] numbers = resignation.getHandover().split(",");
            List<Long> handoverList = new ArrayList<>();
            // 将字符串转换为 Long 类型并添加到 List 中
            for (String num : numbers) {
                handoverList.add(Long.parseLong(num));
            }
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(handoverList);
            resignation.setHandoverList(sysUsers);
        }
        return resignation;
    }

    /**
     * 查询人员离职列表
     *
     * @param personnelResignation 人员离职
     * @return 人员离职
     */
    @Override
    public List<PersonnelResignationVo> selectPersonnelResignationList(PersonnelResignationVo personnelResignation)
    {
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        personnelResignation.setDeptIds(dataRange.get("deptIds"));
        personnelResignation.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        //List<SysRole> oneself = roles.stream().filter(role -> "6".equals(role.getDataScope())).collect(Collectors.toList());
        personnelResignation.setCreateBy(loginUser.getUser().getUserName());
//        if(personnelResignation.getDeptIds().isEmpty() && personnelResignation.getUnitIds().isEmpty() && oneself.isEmpty()){
//            return new ArrayList<>();
//        }
        PageUtil.startPage();
        List<PersonnelResignationVo> resignationList = personnelResignationMapper.selectPersonnelResignationList(personnelResignation);
        if (resignationList.isEmpty()){
            return resignationList;
        }
        /**
        * 人员岗位List
        */
        String[] userName = resignationList.stream().map(PersonnelResignationVo::getSysName).toArray(String[]::new);
        if (userName.length != 0){
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName);
            Map<String, List<SysUserPostVo>> userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserName));
            resignationList.forEach(vo -> {
                vo.setPostList(userPostMap.get(vo.getSysName()));
            });
        }
        /**
        * 移交人员名单List
        */
        resignationList.forEach(resignation -> {
            String[] numbers = resignation.getHandover().split(",");
            List<Long> handoverList = new ArrayList<>();
            // 将字符串转换为 Long 类型并添加到 List 中
            for (String num : numbers) {
                handoverList.add(Long.parseLong(num));
            }
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(handoverList);

            resignation.setHandoverList(sysUsers);
        });
        //当审批流程过程中,没有填写离职时间,获取总裁节点审批通过时间
        List<String> processIdList = resignationList.stream().map(PersonnelResignationVo::getProcessId).collect(Collectors.toList());
        List<ProcessEndTime> processEndTimeList = personnelResignationMapper.getProcessEndTime(processIdList);
        Map<String,Date> endTimeMap = processEndTimeList.stream().collect(
                Collectors.toMap(ProcessEndTime::getBusinessKey,ProcessEndTime::getEndTime,
                                                (existingValue, newValue) -> newValue)
                );
        resignationList.forEach(
                resignation -> {
                    if ("1".equals(resignation.getAuditState()) && resignation.getRatifyTime() == null){
                        resignation.setRatifyTime(endTimeMap.get(resignation.getProcessId()));
                    }
                }
        );
        return resignationList;
    }

    /**
     * 新增人员离职
     *
     * @param personnelResignation 人员离职
     * @return 结果
     */
    @Override
    public AjaxResult insertPersonnelResignation(PersonnelResignationVo personnelResignation)
    {
        if(personnelResignation.getId() == null) {
            personnelResignation.setCreateTime(DateUtils.getNowDate());
            personnelResignation.setCreateBy(getUsername());
            int count = personnelResignationMapper.getCountByCreateTime(DateUtils.getDate()) + 1;
            String createTimeNum = DateUtils.dateTimeNow("yyyyMMdd");
            personnelResignation.setResignationCode("LZ" + createTimeNum + String.format("%03d", count));
            personnelResignation.setSubmitState("0");
            personnelResignation.setAuditState("2");
            int i = personnelResignationMapper.insertPersonnelResignation(personnelResignation);
            return AjaxResult.success(personnelResignation);
        } else {
            int i = updatePersonnelResignation(personnelResignation);
            return AjaxResult.success(personnelResignation);
        }
    }

    /**
     * 修改人员离职
     *
     * @param personnelResignation 人员离职
     * @return 结果
     */
    @Override
    public int updatePersonnelResignation(PersonnelResignation personnelResignation)
    {
        personnelResignation.setUpdateTime(DateUtils.getNowDate());
        personnelResignation.setUpdateBy(getUsername());
        return personnelResignationMapper.updatePersonnelResignation(personnelResignation);
    }

    /**
     * 批量删除人员离职
     *
     * @param ids 需要删除的人员离职主键
     * @return 结果
     */
    @Override
    public int deletePersonnelResignationByIds(Long[] ids)
    {
        return personnelResignationMapper.deletePersonnelResignationByIds(ids);
    }

    /**
     * 删除人员离职信息
     *
     * @param id 人员离职主键
     * @return 结果
     */
    @Override
    public int deletePersonnelResignationById(Long id)
    {
        return personnelResignationMapper.deletePersonnelResignationById(id);
    }

    @Override
    @Transactional
    public int passResignationById(Long id){
        PersonnelResignation resignation = selectPersonnelResignationById(id);
        String[] numbers = resignation.getHandover().split(",");List<Long> handoverList = new ArrayList<>();
        // 将字符串转换为 Long 类型并添加到 List 中
        for (String num : numbers) {
            handoverList.add(Long.parseLong(num));
        }

//        String[] userName = {resignation.getSysName()};
//        List<SysUserRole> postListByUserName =
//        List<SysUserRole> userRoleList = userRoleMapper.getRoleListByUserName(userName);
//
//        //查询接交人的角色信息
//        List<SysUserRole> sysUserRoles = userRoleMapper.selectUserRoleInfo(handoverList.toArray(new Long[0]));
//        //去掉他们重复的部分
//        List<SysUserRole> duplicates = userRoleList.stream()
//                .filter(role -> sysUserRoles.stream()
//                        .noneMatch(sysRole -> Objects.equals(role.getUserId(), sysRole.getUserId())
//                                && Objects.equals(role.getRoleId(), sysRole.getRoleId()))).collect(Collectors.toList());
//
//        if (!duplicates.isEmpty()){
//            userRoleMapper.batchUserRole(duplicates);
//        }
        //修改人员档案状态
        PersonnelArchives personnelArchives = new PersonnelArchives();
        personnelArchives.setPersonnelState("3");
        personnelArchives.setSysName(resignation.getSysName());
        personnelArchives.setUpdateBy(getUsername());
        personnelArchives.setUpdateTime(DateUtils.getNowDate());
        personnelArchivesService.updatePersonnelStateBySysName(personnelArchives);
        //账号状态状态关闭
        SysUser sysUser = sysUserService.selectUserByUserNameOfTrue(resignation.getSysName());
        if (sysUser != null) {
            sysUser.setStatus("1");
            sysUser.setUpdateBy(getUsername());
            sysUser.setUpdateTime(DateUtils.getNowDate());
           // sysUserService.updateUser(sysUser);
            // 删除用户与岗位关联
            //sysUserPostMapper.deleteUserPostByUserId(sysUser.getUserId());
            //更新 user
            sysUserMapper.updateUser(sysUser);
        }
        return personnelResignationMapper.passResignationById(id);
    }

    @Override
    public int unpassResignationById(Long id){
        PersonnelResignation resignation = selectPersonnelResignationById(id);
        PersonnelArchives personnelArchives = new PersonnelArchives();
        personnelArchives.setPersonnelState("1");
        personnelArchives.setSysName(resignation.getSysName());
        personnelArchives.setUpdateBy(getUsername());
        personnelArchives.setUpdateTime(DateUtils.getNowDate());
        personnelArchivesService.updatePersonnelStateBySysName(personnelArchives);

        PersonnelProcess personnelProcess = new PersonnelProcess();
        personnelProcess.setCorrelationId(id);
        personnelProcess.setProcessType("5");
        personnelProcessService.abandonedProcess(personnelProcess);
        return personnelResignationMapper.unpassResignationById(id);
    }

    @Override
    public int commitResignationProcess(Long id){
        PersonnelResignation resignation = selectPersonnelResignationById(id);
        PersonnelArchives personnelArchives = new PersonnelArchives();
        personnelArchives.setPersonnelState("2");
        personnelArchives.setSysName(resignation.getSysName());
        personnelArchives.setUpdateBy(getUsername());
        personnelArchives.setUpdateTime(DateUtils.getNowDate());
        personnelArchivesService.updatePersonnelStateBySysName(personnelArchives);

        return personnelResignationMapper.commitResignationProcess(id);
    }

    @Override
    public List<PersonnelResignationVo> exportList(PersonnelResignationVo personnelResignation){
        if (!"[]".equals(personnelResignation.getIds())){
            personnelResignation.setIds(personnelResignation.getIds().substring(1, personnelResignation.getIds().length() - 1));  // 去除首尾的方括号
            String[] array = personnelResignation.getIds().split(",");
            List<Long> list = new ArrayList<>();
            for (String s : array) {
                list.add(Long.parseLong(s.replaceAll("\"", "")));
            }
            personnelResignation.setIdArray(list);
        }
        //数据范围
        LoginUser loginUser = getLoginUser();
        Map<String, List<Long>> dataRange = personnelArchivesService.getDataRange(loginUser);
        personnelResignation.setDeptIds(dataRange.get("deptIds"));
        personnelResignation.setUnitIds(dataRange.get("unitIds"));
        List<SysRole> roles = loginUser.getUser().getRoles();
        //List<SysRole> oneself = roles.stream().filter(role -> "6".equals(role.getDataScope())).collect(Collectors.toList());
        personnelResignation.setCreateBy(loginUser.getUser().getUserName());
        List<PersonnelResignationVo> resignationList = personnelResignationMapper.selectPersonnelResignationList(personnelResignation);
        if (resignationList.isEmpty()){
            return resignationList;
        }

        String[] userName = resignationList.stream().map(PersonnelResignationVo::getSysName).toArray(String[]::new);
        if (userName.length != 0){
            List<SysUserPostVo> postListByUserName = sysPostService.getPostListByUserName(userName);
            Map<String, List<SysUserPostVo>> userPostMap = postListByUserName.stream().collect(Collectors.groupingBy(SysUserPostVo::getUserName));
            resignationList.forEach(vo -> {
                vo.setPostList(userPostMap.get(vo.getSysName()));
            });
        }
        resignationList.forEach(resignation -> {
            String[] numbers = resignation.getHandover().split(",");
            List<Long> handoverList = new ArrayList<>();
            // 将字符串转换为 Long 类型并添加到 List 中
            for (String num : numbers) {
                handoverList.add(Long.parseLong(num));
            }
            List<SysUser> sysUsers = sysUserService.selectUserByUserIds(handoverList);
            StringBuffer userNames = new StringBuffer();
            sysUsers.forEach(user -> {
                userNames.append(user.getNickName()).append(",");

            });
            resignation.setHandover(userNames.toString());

            if ("1".equals(resignation.getAuditState())){
                resignation.setAuditState("审核通过");
            } else if ("2".equals(resignation.getAuditState())){
                resignation.setAuditState("未审核");
            } else if ("3".equals(resignation.getAuditState())){
                resignation.setAuditState("审核不通过");
            } else if ("4".equals(resignation.getAuditState())){
                resignation.setAuditState("审核中");
            }

            if ("0".equals(resignation.getSubmitState())){
                resignation.setSubmitState("未提交");
            } else {
                resignation.setSubmitState("已提交");
            }
            if ("0".equals(resignation.getPersonnelType())){
                resignation.setPersonnelType("业务类");
            } else {
                resignation.setPersonnelType("非业务类");
            }
        });

        return resignationList;

    }
}
