package org.ruoyi.core.personnel.mapper;

import org.ruoyi.core.personnel.domain.PersonnelProcess;
import org.ruoyi.core.personnel.domain.vo.PersonnelProcessVo;

import java.util.List;

/**
 * 人员流程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface PersonnelProcessMapper
{
    /**
     * 查询人员流程
     *
     * @param id 人员流程主键
     * @return 人员流程
     */
    public PersonnelProcess selectPersonnelProcessById(Long id);

    public PersonnelProcess getByProcessId(String id);

    /**
     * 查询人员流程列表
     *
     * @param personnelProcess 人员流程
     * @return 人员流程集合
     */
    public List<PersonnelProcess> selectPersonnelProcessList(PersonnelProcess personnelProcess);

    /**
     * 新增人员流程
     *
     * @param personnelProcess 人员流程
     * @return 结果
     */
    public int insertPersonnelProcess(PersonnelProcess personnelProcess);

    /**
     * 修改人员流程
     *
     * @param personnelProcess 人员流程
     * @return 结果
     */
    public int updatePersonnelProcess(PersonnelProcess personnelProcess);

    /**
     * 删除人员流程
     *
     * @param id 人员流程主键
     * @return 结果
     */
    public int deletePersonnelProcessById(Long id);

    /**
     * 批量删除人员流程
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonnelProcessByIds(Long[] ids);

    /**
     * 查询人员流程列表
     *
     * @param personnelProcess 人员流程
     * @return 人员流程集合
     */
    public List<PersonnelProcessVo> getPersonnelProcessList(PersonnelProcess personnelProcess);

    public List<PersonnelProcessVo> getProcessList(PersonnelProcess personnelProcess);
    /**
     * 修改人员流程
     *
     * @param personnelProcess 人员流程
     * @return 结果
     */
    public int completeProcessByProcessId(PersonnelProcess personnelProcess);

    public int abandonedProcess(PersonnelProcess personnelProcess);

    public int draftSubmit(PersonnelProcess personnelProcess);
}
