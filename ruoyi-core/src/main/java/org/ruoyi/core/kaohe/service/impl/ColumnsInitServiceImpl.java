package org.ruoyi.core.kaohe.service.impl;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.kaohe.domain.ColumnsInit;
import org.ruoyi.core.kaohe.mapper.ColumnsInitMapper;
import org.ruoyi.core.kaohe.service.IColumnsInitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 字段显示Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@Service
public class ColumnsInitServiceImpl implements IColumnsInitService
{
    @Autowired
    private ColumnsInitMapper columnsInitMapper;

    /**
     * 查询字段显示
     *
     * @param id 字段显示主键
     * @return 字段显示
     */
    @Override
    public ColumnsInit selectColumnsInitById(Long id)
    {
        return columnsInitMapper.selectColumnsInitById(id);
    }

    /**
     * 查询字段显示列表
     *
     * @param columnsInit 字段显示
     * @return 字段显示
     */
    @Override
    public List<ColumnsInit> selectColumnsInitList(ColumnsInit columnsInit)
    {
        return columnsInitMapper.selectColumnsInitList(columnsInit);
    }

    /**
    * 查询业绩录入的字段
    * @param columnsInit
    * @return
    */
    @Override
    public List<ColumnsInit> selectColumnsInitEnterList(ColumnsInit columnsInit)
    {
        columnsInit.setModule("1");
        columnsInit.setCreateBy(getLoginUser().getUsername());
        List<ColumnsInit> columnsInits = columnsInitMapper.selectColumnsInitList(columnsInit);
        if (columnsInits.isEmpty()) {
            List<ColumnsInit> batchColumnsInits = new ArrayList<>();
            String[] columns = {"companyName","yewuList",
                                "m1TotalIndex","m1DistributionIndex","m1ExtensionIndex",
                                "m2TotalIndex","m2DistributionIndex","m2ExtensionIndex",
                                "m3TotalIndex","m3DistributionIndex","m3ExtensionIndex",
                                "m4TotalIndex","m4DistributionIndex","m4ExtensionIndex",
                                "m5TotalIndex","m5DistributionIndex","m5ExtensionIndex",
                                "m6TotalIndex","m6DistributionIndex","m6ExtensionIndex",
                                "m7TotalIndex","m7DistributionIndex","m7ExtensionIndex",
                                "m8TotalIndex","m8DistributionIndex","m8ExtensionIndex",
                                "m9TotalIndex","m9DistributionIndex","m9ExtensionIndex",
                                "m10TotalIndex","m10DistributionIndex","m10ExtensionIndex",
                                "m11TotalIndex","m11DistributionIndex","m11ExtensionIndex",
                                "m12TotalIndex","m12DistributionIndex","m12ExtensionIndex"};
            for (String column : columns) {
                ColumnsInit newColumnsInit = new ColumnsInit();
                newColumnsInit.setModule("1");
                newColumnsInit.setColumns(column);
                newColumnsInit.setVisible(true);
                newColumnsInit.setCreateBy(getLoginUser().getUsername());
                newColumnsInit.setCreateTime(DateUtils.getNowDate());
                batchColumnsInits.add(newColumnsInit);
            }
            columnsInitMapper.batchColumnsInit(batchColumnsInits);
            columnsInits = columnsInitMapper.selectColumnsInitList(columnsInit);
        }
        return columnsInits;
    }

    /**
    * 查询业绩录入的字段
    * @param columnsInit
    * @return
    */
    @Override
    public List<ColumnsInit> selectColumnsCheckResultList(ColumnsInit columnsInit)
    {
        columnsInit.setModule("2");
        columnsInit.setCreateBy(getLoginUser().getUsername());
        List<ColumnsInit> columnsInits = columnsInitMapper.selectColumnsInitList(columnsInit);
        if (columnsInits.isEmpty()) {
            List<ColumnsInit> batchColumnsInits = new ArrayList<>();
            String[] columns = { "year","quarter","deptNameBelong","companyShortNameBelong","totalIndex","distributionIndex","extensionIndex",
                                 "extensionBank","achievementWagesProportion","distributionProportion",
                                 "extensionProportion","completeTotalIndex","projectSalaryTotalProportion",
                                 "completeDistributionIndex","projectSalaryDistributionProportion",
                                 "distributionIndexDeviation","calibrationDistributionIndex","calibrationDistributionIndexState",
                                 "calibrationProjectSalaryDistributionProportion","completeExtensionIndex",
                                 "projectSalaryExtensionProportion","extensionIndexDeviation","calibrationExtensionIndex",
                                 "calibrationProjectSalaryExtensionProportion","completeExtensionBank",
                                 "bankSalaryExtensionProportion","extensionBankDeviation","calibrationExtensionBank",
                                 "calibrationExtensionIndexState","calibrationProjectSalaryBankProportion",
                                 "calibrationProjectSalaryTotalProportion"
            };
            for (String column : columns) {
                ColumnsInit newColumnsInit = new ColumnsInit();
                newColumnsInit.setModule("2");
                newColumnsInit.setColumns(column);
                newColumnsInit.setVisible(true);
                newColumnsInit.setCreateBy(getLoginUser().getUsername());
                newColumnsInit.setCreateTime(DateUtils.getNowDate());
                batchColumnsInits.add(newColumnsInit);
            }
            columnsInitMapper.batchColumnsInit(batchColumnsInits);
            columnsInits = columnsInitMapper.selectColumnsInitList(columnsInit);
        }
        return columnsInits;
    }

    /**
     * 新增字段显示
     *
     * @param columnsInit 字段显示
     * @return 结果
     */
    @Override
    public int insertColumnsInit(ColumnsInit columnsInit)
    {
        columnsInit.setCreateTime(DateUtils.getNowDate());
        return columnsInitMapper.insertColumnsInit(columnsInit);
    }

    @Override
    public int replaceColumnsInit(List<ColumnsInit> columnsInit)
    {
        return columnsInitMapper.replaceColumnsInit(columnsInit);
    }

    /**
     * 修改字段显示
     *
     * @param columnsInit 字段显示
     * @return 结果
     */
    @Override
    public int updateColumnsInit(ColumnsInit columnsInit)
    {
        columnsInit.setUpdateTime(DateUtils.getNowDate());
        return columnsInitMapper.updateColumnsInit(columnsInit);
    }

    /**
     * 批量删除字段显示
     *
     * @param ids 需要删除的字段显示主键
     * @return 结果
     */
    @Override
    public int deleteColumnsInitByIds(Long[] ids)
    {
        return columnsInitMapper.deleteColumnsInitByIds(ids);
    }

    /**
     * 删除字段显示信息
     *
     * @param id 字段显示主键
     * @return 结果
     */
    @Override
    public int deleteColumnsInitById(Long id)
    {
        return columnsInitMapper.deleteColumnsInitById(id);
    }
}
