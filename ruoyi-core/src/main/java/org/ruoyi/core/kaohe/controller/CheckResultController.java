package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.ruoyi.core.kaohe.domain.util.ExcelUtilCheck;
import org.ruoyi.core.kaohe.domain.vo.CheckResultExcel;
import org.ruoyi.core.kaohe.domain.vo.CheckResultVo;
import org.ruoyi.core.kaohe.service.ICheckResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 考核结果Controller
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@RestController
@RequestMapping("/check/result")
public class CheckResultController extends BaseController {
    @Autowired
    private ICheckResultService checkResultService;

    /**
     * 查询考核结果列表
     */
    //@PreAuthorize("@ss.hasPermi('system:result:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckResultVo checkResult) {
        //startPage();
        List<CheckResultVo> list = checkResultService.selectCheckResultList(checkResult);
        return getDataTable(list);
    }

    @GetMapping("/selectCheckResultListofQ3")
    public TableDataInfo selectCheckResultListofQ3(CheckResultVo checkResult) {
        //startPage();
        List<CheckResultVo> list = checkResultService.selectCheckResultListofQ3(checkResult);
        return getDataTable(list);
    }

    /**
     * 导出考核结果列表
     */
    //@PreAuthorize("@ss.hasPermi('system:result:export')")
    @Log(title = "考核结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckResultVo checkResult) {

        List<CheckResultExcel> list = checkResultService.getCheckResultExcel(checkResult);
        ExcelUtilCheck<CheckResultExcel> util = new ExcelUtilCheck<CheckResultExcel>(CheckResultExcel.class);
        if ("1".equals(checkResult.getType())) {
            util.hideColumn("companyShortNameBelong","deptName","nickName","deptNameBelong");
        } else if ("2".equals(checkResult.getType())) {
            util.hideColumn("companyName","deptNameBelong","nickName");
        } else if ("3".equals(checkResult.getType())) {
            util.hideColumn("companyName","companyShortNameBelong","deptName");
        }
        util.exportExcel(response, list, "考核结果数据");
    }

    @Log(title = "考核结果", businessType = BusinessType.EXPORT)
    @PostMapping("/process/export")
    public void processExport(HttpServletResponse response, CheckResultVo checkResult) {

        List<CheckResultVo> list = checkResultService.selectCheckResultsProcess(checkResult);

        HashMap<String, String> map = new HashMap<>();
        map.put("1","已完成");
        map.put("2","未完成");
        map.put("3","已完成 - 项目校准");
        map.put("4","未完成 - 项目校准");

        list.forEach( vo -> {
            if(vo.getCalibrationProjectSalaryExtensionProportion() != null && vo.getCalibrationProjectSalaryDistributionProportion() == null){
                vo.setCalibrationProjectSalaryDistributionProportionAll(vo.getCalibrationProjectSalaryExtensionProportion());
            }
            if(vo.getCalibrationProjectSalaryExtensionProportion() == null && vo.getCalibrationProjectSalaryDistributionProportion() != null){
                vo.setCalibrationProjectSalaryDistributionProportionAll(vo.getCalibrationProjectSalaryDistributionProportion());
            }
            if(vo.getCalibrationProjectSalaryExtensionProportion() != null && vo.getCalibrationProjectSalaryDistributionProportion() != null){
                vo.setCalibrationProjectSalaryDistributionProportionAll(vo.getCalibrationProjectSalaryDistributionProportion());
            }
            vo.setCalibrationDistributionIndexState(map.get(vo.getCalibrationDistributionIndexState()));

            if (vo.getCalibrationExtensionIndexBankState() != null && vo.getCalibrationExtensionIndexProjectState() != null) {
                String temp = "";

                if (vo.getCalibrationExtensionIndexBankState().contains("已完成") && vo.getCalibrationExtensionIndexProjectState().contains("已完成")) {
                    String partA = vo.getCalibrationExtensionIndexProjectState().split("-")[1];
                    String partB = vo.getCalibrationExtensionIndexBankState().split("-")[1];
                    Set<String> uniqueParts = new HashSet<>();
                    uniqueParts.add(partA);
                    uniqueParts.add(partB);
                    temp = "已完成-" + String.join(",", uniqueParts);

                } else if (vo.getCalibrationExtensionIndexBankState().contains("已完成") ||
                        vo.getCalibrationExtensionIndexProjectState().contains("已完成")) {

                    temp = vo.getCalibrationExtensionIndexBankState() != null ? vo.getCalibrationExtensionIndexBankState() : vo.getCalibrationExtensionIndexProjectState();

                } else {
                    temp = "未完成";
                }

                vo.setCalibrationExtensionIndexState(temp);

            } else {
                String temp = vo.getCalibrationExtensionIndexBankState() != null ?
                        vo.getCalibrationExtensionIndexBankState() :
                        vo.getCalibrationExtensionIndexProjectState();

                vo.setCalibrationExtensionIndexState(temp);
            }


        });
        ExcelUtilCheck<CheckResultVo> util = new ExcelUtilCheck<CheckResultVo>(CheckResultVo.class);
        if ("1".equals(checkResult.getType())) {
            util.hideColumn("companyShortNameBelong","deptName","nickName","deptNameBelong");
        } else if ("2".equals(checkResult.getType())) {
            util.hideColumn("companyShortName","deptNameBelong","nickName");
        } else if ("3".equals(checkResult.getType())) {
            util.hideColumn("companyShortName","companyShortNameBelong","deptName");
        }
        util.exportExcel(response, list, "考核结果数据");
    }

    /**
     * 获取考核结果详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:result:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(checkResultService.selectCheckResultById(id));
    }

    /**
     * 新增考核结果
     */
    //@PreAuthorize("@ss.hasPermi('system:result:add')")
    @Log(title = "考核结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.insertCheckResult(checkResult));
    }

    @GetMapping(value = "/getCheckResult")
    public AjaxResult getCheckResult(CheckResultVo checkResult) {
        return AjaxResult.success(checkResultService.getCheckResult(checkResult));
    }

    /**
     * 修改考核结果
     */
    //@PreAuthorize("@ss.hasPermi('system:result:edit')")
    @Log(title = "考核结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.updateCheckResult(checkResult));
    }

    /**
     * 删除考核结果
     */
    //@PreAuthorize("@ss.hasPermi('system:result:remove')")
    @Log(title = "考核结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkResultService.deleteCheckResultByIds(ids));
    }

    @Log(title = "批量修改考核结果", businessType = BusinessType.INSERT)
    @PostMapping("/replaceCheckResult")
    public AjaxResult replaceCheckResult(@RequestBody List<CheckResultVo> checkResults)
    {
        return toAjax(checkResultService.replaceCheckResult(checkResults));
    }

    @GetMapping(value = "/getCheckResultTotal")
    public AjaxResult getCheckResultTotal(CheckResultVo checkResult) {
        return AjaxResult.success(checkResultService.getCheckResultTotal(checkResult));
    }

    /**
     * 考核结果确认 list
     * @param checkResult
     * @return
     */
    @GetMapping(value = "/getCompanyCheckResultList")
    public AjaxResult getCompanyCheckResultList(CheckResultVo checkResult) {
        return AjaxResult.success(checkResultService.getCompanyCheckResultList(checkResult));
    }

    @Log(title = "新增考核结果流程", businessType = BusinessType.INSERT)
    @PostMapping("/insertCheckResultProcess")
    public AjaxResult insertCheckResultProcess(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.insertCheckResultProcess(checkResult));
    }

    @Log(title = "通过考核结果", businessType = BusinessType.UPDATE)
    @PutMapping("/passCheckResult")
    public AjaxResult passCheckResult(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.passCheckResult(checkResult));
    }

    @Log(title = "废弃考核结果", businessType = BusinessType.UPDATE)
    @PutMapping("/unpassCheckResult")
    public AjaxResult unpassCheckResult(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.unpassCheckResult(checkResult));
    }

    /**
     * 重置考核结果
     * @param
     * @return
     */
    @Log(title = "考核结果", businessType = BusinessType.DELETE)
    @PutMapping("/resetCheckResult")
    public AjaxResult resetCheckResult(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.resetCheckResult(checkResult));
    }

    @GetMapping("/selectCheckResultNoAuditing")
    public TableDataInfo selectCheckResultNoAuditing(CheckResultVo checkResult) {
        List<CheckResultVo> list = checkResultService.selectCheckResultNoAuditing(checkResult);
        return getDataTable(list);
    }

    @GetMapping("/selectCheckResultsProcess")
    public TableDataInfo selectCheckResultsProcess(CheckResultVo checkResult) {
        List<CheckResultVo> list = checkResultService.selectCheckResultsProcess(checkResult);
        return getDataTable(list);
    }

    @GetMapping(value = "/getExistence")
    public AjaxResult getExistence(CheckResultVo checkResult) {
        return AjaxResult.success(checkResultService.getExistence(checkResult));
    }

    @Log(title = "考核结果生成部门用户数据", businessType = BusinessType.UPDATE)
    @PostMapping("/generateDeptUserCheckResult")
    public AjaxResult generateDeptUserCheckResult(@RequestBody CheckResultVo checkResult) {
        return toAjax(checkResultService.generateDeptUserCheckResult(checkResult));
    }
}
