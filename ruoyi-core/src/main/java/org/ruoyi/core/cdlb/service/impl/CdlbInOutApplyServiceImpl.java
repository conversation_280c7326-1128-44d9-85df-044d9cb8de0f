package org.ruoyi.core.cdlb.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import org.ruoyi.core.cdlb.domain.CdlbInOutApply;
import org.ruoyi.core.cdlb.mapper.CdlbInOutApplyMapper;
import org.ruoyi.core.cdlb.service.ICdlbInOutApplyService;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车贷绿本出入库申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@Service
public class CdlbInOutApplyServiceImpl implements ICdlbInOutApplyService {


    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private CdlbInOutApplyMapper cdlbInOutApplyMapper;
    /**
     * 查询车贷绿本出入库申请
     *
     * @param id 车贷绿本出入库申请主键
     * @return 车贷绿本出入库申请
     */
    @Override
    public CdlbInOutApply selectCdlbInOutApplyByIdA(Long id) {

     return    cdlbInOutApplyMapper.selectCdlbInOutApplyById(id);
    }
    @Override
    public CdlbInOutApply selectCdlbInOutApplyByIdDto(Long id) {

     return    cdlbInOutApplyMapper.selectCdlbInOutApplyByIdDto(id);
    }
    /**
     * 查询车贷绿本出入库申请
     *
     * @param id 车贷绿本出入库申请主键
     * @return 车贷绿本出入库申请
     */
    @Override
    public CdlbInOutApply selectCdlbInOutApplyById(Long id) {

        CdlbInOutApply inOutApply = cdlbInOutApplyMapper.selectCdlbInOutApplyById(id);


        if (StringUtils.isNotEmpty(inOutApply.getUser10Id())){
            int i = inOutApply.getUser10Id().indexOf(",");
            if (i!=-1){
                String[] split = inOutApply.getUser10Id().split(",");
                String names = "";
                for (int j = 0; j < split.length; j++) {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        names+=sysUser.getNickName()+",";
                    }
                }
                int i1 = names.indexOf(",");
                if(i1!=-1){
                    names=    names.substring(0,names.length()-1);
                }
                inOutApply.setUser10Name(names);
            }else {
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser10Id()));
                if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                    inOutApply.setUser10Name(sysUser.getNickName());
                }
            }


        }
        if (StringUtils.isNotEmpty(inOutApply.getUser11Id())){
            int i = inOutApply.getUser11Id().indexOf(",");
            if (i!=-1){
                String[] split = inOutApply.getUser11Id().split(",");
                String names = "";
                for (int j = 0; j < split.length; j++) {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        names+=sysUser.getNickName()+",";
                    }
                }
                int i1 = names.indexOf(",");
                if(i1!=-1){
                    names=    names.substring(0,names.length()-1);
                }
                inOutApply.setUser11Name(names);
            }else {
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser11Id()));
                if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                    inOutApply.setUser11Name(sysUser.getNickName());
                }
            }


        }
        if (StringUtils.isNotEmpty(inOutApply.getUser12Id())){
            int i = inOutApply.getUser12Id().indexOf(",");
            if (i!=-1){
                String[] split = inOutApply.getUser12Id().split(",");
                String names = "";
                for (int j = 0; j < split.length; j++) {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        names+=sysUser.getNickName()+",";
                    }
                }
                int i1 = names.indexOf(",");
                if(i1!=-1){
                    names=    names.substring(0,names.length()-1);
                }
                inOutApply.setUser12Name(names);
            }else {
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser12Id()));
                if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                    inOutApply.setUser12Name(sysUser.getNickName());
                }
            }


        }

        if (StringUtils.isNotEmpty(inOutApply.getUser21Id())){
            int i = inOutApply.getUser21Id().indexOf(",");
            if (i!=-1){
                String[] split = inOutApply.getUser21Id().split(",");
                String names = "";
                for (int j = 0; j < split.length; j++) {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        names+=sysUser.getNickName()+"，";
                    }
                }
                int i1 = names.indexOf("，");
                if(i1!=-1){
                    names=    names.substring(0,names.length()-1);
                }
                inOutApply.setUser21Name(names);
            }else {
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser21Id()));
                if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                    inOutApply.setUser21Name(sysUser.getNickName());
                }
            }


        }
        if (StringUtils.isNotEmpty(inOutApply.getUser22Id())){
            int i = inOutApply.getUser22Id().indexOf(",");
            if (i!=-1){
                String[] split = inOutApply.getUser22Id().split(",");
                String names = "";
                for (int j = 0; j < split.length; j++) {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        names+=sysUser.getNickName()+"，";
                    }
                }
                int i1 = names.indexOf("，");
                if(i1!=-1){
                    names=    names.substring(0,names.length()-1);
                }
                inOutApply.setUser22Name(names);
            }else {
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser22Id()));
                if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                    inOutApply.setUser22Name(sysUser.getNickName());
                }
            }


        }
        if (StringUtils.isNotEmpty(inOutApply.getUser23Id())){
            int i = inOutApply.getUser23Id().indexOf(",");
            if (i!=-1){
                String[] split = inOutApply.getUser23Id().split(",");
                String names = "";
                for (int j = 0; j < split.length; j++) {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        names+=sysUser.getNickName()+"，";
                    }
                }
                int i1 = names.indexOf("，");
                if(i1!=-1){
                    names=    names.substring(0,names.length()-1);
                }
                inOutApply.setUser23Name(names);
            }else {
                SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser23Id()));
                if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                    inOutApply.setUser23Name(sysUser.getNickName());
                }
            }


        }

        return  inOutApply;
    }

    /**
     * 查询车贷绿本出入库申请列表
     *
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 车贷绿本出入库申请
     */
    @Override
    public List<CdlbInOutApply> selectCdlbInOutApplyList(CdlbInOutApply cdlbInOutApply) {
        List<CdlbInOutApply> cdlbInOutApplies = cdlbInOutApplyMapper.selectCdlbInOutApplyList(cdlbInOutApply);
        for (CdlbInOutApply inOutApply : cdlbInOutApplies) {

            if (StringUtils.isNotEmpty(inOutApply.getUser10Id())){
                int i = inOutApply.getUser10Id().indexOf(",");
                if (i!=-1){
                    String[] split = inOutApply.getUser10Id().split(",");
                   String names = "";
                    for (int j = 0; j < split.length; j++) {
                        SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                        if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                            names+=sysUser.getNickName()+"，";
                        }
                    }
                    int i1 = names.indexOf("，");
                    if(i1!=-1){
                        names=    names.substring(0,names.length()-1);
                    }
                    inOutApply.setUser10Name(names);
                }else {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser10Id()));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        inOutApply.setUser10Name(sysUser.getNickName());
                    }
                }


            }
            if (StringUtils.isNotEmpty(inOutApply.getUser11Id())){
                int i = inOutApply.getUser11Id().indexOf(",");
                if (i!=-1){
                    String[] split = inOutApply.getUser11Id().split(",");
                    String names = "";
                    for (int j = 0; j < split.length; j++) {
                        SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                        if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                            names+=sysUser.getNickName()+"，";
                        }
                    }
                    int i1 = names.indexOf("，");
                    if(i1!=-1){
                        names=    names.substring(0,names.length()-1);
                    }
                    inOutApply.setUser11Name(names);
                }else {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser11Id()));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        inOutApply.setUser11Name(sysUser.getNickName());
                    }
                }


            }
            if (StringUtils.isNotEmpty(inOutApply.getUser12Id())){
                int i = inOutApply.getUser12Id().indexOf(",");
                if (i!=-1){
                    String[] split = inOutApply.getUser12Id().split(",");
                    String names = "";
                    for (int j = 0; j < split.length; j++) {
                        SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                        if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                            names+=sysUser.getNickName()+"，";
                        }
                    }
                    int i1 = names.indexOf("，");
                    if(i1!=-1){
                        names=    names.substring(0,names.length()-1);
                    }
                    inOutApply.setUser12Name(names);
                }else {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser12Id()));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        inOutApply.setUser12Name(sysUser.getNickName());
                    }
                }


            }

            if (StringUtils.isNotEmpty(inOutApply.getUser21Id())){
                int i = inOutApply.getUser21Id().indexOf(",");
                if (i!=-1){
                    String[] split = inOutApply.getUser21Id().split(",");
                    String names = "";
                    for (int j = 0; j < split.length; j++) {
                        SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                        if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                            names+=sysUser.getNickName()+"，";
                        }
                    }
                    int i1 = names.indexOf("，");
                    if(i1!=-1){
                        names=    names.substring(0,names.length()-1);
                    }
                    inOutApply.setUser21Name(names);
                }else {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser21Id()));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        inOutApply.setUser21Name(sysUser.getNickName());
                    }
                }


            }
            if (StringUtils.isNotEmpty(inOutApply.getUser22Id())){
                int i = inOutApply.getUser22Id().indexOf(",");
                if (i!=-1){
                    String[] split = inOutApply.getUser22Id().split(",");
                    String names = "";
                    for (int j = 0; j < split.length; j++) {
                        SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                        if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                            names+=sysUser.getNickName()+"，";
                        }
                    }
                    int i1 = names.indexOf("，");
                    if(i1!=-1){
                        names=    names.substring(0,names.length()-1);
                    }
                    inOutApply.setUser22Name(names);
                }else {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser22Id()));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        inOutApply.setUser22Name(sysUser.getNickName());
                    }
                }


            }
            if (StringUtils.isNotEmpty(inOutApply.getUser23Id())){
                int i = inOutApply.getUser23Id().indexOf(",");
                if (i!=-1){
                    String[] split = inOutApply.getUser23Id().split(",");
                    String names = "";
                    for (int j = 0; j < split.length; j++) {
                        SysUser sysUser = userMapper.selectUserById(Long.valueOf(split[j]));
                        if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                            names+=sysUser.getNickName()+"，";
                        }
                    }
                    int i1 = names.indexOf("，");
                    if(i1!=-1){
                        names=    names.substring(0,names.length()-1);
                    }
                    inOutApply.setUser23Name(names);
                }else {
                    SysUser sysUser = userMapper.selectUserById(Long.valueOf(inOutApply.getUser23Id()));
                    if (sysUser!=null&& StringUtils.isNotEmpty(sysUser.getNickName())){
                        inOutApply.setUser23Name(sysUser.getNickName());
                    }
                }


            }
        }
        return  cdlbInOutApplies;
    }

    /**
     * 新增车贷绿本出入库申请
     *
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 结果
     */
    @Override
    public int insertCdlbInOutApply(CdlbInOutApply cdlbInOutApply) {
        cdlbInOutApply.setCreateTime(DateUtils.getNowDate());
        return cdlbInOutApplyMapper.insertCdlbInOutApply(cdlbInOutApply);
    }

    /**
     * 修改车贷绿本出入库申请
     *
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 结果
     */
    @Override
    public int updateCdlbInOutApply(CdlbInOutApply cdlbInOutApply) {
        cdlbInOutApply.setUpdateTime(DateUtils.getNowDate());
        return cdlbInOutApplyMapper.updateCdlbInOutApply(cdlbInOutApply);
    }    /**
     * 修改车贷绿本出入库申请
     *
     * @param cdlbInOutApply 车贷绿本出入库申请
     * @return 结果
     */
    @Override
    public int updateCdlbInOutApplyByProjectId(CdlbInOutApply cdlbInOutApply) {
        cdlbInOutApply.setUpdateTime(DateUtils.getNowDate());
        return cdlbInOutApplyMapper.updateCdlbInOutApplyByProjectId(cdlbInOutApply);
    }

    /**
     * 批量删除车贷绿本出入库申请
     *
     * @param ids 需要删除的车贷绿本出入库申请主键
     * @return 结果
     */
    @Override
    public int deleteCdlbInOutApplyByIds(Long[] ids) {
        return cdlbInOutApplyMapper.deleteCdlbInOutApplyByIds(ids);
    }

    /**
     * 删除车贷绿本出入库申请信息
     *
     * @param id 车贷绿本出入库申请主键
     * @return 结果
     */
    @Override
    public int deleteCdlbInOutApplyById(Long id) {
        return cdlbInOutApplyMapper.deleteCdlbInOutApplyById(id);
    }


    @Override
    public Integer selectCdlbInOutApplyListCounts(CdlbInOutApply cdlbInOutApply) {
        return cdlbInOutApplyMapper.selectCdlbInOutApplyListCounts(cdlbInOutApply);
    }
    @Override
    public Integer selectCdlbInOutApplyListCountschu(CdlbInOutApply cdlbInOutApply) {
        return cdlbInOutApplyMapper.selectCdlbInOutApplyListCountschu(cdlbInOutApply);
    }

    /**
     * 消除本次撤销申请后产生的代办通知
     * @param topNotify
     */
    @Override
    public void updateTopNotifyByApplyId(TopNotify topNotify) {
        cdlbInOutApplyMapper.updateTopNotifyByApplyId(topNotify);
    }
}