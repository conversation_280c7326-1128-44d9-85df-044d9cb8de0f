package org.ruoyi.core.cdlb.mapper;


import org.ruoyi.core.cdlb.domain.CdlbFiles;

import java.util.List;

/**
 * 绿本文件关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
public interface CdlbFilesMapper 
{
    /**
     * 查询绿本文件关联
     * 
     * @param id 绿本文件关联主键
     * @return 绿本文件关联
     */
    public CdlbFiles selectCdlbFilesById(Integer id);

    /**
     * 查询绿本文件关联列表
     * 
     * @param cdlbFiles 绿本文件关联
     * @return 绿本文件关联集合
     */
    public List<CdlbFiles> selectCdlbFilesList(CdlbFiles cdlbFiles);

    /**
     * 新增绿本文件关联
     * 
     * @param cdlbFiles 绿本文件关联
     * @return 结果
     */
    public int insertCdlbFiles(CdlbFiles cdlbFiles);

    /**
     * 修改绿本文件关联
     * 
     * @param cdlbFiles 绿本文件关联
     * @return 结果
     */
    public int updateCdlbFiles(CdlbFiles cdlbFiles);

    /**
     * 删除绿本文件关联
     * 
     * @param id 绿本文件关联主键
     * @return 结果
     */
    public int deleteCdlbFilesById(Integer id);

    /**
     * 批量删除绿本文件关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCdlbFilesByIds(Integer[] ids);
}
