<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phonenumber"  column="phonenumber"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result column="cust_no" property="custNo"/>
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="professionSort" column="profession_sort"/>
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="unitId"       column="unit_id"      />
		<association property="dept"    column="dept_id" javaType="SysDept" resultMap="deptResult" />
		<association property="unit"    column="unit_id" javaType="SysUnit" resultMap="SysUnitResult" />
		<association property="post" column="post_id" javaType="SysPostAO" resultMap="sysPost"/>
		<collection  property="roles"   javaType="java.util.List"        resultMap="RoleResult" />
		<collection  property="userPostList"   javaType="java.util.List"       select="selectUserPostByUserId" column="user_id" />
	</resultMap>

	<resultMap id="deptResult" type="SysDept">
		<id     property="deptId"   column="dept_id"     />
		<result property="parentId" column="parent_id"   />
		<result property="deptName" column="dept_name"   />
		<result property="orderNum" column="order_num"   />
		<result property="leader"   column="leader"      />
		<result property="status"   column="dept_status" />
	</resultMap>


	<resultMap type="SysUnit" id="SysUnitResult">
        <result property="unitId"    column="unit_id"    />
        <result property="unitCode"    column="unit_code"    />
        <result property="unitName"    column="unit_name"    />
        <result property="unitShortName"    column="unit_short_name"    />
        <result property="unitNo"    column="unit_no"    />
    </resultMap>

	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
		<result property="dataScope"     column="data_scope"    />
		<result property="status"       column="role_status"    />
		<result property="delFlag"       column="role_del_flag"    />
		<result property="roleType"           column="role_type"             />
	</resultMap>


	<resultMap id="SysUserPostResult" type="SysUserPost">
		<id     property="userId"       column="user_id"        />
		<result property="postId"     column="post_id"      />
		<result property="homePost"      column="home_post"       />
		<result property="homePostLabel"      column="homePostLabel" />
		<result property="unitId"      column="unit_id"       />
		<result property="unitName"      column="unitName"       />
		<result property="postType"      column="post_type"       />
		<result property="postName"      column="post_name"       />
	</resultMap>
	<resultMap id="sysPost" type="SysPostAO">
		<id     property="postId"       column="post_id"        />
		<result property="postName" column="post_name"/>
	</resultMap>
	<sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password,u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.profession_sort, u.login_date, u.create_by, u.create_time, u.remark,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,r.role_type,r.del_flag as role_del_flag,
        unit.id unit_id,unit.company_code unit_code,unit.company_name unit_name,unit.company_short_name unit_short_name,unit.company_no unit_no,p.post_id,p.post_name
        from sys_user u
		    left join sys_user_post up on u.user_id = up.user_id
		    left join sys_post p on (p.post_id = up.post_id and home_post = '0')
		    left join sys_dept d on p.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
		    left join sys_company unit on unit.id = d.unit_id and unit.is_inside = '1'
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.profession_sort
		from sys_user u
		left join sys_user_post up on u.user_id = up.user_id
		left join sys_post p on up.post_id = p.post_id
		left join sys_dept d on p.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<!-- <if test="custNo != null and custNo != ''">
		  and u.cust_no = #{custNo}
		</if> -->
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="userIds != null and userIds.size() > 0">
			and u.user_id in
			<foreach item="userId" collection="userIds" open="(" close=")" separator=",">
				#{userId}
			</foreach>
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUserListOfDayLog" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.profession_sort
		from sys_user u
		left join sys_user_post up on u.user_id = up.user_id
		left join sys_post p on up.post_id = p.post_id
		left join sys_dept d on p.dept_id = d.dept_id
		where u.del_flag = '0'
		<if test="userId != null and userId != 0">
			AND u.user_id = #{userId}
		</if>
		<if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
		<!-- <if test="custNo != null and custNo != ''">
		  and u.cust_no = #{custNo}
		</if> -->
		<if test="status != null and status != ''">
			AND u.status = #{status}
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
		</if>
		<if test="deptId != null and deptId != 0">
			AND (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))
		</if>
		<if test="userIds != null and userIds.size() > 0">
			and u.user_id in
			<foreach item="userId" collection="userIds" open="(" close=")" separator=",">
				#{userId}
			</foreach>
		</if>
		<trim prefix="and (" suffix=")" prefixOverrides="and|or">
			<if test="unitIds != null and unitIds.size() > 0">
				or d.unit_id in
				<foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
					#{unitId}
				</foreach>
			</if>
			<if test="deptIds != null and deptIds.size() > 0">
				or d.dept_id in
				<foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
					#{deptId}
				</foreach>
			</if>
			<if test="userNames != null and userNames.size() > 0">
				or u.user_name in
				<foreach item="create" collection="userNames" open="(" close=")" separator=",">
					#{create}
				</foreach>
			</if>
		</trim>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber,u.cust_no, u.status, u.create_time, u.profession_sort
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber,u.cust_no, u.status, u.create_time, u.profession_sort
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL)
	    and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
	    <if test="userName != null and userName != ''">
			AND u.user_name like concat('%', #{userName}, '%')
		</if>
		<if test="phonenumber != null and phonenumber != ''">
			AND u.phonenumber like concat('%', #{phonenumber}, '%')
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
	</select>

	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName}
	</select>

	<select id="selectUserByUserNameOfTrue" parameterType="String" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_name = #{userName}  and u.del_flag = '0' and u.status = '0'
	</select>

	<select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.user_id = #{userId}
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultType="int">
		select count(1) from sys_user where user_name = #{userName} limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} limit 1
	</select>

    <select id="selectUserByUserId" resultMap="SysUserResult">
		select su.* from sys_user su where su.user_id IN
		<foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
			#{id}
		</foreach>

	</select>

    <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
 		insert into sys_user(
 			<if test="userId != null and userId != 0">user_id,</if>
 			<if test="deptId != null and deptId != 0">dept_id,</if>
 			<if test="userName != null and userName != ''">user_name,</if>
 			<if test="nickName != null and nickName != ''">nick_name,</if>
 			<if test="email != null and email != ''">email,</if>
 			<if test="avatar != null and avatar != ''">avatar,</if>
 			<if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
 			<if test="sex != null and sex != ''">sex,</if>
 			<if test="password != null and password != ''">password,</if>
			<if test="custNo != null and custNo!=''" >cust_no,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="professionSort != null and professionSort != ''">profession_sort,</if>
 			create_time
 		)values(
 			<if test="userId != null and userId != ''">#{userId},</if>
 			<if test="deptId != null and deptId != ''">#{deptId},</if>
 			<if test="userName != null and userName != ''">#{userName},</if>
 			<if test="nickName != null and nickName != ''">#{nickName},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			<if test="avatar != null and avatar != ''">#{avatar},</if>
 			<if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
 			<if test="sex != null and sex != ''">#{sex},</if>
 			<if test="password != null and password != ''">#{password},</if>
			<if test="custNo != null and custNo!='' ">#{custNo},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="professionSort != null and professionSort != ''">#{professionSort},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateUser" parameterType="SysUser">
 		update sys_user
 		<set>
 			<if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
 			<if test="userName != null and userName != ''">user_name = #{userName},</if>
 			<if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
 			<if test="email != null ">email = #{email},</if>
 			<if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
 			<if test="sex != null and sex != ''">sex = #{sex},</if>
 			<if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
 			<if test="password != null and password != ''">password = #{password},</if>
			<if test="custNo != null and custNo !=''">cust_no = #{custNo},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
 			<if test="loginDate != null">login_date = #{loginDate},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="remark != null">remark = #{remark},</if>
 			<if test="professionSort != null and professionSort != ''" >profession_sort = #{professionSort},</if>
 			update_time = sysdate()
 		</set>
 		where user_id = #{userId}
	</update>

	<update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>

	<update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

	<update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set password = #{password} where user_name = #{userName}
	</update>

	<delete id="deleteUserById" parameterType="Long">
		update sys_user set del_flag = '2' where user_id = #{userId}
 	</delete>

 	<delete id="deleteUserByIds" parameterType="Long">
 		update sys_user set del_flag = '2' where user_id in
 		<foreach collection="array" item="userId" open="(" separator="," close=")">
 			#{userId}
        </foreach>
 	</delete>

<!--	<select id="selectInRoleId" parameterType="Long" resultType="map">-->
<!--		select * from sys_user where  FIND_IN_SET (user_id,(select GROUP_CONCAT(user_id) from sys_user_role where role_id=#{id} GROUP BY role_id))-->
<!--	</select>-->


	<select id="selectInRoleId" parameterType="string" resultType="map">
		SELECT user_id as value,nick_name AS label FROM sys_user WHERE  user_id in( SELECT user_id FROM sys_user_role WHERE role_id = (SELECT role_id FROM sys_role WHERE role_key = #{roleKey}))
	</select>
	<select id="excludeAdmin" resultMap="SysUserResult">
		SELECT  * FROM sys_user WHERE user_name != 'admin' and del_flag = 0 AND `status`  = 0
	</select>




	<select id="selectUserListEnable"  resultMap="SysUserResult">
		select u.user_id, u.nick_name, u.user_name, u.cust_no, u.status, u.del_flag from sys_user u
		where u.del_flag = '0' and u.status = '0'
	</select>
	<select id="selectUserListAll"  resultMap="SysUserResult">
		select u.user_id, u.nick_name, u.user_name, u.cust_no, u.status, u.del_flag from sys_user u
	</select>



	<select id="selectUserPostByUserId" resultMap="SysUserPostResult">
		select sup.user_id, sup.post_id, sup.home_post, sp.dept_id, sd.dept_name, sp.unit_id, sc.company_short_name as 'unitName', sp.post_type, sp.post_name,
		case
		    when sup.home_post = '0' then '主要岗位'
			when sup.home_post = '1' then '兼任岗位'
		else ''
		    end as 'homePostLabel'
		from  sys_user_post sup
				  left join sys_post sp
							on sup.post_id = sp.post_id
				  left join sys_dept sd
							on sp.dept_id = sd.dept_id
				  left join sys_company sc
							on sp.unit_id = sc.id
		where sup.user_id = #{id}
	</select>

	<select id="getUserListByDeptId" parameterType="SysUser" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
			where
            u.del_flag = '0' and u.status = '0'
			<if test="deptId != null">
				and d.dept_id=#{deptId,jdbcType=BIGINT}
			</if>
			<if test="nickName != null and nickName != ''">
				AND u.nick_name like concat('%', #{nickName}, '%')
			</if>

    </select>

	<select id="selectUserListByDeptIds" parameterType="Long" resultMap="SysUserResult">
		select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password,u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark
		from sys_user u
		where u.dept_id in
			<foreach item="deptId" collection="array" open="(" separator="," close=")">
				#{deptId}
			</foreach>
	</select>

    <select id="getUserListByPostId" resultType="com.ruoyi.common.core.domain.entity.SysUser">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password,u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.profession_sort,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status,r.role_type,r.del_flag as role_del_flag,
        unit.id unit_id,unit.company_code unit_code,unit.company_name unit_name,unit.company_short_name unit_short_name,unit.company_no unit_no ,p.post_id,p.post_name
        from sys_user u
		    left join sys_user_post up on u.user_id = up.user_id
		    left join sys_post p on p.post_id = up.post_id
		    left join sys_dept d on p.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
		    left join sys_company unit on unit.id = d.unit_id and unit.is_inside = '1'
        <where>
            <if test="post.postId != null">
                AND p.post_id=#{post.postId,jdbcType=BIGINT}
            </if>
			<if test="post.postCode != null">
				AND p.post_code=#{post.postCode,jdbcType=VARCHAR}
			</if>
            AND u.status='0'
        </where>
		GROUP BY u.user_id
    </select>

	<select id="selectUserListByPostId" parameterType="Long" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password,u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.profession_sort
		from sys_user u
		left join sys_user_post up on u.user_id = up.user_id
		where up.post_id in
		<foreach item="postId" collection="array" open="(" separator="," close=")">
			#{postId}
		</foreach>
		AND u.status='0'

	</select>


	<select id="selectByNickName" parameterType="String" resultMap="SysUserResult">
		select user_id, dept_id, user_name, nick_name from sys_user where nick_name like concat('%', #{nickName}, '%') limit 1
	</select>

	<select id="selectUserByUserPostCode" resultType="java.lang.String">
		SELECT DISTINCT su.user_name FROM sys_user su LEFT JOIN sys_user_post sup ON su.user_id=sup.user_id LEFT JOIN sys_post sp ON sup.post_id=sp.post_id WHERE sp.post_code=#{postCode,jdbcType=VARCHAR}
	</select>

    <select id="getUserAuthorizationList" resultType="com.ruoyi.common.core.domain.entity.SysUserAuth">
		select u.user_id userId,
		u.user_name userName,
		u.nick_name nickName,
		de.dept_id deptId,
		de.dept_name deptName,
		u.email
		from sys_user u
		left join sys_user_post upo on u.user_id = upo.user_id
		left join sys_post po on upo.post_id = po.post_id
		left join sys_dept de on de.dept_id = po.dept_id
		where u.del_flag = '0' and upo.home_post = '0'
		<if test="nickName != null">
			AND u.nick_name like concat('%',#{nickName},'%')
		</if>
	</select>

    <select id="getMainPostId" resultType="string" >
		SELECT sp.dept_id FROM sys_user_post sup LEFT JOIN sys_post sp ON sup.post_id = sp.post_id WHERE sup.user_id = #{userId} and sup.home_post = '0'
	</select>
	<select id="getNameByDept" resultType="string">
		SELECT su.user_name FROM sys_user su LEFT JOIN sys_user_post sup ON su.user_id = sup.user_id
		LEFT JOIN sys_post sp ON sup.post_id = sp.post_id WHERE  su.`status` = '0' and su.del_flag = '0' AND sp.dept_id = #{deptid}

<!--		sup.home_post = '0' AND-->
	</select>


	<select id="listForPersonnel" parameterType="SysUser" resultType="SysUserAuth">
		select distinct u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, d.dept_name
		from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.del_flag = '0'
		  and u.status = '0'
		<if test="nickName != null and nickName != ''">
			AND u.nick_name like concat('%', #{nickName}, '%')
		</if>
	</select>

	<select id="selectUserByRoleKey" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber,u.cust_no, u.status, u.create_time
	    from sys_user u
			 left join sys_dept d on u.dept_id = d.dept_id
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	   WHERE r.role_key=#{roleKey,jdbcType=VARCHAR}
	</select>

	<select id="selectUserByNickName" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.cust_no, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, u.profession_sort
		from sys_user u
		where u.del_flag = '0'
        AND u.nick_name like concat('%', #{nickName,jdbcType=VARCHAR}, '%')
    </select>

	<select id="selectAccreditUserList" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		select * from sys_user_post sup
			left join sys_user su on sup.user_id = su.user_id
		    <where>
				<if test="postId != null">
					and sup.post_id = #{postId}
				</if>
				<if test="nickName != null and nickName != '' " >
					and su.nick_name like concat('%',#{nickName},'%')
				</if>
			</where>
	</select>
    <select id="selectUserPostCompanyInfoByUserId"
            resultType="com.ruoyi.system.domain.SysPost">
		select sp.* from sys_user_post sup
			left join sys_post sp on sup.post_id = sp.post_id
		where sup.user_id = #{userId}
	</select>

	<select id="selectProcessClassificationByCompanysId" resultType="com.ruoyi.system.domain.vo.ProClassificationVo">
		SELECT
		OP.id,
		OP.parent_id AS 'parentId',
		OP.ancestors,
		SC.company_short_name AS 'proCompanyName',
		OP1.NAME AS 'proClassName' ,
		OP.is_company as 'isCompany'
		FROM oa_process_classification OP
		LEFT JOIN sys_company SC ON OP.company_id = SC.id
		LEFT JOIN oa_process_classification OP1 ON OP.id = OP1.id
		WHERE
		OP.PARENT_ID IN (
			SELECT OPP.ID FROM oa_process_classification OPP WHERE OPP.company_id IN
			<foreach collection="collect" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		) OR OP.COMPANY_ID IN
			<foreach collection="collect" item="item" separator="," open="(" close=")">
				#{item}
			</foreach>
	</select>

	<select id="selectClassificationByAncestors" resultType="com.ruoyi.system.domain.vo.OaProcessTemplateVo">
		SELECT id, classification_id 'classificationId', template_name 'templateName' FROM oa_process_template WHERE	classification_id IN (
			SELECT id FROM oa_process_classification WHERE ancestors LIKE concat('%', #{ancestors}, '%') OR id = #{id} )
		  AND is_enable = 'Y'
	</select>

    <select id="selectUserByIds" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		SELECT
			user_id,
			dept_id,
			user_name,
			nick_name
		FROM
			sys_user
		WHERE
			user_id IN
			<foreach item="id" collection="authUserIds" open="(" separator="," close=")">
				#{id}
			</foreach>
		  AND STATUS = '0'
	</select>
    <select id="getUserByPostName" resultType="com.ruoyi.common.core.domain.entity.SysUser">



	</select>

	<select id="queryTabsCompanyByRole" resultType="map">
		SELECT sr.role_name roleName,opc.`name` as title,opc.`name` as value,opc.company_id as id,opc.company_id as name ,opc.id as cid,sr.status,sr.role_type,sro.oa_type,opc.is_company
		FROM sys_role sr LEFT JOIN sys_role_oa sro ON sr.role_id = sro.role_id
		LEFT JOIN oa_process_classification opc ON sro.oa_id = opc.id WHERE sr.`status` = '0' and sr.role_type = '2' AND sro.oa_type = 'opc' AND opc.is_company = '0' and opc.parent_id !='0'
		<if test="roleList != null and roleList.size() != 0">
			AND sr.role_id IN
			<foreach collection="roleList" item="roleid" open="(" close=")" separator=",">
				#{roleid}
			</foreach>
			GROUP BY opc.company_id
		</if>
	</select>

	<select id="queryTabsAllCompany" resultType="map">
		SELECT name as title,name as value,company_id as id,company_id as name FROM oa_process_classification WHERE is_company = '0' AND parent_id != '0' GROUP BY company_id
	</select>

	<select id="queryTabsCompanyByUserID" resultType="java.util.Map">
		SELECT DISTINCT
			opc.`name` AS title,
			opc.`name` AS VALUE,
            opc.company_id AS id,
            opc.company_id AS name,
            opc.id AS cid,
            opc.is_company
		FROM
			auth_main am
			LEFT JOIN auth_detail ad ON am.id = ad.auth_main_id
			LEFT JOIN sys_company sc ON ( sc.id = ad.third_table_id AND ad.third_table_name = 'sys_company' AND sc.is_inside = '1' )
			INNER JOIN oa_process_classification opc ON ad.third_table_id = opc.company_id
		WHERE
			am.STATUS = '0'
		  AND ad.STATUS = '0'
		  AND sc.STATUS = '0'
		  AND am.permission_time > NOW()
		  AND am.third_type = '1'
		  AND am.module_type = 'OALAUNCH'
		  and am.third_id=#{userId}
	</select>

	<select id="queryDataByUserNames" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		select * from   sys_user
		<if test="userNameList != null and userNameList.size() != 0">
			where nick_name in
			<foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
				#{userName}
			</foreach>
		</if>
	</select>


	<select id="selectClassification" resultType="com.ruoyi.system.domain.vo.OaProcessTemplateVo">
		SELECT opt.id, opt.classification_id 'classificationId', opt.template_name 'templateName', opc.ancestors
		FROM oa_process_template opt
				 left join oa_process_classification opc on opt.classification_id = opc.id
		WHERE  opt.is_enable = 'Y'
	</select>

	<select id="queryAuthMainByUserId" resultType="java.lang.Integer">
		SELECT
			count(1)
		FROM
			auth_main am
		WHERE
			am.STATUS = '0'
		  AND am.permission_time > NOW()
		  AND am.third_type = '1'
		  AND am.module_type = 'OALAUNCH'
		  AND am.third_id = #{userId}
		  And am.permission_scope = #{authPermissionCode}
	</select>

	<select id="selectUserIdAndNickNameByUserIdList" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		select user_id as userId, nick_name as nickName from sys_user where status = 0 and user_id in
		<foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
	</select>
	<select id="selectUserNameAndNickNameByUserNameList" resultType="com.ruoyi.common.core.domain.entity.SysUser">
		select user_name as userName, nick_name as nickName from sys_user where status = 0 and user_name in
		<foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
			#{userName}
		</foreach>
	</select>
</mapper>
