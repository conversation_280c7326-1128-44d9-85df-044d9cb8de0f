package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 外部系统平台运营情况数据表(DData)实体类
 *
 * <AUTHOR>
 * @since 2022-04-12 16:42:49
 */
public class DDataCope extends BaseEntity {
    private static final long serialVersionUID = -65086712280196688L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 外部系统平台编码
     */
    @Excel(name = "外部系统平台编码")
    private String platformNo;

    /**
     * 担保公司编码
     */
    @Excel(name = "担保公司编码")
    private String custNo;

    /**
     * 合作方编码
     */
    @Excel(name = "合作方编码")
    private String partnerNo;

    /**
     * 资金方编码
     */
    @Excel(name = "资金方编码")
    private String fundNo;

    /**
     * 产品编码
     */
    @Excel(name = "产品编码")
    private String productNo;

    /**
     * 是否为画像数据（Y是 N否）
     */
    @Excel(name = "是否为画像数据", readConverterExp = "Y=是,N=否")
    private String isPortrayal;

    /**
     * 画像类型
     */
    @Excel(name = "画像类型")
    private String portrayalType;

    /**
     * 画像编码
     */
    @Excel(name = "画像编码")
    private String portrayalNo;

    /**
     * 数据统计时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据统计时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reconDate;

    /**
     * 历史累计-数据起算日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "历史累计-数据起算日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date totalBeginDate;

    /**
     * 历史累计-统计截止日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "历史累计-统计截止日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date totalEndDate;

    /**
     * 历史累计-贷款笔数（笔）
     */
    @Excel(name = "历史累计-贷款笔数")
    private Long totalCount;

    /**
     * 历史累计-贷款人数（人）
     */
    @Excel(name = "历史累计-贷款人数")
    private Long totalHumanCount;

    /**
     * 历史累计-累计贷款本金（元）
     */
    @Excel(name = "历史累计-累计贷款本金")
    private BigDecimal totalAmount;

    /**
     * 累计前期服务费
     */
    @Excel(name = "累计前期服务费")
    private BigDecimal totalActServiceAmount;

    /**
     * 累计前期担保费
     */
    @Excel(name = "累计前期担保费")
    private BigDecimal totalActGuaranteeAmount;

    /**
     * 累计前期保证金
     */
    @Excel(name = "累计前期保证金")
    private BigDecimal totalActMarginAmount;

    /**
     * 累计前期代偿金
     */
    @Excel(name = "累计前期代偿金")
    private BigDecimal totalActCompensateAmount;

    /**
     * 历史累计-贷款平均IRR（%）
     */
    @Excel(name = "历史累计-贷款平均IRR")
    private BigDecimal totalAverageIrr;

    /**
     * 历史累计-平均贷款期限（月）
     */
    @Excel(name = "历史累计-平均贷款期限")
    private BigDecimal totalAverageTerm;

    /**
     * 历史累计-累计贷款本金余额（元）
     */
    @Excel(name = "历史累计-累计贷款本金余额")
    private BigDecimal totalBalanceAmount;

    @Excel(name = "历史累计-还款计划贷款本金余额")
    private BigDecimal totalPlanBalanceAmt;


    @Excel(name = "历史累计-资金贷款余额")
    private BigDecimal totalFundBalanceAmt;


    /**
     * 历史累计-未结清贷款笔数（笔）
     */
    @Excel(name = "历史累计-未结清贷款笔数")
    private Long totalUnclearedCount;

    /**
     * 历史累计-代偿笔数（笔）
     */
    @Excel(name = "历史累计-代偿笔数")
    private Long totalCompensateCount;

    /**
     * 历史累计-累计代偿本金（元）
     */
    @Excel(name = "历史累计-代偿总额")
    private BigDecimal totalCompensateAmount;

    /**
     * 历史累计-代偿总本金（元）
     */
    @Excel(name = "历史累计-代偿总本金")
    private BigDecimal totalCompensatePrintAmount;

    /**
     * 累计还款笔数
     */
    @Excel(name = "累计还款笔数")
    private Long totalRepayCount;

    /**
     * 累计还款本金
     */
    @Excel(name = "累计还款本金")
    private BigDecimal totalRepayPrintAmount;

    /**
     * 累计还款利息
     */
    @Excel(name = "累计还款利息")
    private BigDecimal totalRepayIntAmount;

    /**
     * 累计还款罚息
     */
    @Excel(name = "累计还款罚息")
    private BigDecimal totalRepayOintAmount;

    /**
     * 累计还款服务费
     */
    @Excel(name = "累计还款服务费")
    private BigDecimal totalRepayServiceAmount;

    /**
     * 累计还款担保费
     */
    @Excel(name = "累计还款担保费")
    private BigDecimal totalRepayGuaranteeAmount;

    /**
     * 累计还款保证金
     */
    @Excel(name = "累计还款保证金")
    private BigDecimal totalRepayMarginAmount;

    /**
     * 累计还款代偿金
     */
    @Excel(name = "累计还款代偿金")
    private BigDecimal totalRepayCompensateAmount;

    /**
     * 累计还款逾期违约金
     */
    @Excel(name = "累计还款逾期违约金")
    private BigDecimal totalRepayDefineAmount;

    /**
     * 累计还款提前还款违约金
     */
    @Excel(name = "累计还款提前还款违约金")
    private BigDecimal totalRepayAdvDefineAmount;

    /**
     * 累计减免笔数
     */
    @Excel(name = "累计减免笔数")
    private Long totalReduceCount;

    /**
     * 累计减免本金
     */
    @Excel(name = "累计减免本金")
    private BigDecimal totalReducePrintAmount;

    /**
     * 累计减免利息
     */
    @Excel(name = "累计减免利息")
    private BigDecimal totalReduceIntAmount;

    /**
     * 累计减免罚息
     */
    @Excel(name = "累计减免罚息")
    private BigDecimal totalReduceOintAmount;

    /**
     * 累计减免服务费
     */
    @Excel(name = "累计减免服务费")
    private BigDecimal totalReduceServiceAmount;

    /**
     * 累计减免担保费
     */
    @Excel(name = "累计减免担保费")
    private BigDecimal totalReduceGuaranteeAmount;

    /**
     * 累计减免保证金
     */
    @Excel(name = "累计减免保证金")
    private BigDecimal totalReduceMarginAmount;

    /**
     * 累计减免代偿金
     */
    @Excel(name = "累计减免代偿金")
    private BigDecimal totalReduceCompensateAmount;

    /**
     * 累计减免逾期违约金
     */
    @Excel(name = "累计减免逾期违约金")
    private BigDecimal totalReduceDefineAmount;

    /**
     * 累计减免提前还款违约金
     */
    @Excel(name = "累计减免提前还款违约金")
    private BigDecimal totalReduceAdvDefineAmount;

    /**
     * 当期新增-初始起算日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当期新增-初始起算日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addBeginDate;

    /**
     * 当期新增-统计截止日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "当期新增-统计截止日", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addEndDate;

    /**
     * 当期新增-新增贷款笔数（笔）
     */
    @Excel(name = "当期新增-新增贷款笔数")
    private Long addCount;

    /**
     * 当期新增-新增贷款人数（人）
     */
    @Excel(name = "当期新增-新增贷款人数")
    private Long addHumanCount;

    /**
     * 当期新增-新增贷款本金（元）
     */
    @Excel(name = "当期新增-新增贷款本金")
    private BigDecimal addAmount;

    /**
     * 当期新增前期服务费
     */
    @Excel(name = "当期新增前期服务费")
    private BigDecimal addActServiceAmount;

    /**
     * 当期新增前期担保费
     */
    @Excel(name = "当期新增前期担保费")
    private BigDecimal addActGuaranteeAmount;

    /**
     * 当期新增前期保证金
     */
    @Excel(name = "当期新增前期保证金")
    private BigDecimal addActMarginAmount;

    /**
     * 当期新增前期代偿金
     */
    @Excel(name = "当期新增前期代偿金")
    private BigDecimal addActCompensateAmount;

    /**
     * 当期新增-新增贷款本金余额（元）
     */
    @Excel(name = "当期新增-新增贷款本金余额")
    private BigDecimal addEalanceAmount;

    /**
     * 当期新增-贷款金额中位数（元）
     */
    @Excel(name = "当期新增-贷款金额中位数")
    private BigDecimal addMedianAmount;

    /**
     * 当期新增-贷款平均IRR（%）
     */
    @Excel(name = "当期新增-贷款平均IRR")
    private BigDecimal addAverageIrr;

    /**
     * 当期新增-平均贷款期限（月）
     */
    @Excel(name = "当期新增-平均贷款期限")
    private BigDecimal addAverageTerm;

    /**
     * 当期新增-代偿笔数（笔）
     */
    @Excel(name = "当期新增-代偿笔数")
    private Long addCompensateCount;

    /**
     * 当期新增-累计代偿本金（元）
     */
    @Excel(name = "当期新增-累计代偿本金")
    private BigDecimal addCompensateAmount;

    /**
     * 当期新增还款笔数
     */
    @Excel(name = "当期新增还款笔数")
    private Long addRepayCount;

    /**
     * 当期新增还款本金
     */
    @Excel(name = "当期新增还款本金")
    private BigDecimal addRepayPrintAmount;

    /**
     * 当期新增还款利息
     */
    @Excel(name = "当期新增还款利息")
    private BigDecimal addRepayIntAmount;

    /**
     * 当期新增还款罚息
     */
    @Excel(name = "当期新增还款罚息")
    private BigDecimal addRepayOintAmount;

    /**
     * 当期新增还款服务费
     */
    @Excel(name = "当期新增还款服务费")
    private BigDecimal addRepayServiceAmount;

    /**
     * 当期新增还款担保费
     */
    @Excel(name = "当期新增还款担保费")
    private BigDecimal addRepayGuaranteeAmount;

    /**
     * 当期新增还款保证金
     */
    @Excel(name = "当期新增还款保证金")
    private BigDecimal addRepayMarginAmount;

    /**
     * 当期新增还款代偿金
     */
    @Excel(name = "当期新增还款代偿金")
    private BigDecimal addRepayCompensateAmount;

    /**
     * 当期新增还款逾期违约金
     */
    @Excel(name = "当期新增还款逾期违约金")
    private BigDecimal addRepayDefineAmount;

    /**
     * 当期新增还款提前还款违约金
     */
    @Excel(name = "当期新增还款提前还款违约金")
    private BigDecimal addRepayAdvDefineAmount;

    /**
     * 当期新增减免笔数
     */
    @Excel(name = "当期新增减免笔数")
    private Long addReduceCount;

    /**
     * 当期新增减免本金
     */
    @Excel(name = "当期新增减免本金")
    private BigDecimal addReducePrintAmount;

    /**
     * 当期新增减免利息
     */
    @Excel(name = "当期新增减免利息")
    private BigDecimal addReduceIntAmount;

    /**
     * 当期新增减免罚息
     */
    @Excel(name = "当期新增减免罚息")
    private BigDecimal addReduceOintAmount;

    /**
     * 当期新增减免服务费
     */
    @Excel(name = "当期新增减免服务费")
    private BigDecimal addReduceServiceAmount;

    /**
     * 当期新增减免担保费
     */
    @Excel(name = "当期新增减免担保费")
    private BigDecimal addReduceGuaranteeAmount;

    /**
     * 当期新增减免保证金
     */
    @Excel(name = "当期新增减免保证金")
    private BigDecimal addReduceMarginAmount;

    /**
     * 当期新增减免代偿金
     */
    @Excel(name = "当期新增减免代偿金")
    private BigDecimal addReduceCompensateAmount;

    /**
     * 当期新增减免逾期违约金
     */
    @Excel(name = "当期新增减免逾期违约金")
    private BigDecimal addReduceDefineAmount;

    /**
     * 当期新增减免提前还款违约金
     */
    @Excel(name = "当期新增减免提前还款违约金")
    private BigDecimal addReduceAdvDefineAmount;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 是否映射成功（Y映射成功N映射失败）
     */
    @Excel(name = "是否映射成功", readConverterExp = "Y=映射成功,N映射失败")
    private String isMapping;

    List<String> dateRange;

    public List<String> getDateRange() {
        return dateRange;
    }

    public void setDateRange(List<String> dateRange) {
        this.dateRange = dateRange;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setPlatformNo(String platformNo) {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() {
        return platformNo;
    }

    public void setCustNo(String custNo) {
        this.custNo = custNo;
    }

    public String getCustNo() {
        return custNo;
    }

    public void setPartnerNo(String partnerNo) {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo() {
        return partnerNo;
    }

    public void setFundNo(String fundNo) {
        this.fundNo = fundNo;
    }

    public String getFundNo() {
        return fundNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setIsPortrayal(String isPortrayal) {
        this.isPortrayal = isPortrayal;
    }

    public String getIsPortrayal() {
        return isPortrayal;
    }

    public void setPortrayalType(String portrayalType) {
        this.portrayalType = portrayalType;
    }

    public String getPortrayalType() {
        return portrayalType;
    }

    public void setPortrayalNo(String portrayalNo) {
        this.portrayalNo = portrayalNo;
    }

    public String getPortrayalNo() {
        return portrayalNo;
    }

    public void setReconDate(Date reconDate) {
        this.reconDate = reconDate;
    }

    public Date getReconDate() {
        return reconDate;
    }

    public void setTotalBeginDate(Date totalBeginDate) {
        this.totalBeginDate = totalBeginDate;
    }

    public Date getTotalBeginDate() {
        return totalBeginDate;
    }

    public void setTotalEndDate(Date totalEndDate) {
        this.totalEndDate = totalEndDate;
    }

    public Date getTotalEndDate() {
        return totalEndDate;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalHumanCount(Long totalHumanCount) {
        this.totalHumanCount = totalHumanCount;
    }

    public Long getTotalHumanCount() {
        return totalHumanCount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalActServiceAmount(BigDecimal totalActServiceAmount) {
        this.totalActServiceAmount = totalActServiceAmount;
    }

    public BigDecimal getTotalActServiceAmount() {
        return totalActServiceAmount;
    }

    public void setTotalActGuaranteeAmount(BigDecimal totalActGuaranteeAmount) {
        this.totalActGuaranteeAmount = totalActGuaranteeAmount;
    }

    public BigDecimal getTotalActGuaranteeAmount() {
        return totalActGuaranteeAmount;
    }

    public void setTotalActMarginAmount(BigDecimal totalActMarginAmount) {
        this.totalActMarginAmount = totalActMarginAmount;
    }

    public BigDecimal getTotalActMarginAmount() {
        return totalActMarginAmount;
    }

    public void setTotalActCompensateAmount(BigDecimal totalActCompensateAmount) {
        this.totalActCompensateAmount = totalActCompensateAmount;
    }

    public BigDecimal getTotalActCompensateAmount() {
        return totalActCompensateAmount;
    }

    public void setTotalAverageIrr(BigDecimal totalAverageIrr) {
        this.totalAverageIrr = totalAverageIrr;
    }

    public BigDecimal getTotalAverageIrr() {
        return totalAverageIrr;
    }

    public void setTotalAverageTerm(BigDecimal totalAverageTerm) {
        this.totalAverageTerm = totalAverageTerm;
    }

    public BigDecimal getTotalAverageTerm() {
        return totalAverageTerm;
    }

    public void setTotalBalanceAmount(BigDecimal totalBalanceAmount) {
        this.totalBalanceAmount = totalBalanceAmount;
    }

    public BigDecimal getTotalBalanceAmount() {
        return totalBalanceAmount;
    }

    public void setTotalUnclearedCount(Long totalUnclearedCount) {
        this.totalUnclearedCount = totalUnclearedCount;
    }

    public Long getTotalUnclearedCount() {
        return totalUnclearedCount;
    }

    public void setTotalCompensateCount(Long totalCompensateCount) {
        this.totalCompensateCount = totalCompensateCount;
    }

    public Long getTotalCompensateCount() {
        return totalCompensateCount;
    }

    public void setTotalCompensateAmount(BigDecimal totalCompensateAmount) {
        this.totalCompensateAmount = totalCompensateAmount;
    }

    public BigDecimal getTotalCompensateAmount() {
        return totalCompensateAmount;
    }

    public void setTotalRepayCount(Long totalRepayCount) {
        this.totalRepayCount = totalRepayCount;
    }

    public Long getTotalRepayCount() {
        return totalRepayCount;
    }

    public void setTotalRepayPrintAmount(BigDecimal totalRepayPrintAmount) {
        this.totalRepayPrintAmount = totalRepayPrintAmount;
    }

    public BigDecimal getTotalRepayPrintAmount() {
        return totalRepayPrintAmount;
    }

    public void setTotalRepayIntAmount(BigDecimal totalRepayIntAmount) {
        this.totalRepayIntAmount = totalRepayIntAmount;
    }

    public BigDecimal getTotalRepayIntAmount() {
        return totalRepayIntAmount;
    }

    public void setTotalRepayOintAmount(BigDecimal totalRepayOintAmount) {
        this.totalRepayOintAmount = totalRepayOintAmount;
    }

    public BigDecimal getTotalRepayOintAmount() {
        return totalRepayOintAmount;
    }

    public void setTotalRepayServiceAmount(BigDecimal totalRepayServiceAmount) {
        this.totalRepayServiceAmount = totalRepayServiceAmount;
    }

    public BigDecimal getTotalRepayServiceAmount() {
        return totalRepayServiceAmount;
    }

    public void setTotalRepayGuaranteeAmount(BigDecimal totalRepayGuaranteeAmount) {
        this.totalRepayGuaranteeAmount = totalRepayGuaranteeAmount;
    }

    public BigDecimal getTotalRepayGuaranteeAmount() {
        return totalRepayGuaranteeAmount;
    }

    public void setTotalRepayMarginAmount(BigDecimal totalRepayMarginAmount) {
        this.totalRepayMarginAmount = totalRepayMarginAmount;
    }

    public BigDecimal getTotalRepayMarginAmount() {
        return totalRepayMarginAmount;
    }

    public void setTotalRepayCompensateAmount(BigDecimal totalRepayCompensateAmount) {
        this.totalRepayCompensateAmount = totalRepayCompensateAmount;
    }

    public BigDecimal getTotalRepayCompensateAmount() {
        return totalRepayCompensateAmount;
    }

    public void setTotalRepayDefineAmount(BigDecimal totalRepayDefineAmount) {
        this.totalRepayDefineAmount = totalRepayDefineAmount;
    }

    public BigDecimal getTotalRepayDefineAmount() {
        return totalRepayDefineAmount;
    }

    public void setTotalRepayAdvDefineAmount(BigDecimal totalRepayAdvDefineAmount) {
        this.totalRepayAdvDefineAmount = totalRepayAdvDefineAmount;
    }

    public BigDecimal getTotalRepayAdvDefineAmount() {
        return totalRepayAdvDefineAmount;
    }

    public void setTotalReduceCount(Long totalReduceCount) {
        this.totalReduceCount = totalReduceCount;
    }

    public Long getTotalReduceCount() {
        return totalReduceCount;
    }

    public void setTotalReducePrintAmount(BigDecimal totalReducePrintAmount) {
        this.totalReducePrintAmount = totalReducePrintAmount;
    }

    public BigDecimal getTotalReducePrintAmount() {
        return totalReducePrintAmount;
    }

    public void setTotalReduceIntAmount(BigDecimal totalReduceIntAmount) {
        this.totalReduceIntAmount = totalReduceIntAmount;
    }

    public BigDecimal getTotalReduceIntAmount() {
        return totalReduceIntAmount;
    }

    public void setTotalReduceOintAmount(BigDecimal totalReduceOintAmount) {
        this.totalReduceOintAmount = totalReduceOintAmount;
    }

    public BigDecimal getTotalReduceOintAmount() {
        return totalReduceOintAmount;
    }

    public void setTotalReduceServiceAmount(BigDecimal totalReduceServiceAmount) {
        this.totalReduceServiceAmount = totalReduceServiceAmount;
    }

    public BigDecimal getTotalReduceServiceAmount() {
        return totalReduceServiceAmount;
    }

    public void setTotalReduceGuaranteeAmount(BigDecimal totalReduceGuaranteeAmount) {
        this.totalReduceGuaranteeAmount = totalReduceGuaranteeAmount;
    }

    public BigDecimal getTotalReduceGuaranteeAmount() {
        return totalReduceGuaranteeAmount;
    }

    public void setTotalReduceMarginAmount(BigDecimal totalReduceMarginAmount) {
        this.totalReduceMarginAmount = totalReduceMarginAmount;
    }

    public BigDecimal getTotalReduceMarginAmount() {
        return totalReduceMarginAmount;
    }

    public void setTotalReduceCompensateAmount(BigDecimal totalReduceCompensateAmount) {
        this.totalReduceCompensateAmount = totalReduceCompensateAmount;
    }

    public BigDecimal getTotalReduceCompensateAmount() {
        return totalReduceCompensateAmount;
    }

    public void setTotalReduceDefineAmount(BigDecimal totalReduceDefineAmount) {
        this.totalReduceDefineAmount = totalReduceDefineAmount;
    }

    public BigDecimal getTotalReduceDefineAmount() {
        return totalReduceDefineAmount;
    }

    public void setTotalReduceAdvDefineAmount(BigDecimal totalReduceAdvDefineAmount) {
        this.totalReduceAdvDefineAmount = totalReduceAdvDefineAmount;
    }

    public BigDecimal getTotalReduceAdvDefineAmount() {
        return totalReduceAdvDefineAmount;
    }

    public void setAddBeginDate(Date addBeginDate) {
        this.addBeginDate = addBeginDate;
    }

    public Date getAddBeginDate() {
        return addBeginDate;
    }

    public void setAddEndDate(Date addEndDate) {
        this.addEndDate = addEndDate;
    }

    public Date getAddEndDate() {
        return addEndDate;
    }

    public void setAddCount(Long addCount) {
        this.addCount = addCount;
    }

    public Long getAddCount() {
        return addCount;
    }

    public void setAddHumanCount(Long addHumanCount) {
        this.addHumanCount = addHumanCount;
    }

    public Long getAddHumanCount() {
        return addHumanCount;
    }

    public void setAddAmount(BigDecimal addAmount) {
        this.addAmount = addAmount;
    }

    public BigDecimal getAddAmount() {
        return addAmount;
    }

    public void setAddActServiceAmount(BigDecimal addActServiceAmount) {
        this.addActServiceAmount = addActServiceAmount;
    }

    public BigDecimal getAddActServiceAmount() {
        return addActServiceAmount;
    }

    public void setAddActGuaranteeAmount(BigDecimal addActGuaranteeAmount) {
        this.addActGuaranteeAmount = addActGuaranteeAmount;
    }

    public BigDecimal getAddActGuaranteeAmount() {
        return addActGuaranteeAmount;
    }

    public void setAddActMarginAmount(BigDecimal addActMarginAmount) {
        this.addActMarginAmount = addActMarginAmount;
    }

    public BigDecimal getAddActMarginAmount() {
        return addActMarginAmount;
    }

    public void setAddActCompensateAmount(BigDecimal addActCompensateAmount) {
        this.addActCompensateAmount = addActCompensateAmount;
    }

    public BigDecimal getAddActCompensateAmount() {
        return addActCompensateAmount;
    }

    public void setAddEalanceAmount(BigDecimal addEalanceAmount) {
        this.addEalanceAmount = addEalanceAmount;
    }

    public BigDecimal getAddEalanceAmount() {
        return addEalanceAmount;
    }

    public void setAddMedianAmount(BigDecimal addMedianAmount) {
        this.addMedianAmount = addMedianAmount;
    }

    public BigDecimal getAddMedianAmount() {
        return addMedianAmount;
    }

    public void setAddAverageIrr(BigDecimal addAverageIrr) {
        this.addAverageIrr = addAverageIrr;
    }

    public BigDecimal getAddAverageIrr() {
        return addAverageIrr;
    }

    public void setAddAverageTerm(BigDecimal addAverageTerm) {
        this.addAverageTerm = addAverageTerm;
    }

    public BigDecimal getAddAverageTerm() {
        return addAverageTerm;
    }

    public void setAddCompensateCount(Long addCompensateCount) {
        this.addCompensateCount = addCompensateCount;
    }

    public Long getAddCompensateCount() {
        return addCompensateCount;
    }

    public void setAddCompensateAmount(BigDecimal addCompensateAmount) {
        this.addCompensateAmount = addCompensateAmount;
    }

    public BigDecimal getAddCompensateAmount() {
        return addCompensateAmount;
    }

    public void setAddRepayCount(Long addRepayCount) {
        this.addRepayCount = addRepayCount;
    }

    public Long getAddRepayCount() {
        return addRepayCount;
    }

    public void setAddRepayPrintAmount(BigDecimal addRepayPrintAmount) {
        this.addRepayPrintAmount = addRepayPrintAmount;
    }

    public BigDecimal getAddRepayPrintAmount() {
        return addRepayPrintAmount;
    }

    public void setAddRepayIntAmount(BigDecimal addRepayIntAmount) {
        this.addRepayIntAmount = addRepayIntAmount;
    }

    public BigDecimal getAddRepayIntAmount() {
        return addRepayIntAmount;
    }

    public void setAddRepayOintAmount(BigDecimal addRepayOintAmount) {
        this.addRepayOintAmount = addRepayOintAmount;
    }

    public BigDecimal getAddRepayOintAmount() {
        return addRepayOintAmount;
    }

    public void setAddRepayServiceAmount(BigDecimal addRepayServiceAmount) {
        this.addRepayServiceAmount = addRepayServiceAmount;
    }

    public BigDecimal getAddRepayServiceAmount() {
        return addRepayServiceAmount;
    }

    public void setAddRepayGuaranteeAmount(BigDecimal addRepayGuaranteeAmount) {
        this.addRepayGuaranteeAmount = addRepayGuaranteeAmount;
    }

    public BigDecimal getAddRepayGuaranteeAmount() {
        return addRepayGuaranteeAmount;
    }

    public void setAddRepayMarginAmount(BigDecimal addRepayMarginAmount) {
        this.addRepayMarginAmount = addRepayMarginAmount;
    }

    public BigDecimal getAddRepayMarginAmount() {
        return addRepayMarginAmount;
    }

    public void setAddRepayCompensateAmount(BigDecimal addRepayCompensateAmount) {
        this.addRepayCompensateAmount = addRepayCompensateAmount;
    }

    public BigDecimal getAddRepayCompensateAmount() {
        return addRepayCompensateAmount;
    }

    public void setAddRepayDefineAmount(BigDecimal addRepayDefineAmount) {
        this.addRepayDefineAmount = addRepayDefineAmount;
    }

    public BigDecimal getAddRepayDefineAmount() {
        return addRepayDefineAmount;
    }

    public void setAddRepayAdvDefineAmount(BigDecimal addRepayAdvDefineAmount) {
        this.addRepayAdvDefineAmount = addRepayAdvDefineAmount;
    }

    public BigDecimal getAddRepayAdvDefineAmount() {
        return addRepayAdvDefineAmount;
    }

    public void setAddReduceCount(Long addReduceCount) {
        this.addReduceCount = addReduceCount;
    }

    public Long getAddReduceCount() {
        return addReduceCount;
    }

    public void setAddReducePrintAmount(BigDecimal addReducePrintAmount) {
        this.addReducePrintAmount = addReducePrintAmount;
    }

    public BigDecimal getAddReducePrintAmount() {
        return addReducePrintAmount;
    }

    public void setAddReduceIntAmount(BigDecimal addReduceIntAmount) {
        this.addReduceIntAmount = addReduceIntAmount;
    }

    public BigDecimal getAddReduceIntAmount() {
        return addReduceIntAmount;
    }

    public void setAddReduceOintAmount(BigDecimal addReduceOintAmount) {
        this.addReduceOintAmount = addReduceOintAmount;
    }

    public BigDecimal getAddReduceOintAmount() {
        return addReduceOintAmount;
    }

    public void setAddReduceServiceAmount(BigDecimal addReduceServiceAmount) {
        this.addReduceServiceAmount = addReduceServiceAmount;
    }

    public BigDecimal getAddReduceServiceAmount() {
        return addReduceServiceAmount;
    }

    public void setAddReduceGuaranteeAmount(BigDecimal addReduceGuaranteeAmount) {
        this.addReduceGuaranteeAmount = addReduceGuaranteeAmount;
    }

    public BigDecimal getAddReduceGuaranteeAmount() {
        return addReduceGuaranteeAmount;
    }

    public void setAddReduceMarginAmount(BigDecimal addReduceMarginAmount) {
        this.addReduceMarginAmount = addReduceMarginAmount;
    }

    public BigDecimal getAddReduceMarginAmount() {
        return addReduceMarginAmount;
    }

    public void setAddReduceCompensateAmount(BigDecimal addReduceCompensateAmount) {
        this.addReduceCompensateAmount = addReduceCompensateAmount;
    }

    public BigDecimal getAddReduceCompensateAmount() {
        return addReduceCompensateAmount;
    }

    public void setAddReduceDefineAmount(BigDecimal addReduceDefineAmount) {
        this.addReduceDefineAmount = addReduceDefineAmount;
    }

    public BigDecimal getAddReduceDefineAmount() {
        return addReduceDefineAmount;
    }

    public void setAddReduceAdvDefineAmount(BigDecimal addReduceAdvDefineAmount) {
        this.addReduceAdvDefineAmount = addReduceAdvDefineAmount;
    }

    public BigDecimal getAddReduceAdvDefineAmount() {
        return addReduceAdvDefineAmount;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setIsMapping(String isMapping) {
        this.isMapping = isMapping;
    }

    public String getIsMapping() {
        return isMapping;
    }

    public BigDecimal getTotalPlanBalanceAmt() {
        return totalPlanBalanceAmt;
    }

    public void setTotalPlanBalanceAmt(BigDecimal totalPlanBalanceAmt) {
        this.totalPlanBalanceAmt = totalPlanBalanceAmt;
    }

    public BigDecimal getTotalFundBalanceAmt() {
        return totalFundBalanceAmt;
    }

    public void setTotalFundBalanceAmt(BigDecimal totalFundBalanceAmt) {
        this.totalFundBalanceAmt = totalFundBalanceAmt;
    }

    public BigDecimal getTotalCompensatePrintAmount() {
        return totalCompensatePrintAmount;
    }

    public void setTotalCompensatePrintAmount(BigDecimal totalCompensatePrintAmount) {
        this.totalCompensatePrintAmount = totalCompensatePrintAmount;
    }

}

