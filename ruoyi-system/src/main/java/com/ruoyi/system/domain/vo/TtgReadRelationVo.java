package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.List;

@Data
public class TtgReadRelationVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 公告id */
    @Excel(name = "公告id")
    private Long noticeId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 状态(0未读 1已读) */
    @Excel(name = "状态(0未读 1已读)")
    private String readType;

    /** 状态字典值 */
    private String readTypeLabel;

    /** 公司id集合 */
    private List<Long> companyIdList;

    /** 公告类型 */
    private String noticeType;

    /** 公告名称 */
    private String noticeName;

    /** 创建人姓名 */
    private String createNickName;

    /** 分页参数 */
    private Integer pageSize;

    /** 分页参数 */
    private Integer pageNum;

    /** 是否置顶(0否 1是) */
    private String isHeader;

    /** 是否重点(0否 1是) */
    private String isEmphasis;

    /** 版本号 */
    private Integer version;
}
