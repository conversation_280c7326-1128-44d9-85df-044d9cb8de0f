package com.ruoyi.system.service.impl;

import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.service.IAuthModuleService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 获取模块公共类service实现类
 *
 * 用于获取项目、公司、等不同维度的模块分页
 * 目前分页：项目、公司、所有
 *
 * <AUTHOR>
 * @date 2024-04-15
 */
@Service
public class AuthModuleServiceImpl implements IAuthModuleService {

    /**
     * 通过模板类型获取模板
     * @param type  模板类型 proj 项目  unit 公司  all 所有
     * @return 模板集合
     */
    @Override
    public List<HashMap<String, Object>> getModule(String type) {
        if(type == null){
            throw new ServiceException("请传递权限类型");
        }
        //去除空格和转小写
        type= type.trim().toLowerCase();
        List<HashMap<String, Object>> list = new ArrayList<>();
        //项目模板
        if("proj".equals(type)) {
            for (AuthModuleEnum authModuleEnum : EnumSet.allOf(AuthModuleEnum.class)) {
                String code = authModuleEnum.getCode();
                if("FINANCEPROJ".equals(code) || "CARBOOLMANGER".equals(code) || "PROJSETUP".equals(code) || "DATAREPORT".equals(code) ||
                        "ECHARTS".equals(code) || "DATATOP".equals(code) || "PROJNAME".equals(code)
                        || "JGINFORMATION".equals(code) || "BUSINESSCONTROL".equals(code)
                ){
                    setData(list, authModuleEnum);
                }
            }
            return list;
        }
        //公司模板
        else if("unit".equals(type)) {
            for (AuthModuleEnum authModuleEnum : EnumSet.allOf(AuthModuleEnum.class)) {
                String code = authModuleEnum.getCode();
                if("OALAUNCH".equals(code) || "FINANCESYS".equals(code) || "FINANCEPROJ".equals(code) || "CARBOOLMANGER".equals(code) ||
                        "PROJSETUP".equals(code) || "ARCHIVES".equals(code) ||
                        "JGINFORMATION".equals(code) || "SXINFORMATION".equals(code) ||
                        "LICENSE".equals(code) || "PAYROLL".equals(code) ||
                        "PERSONNEL".equals(code) || "ATTENDANCE".equals(code) || "DATAREPORT".equals(code) || "ECHARTS".equals(code) || "DATATOP".equals(code) ||
                        "PROJNAME".equals(code) || "PERFORMANCE".equals(code) || "NOTICE".equals(code) || "BADSYSTEM".equals(code)||"OFFSUPPLY".equals(code) ||
                        "DEBTCONVERSION".equals(code)||"BUSINESSCONTROL".equals(code) || "BUSINESSCONTROLPROJ".equals(code)){
                    setData(list, authModuleEnum);
                }
            }
            return list;
        //公司类型
        }else if("select".equals(type)){
            for (AuthModuleEnum authModuleEnum : EnumSet.allOf(AuthModuleEnum.class)) {
                String code = authModuleEnum.getCode();
                if("FINANCEPROJ".equals(code) || "PROJSETUP".equals(code) || "DATAREPORT".equals(code) || "ECHARTS".equals(code) || "DATATOP".equals(code) ||
                        "PROJNAME".equals(code)|| "BUSINESSDATACONFIG".equals(code)||"BUSINESSANALYSIS".equals(code) ||"BADSYSTEM".equals(code)
                        || "INTERNALCOMPANY".equals(code)||"EXTERNALCOMPANY".equals(code)||"PROJECTNAMERULE".equals(code)
                        ||"DEBTCONVERSION".equals(code)
                        || "JGINFORMATION".equals(code)
                ){
                    setData(list, authModuleEnum);
                }
            }
            return list;
        }
        //所有模板
        else if("all".equals(type)) {
            for (AuthModuleEnum authModuleEnum : EnumSet.allOf(AuthModuleEnum.class)) {
                String code = authModuleEnum.getCode();
                if("OALAUNCH".equals(code) || "OAVIEW".equals(code) || "FINANCESYS".equals(code) || "FINANCEPROJ".equals(code) || "CARBOOLMANGER".equals(code) ||
                        "PROJSETUP".equals(code) || "ARCHIVES".equals(code) ||
                        "JGINFORMATION".equals(code) || "SXINFORMATION".equals(code) ||
                        "LICENSE".equals(code) || "PAYROLL".equals(code) ||
                        "PERSONNEL".equals(code) || "ATTENDANCE".equals(code) || "DATAREPORT".equals(code) || "ECHARTS".equals(code) || "DATATOP".equals(code) ||
                        "PROJNAME".equals(code) || "PERFORMANCE".equals(code) || "BUSINESSCONTROL".equals(code) || "BUSINESSCONTROLPROJ".equals(code) || "NOTICE".equals(code)|| "OFFSUPPLY".equals(code)){
                    setData(list, authModuleEnum);
                }
            }
            return list;
        }
        else{
            throw new ServiceException("未找到对应的模板类型");
        }
    }

    /**
     * 拼接模板类型数组
     * @param list  返回的数组
     * @param authModuleEnum    获取枚举类里的值
     */
    private void setData(List<HashMap<String, Object>> list, AuthModuleEnum authModuleEnum) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("code", authModuleEnum.getCode());
        map.put("permissionType", authModuleEnum.getPermissionType());
        map.put("info", authModuleEnum.getInfo());
        map.put("order", authModuleEnum.getOrder());
        map.put("status", authModuleEnum.getStatus());
        //前端要求加一个首字母大写的值
        map.put("UPCode",authModuleEnum.getCode() != null ? authModuleEnum.getCode().substring(0,1).toUpperCase() + authModuleEnum.getCode().substring(1).toLowerCase():null);
        list.add(map);
    }

}
