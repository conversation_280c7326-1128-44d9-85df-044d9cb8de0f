package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ICompanyBusinessTypeMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CompanyBusinessTypeMappingMapper;
import com.ruoyi.system.domain.CompanyBusinessTypeMapping;

/**
 * 公司支持业务类型映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@Service
public class CompanyBusinessTypeMappingServiceImpl implements ICompanyBusinessTypeMappingService
{
    @Autowired
    private CompanyBusinessTypeMappingMapper companyBusinessTypeMappingMapper;

    /**
     * 查询公司支持业务类型映射
     *
     * @param id 公司支持业务类型映射主键
     * @return 公司支持业务类型映射
     */
    @Override
    public CompanyBusinessTypeMapping selectCompanyBusinessTypeMappingById(Long id)
    {
        return companyBusinessTypeMappingMapper.selectCompanyBusinessTypeMappingById(id);
    }

    /**
     * 查询公司支持业务类型映射列表
     *
     * @param companyBusinessTypeMapping 公司支持业务类型映射
     * @return 公司支持业务类型映射
     */
    @Override
    public List<CompanyBusinessTypeMapping> selectCompanyBusinessTypeMappingList(CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        return companyBusinessTypeMappingMapper.selectCompanyBusinessTypeMappingList(companyBusinessTypeMapping);
    }

    /**
     * 新增公司支持业务类型映射
     *
     * @param companyBusinessTypeMapping 公司支持业务类型映射
     * @return 结果
     */
    @Override
    public int insertCompanyBusinessTypeMapping(CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        companyBusinessTypeMapping.setCreateTime(DateUtils.getNowDate());
        return companyBusinessTypeMappingMapper.insertCompanyBusinessTypeMapping(companyBusinessTypeMapping);
    }

    /**
     * 修改公司支持业务类型映射
     *
     * @param companyBusinessTypeMapping 公司支持业务类型映射
     * @return 结果
     */
    @Override
    public int updateCompanyBusinessTypeMapping(CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        companyBusinessTypeMapping.setUpdateTime(DateUtils.getNowDate());
        return companyBusinessTypeMappingMapper.updateCompanyBusinessTypeMapping(companyBusinessTypeMapping);
    }

    /**
     * 批量删除公司支持业务类型映射
     *
     * @param ids 需要删除的公司支持业务类型映射主键
     * @return 结果
     */
    @Override
    public int deleteCompanyBusinessTypeMappingByIds(Long[] ids)
    {
        return companyBusinessTypeMappingMapper.deleteCompanyBusinessTypeMappingByIds(ids);
    }

    /**
     * 删除公司支持业务类型映射信息
     *
     * @param id 公司支持业务类型映射主键
     * @return 结果
     */
    @Override
    public int deleteCompanyBusinessTypeMappingById(Long id)
    {
        return companyBusinessTypeMappingMapper.deleteCompanyBusinessTypeMappingById(id);
    }

    /**
     * 批量插入公司类支持业务型映射数据
     *
     * @param companyBusinessTypeMappingList
     */
    @Override
    public int batchInsertCompanyBusinessTypeMapping(List<CompanyBusinessTypeMapping> companyBusinessTypeMappingList) {
        return companyBusinessTypeMappingMapper.batchInsertCompanyBusinessTypeMapping(companyBusinessTypeMappingList);
    }


    /**
     * 删除公司支持业务类型映射信息
     *
     * @param companyId 公司支持业务类型映射主键
     * @return 结果
     */
    @Override
    public int deleteCompanyBusinessTypeMappingByCompanyId(Long companyId) {
        return companyBusinessTypeMappingMapper.deleteCompanyBusinessTypeMappingByCompanyId(companyId);
    }
}
