package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysOperLog;

import java.util.Date;
import java.util.List;

/**
 * 操作日志 服务层
 *
 * <AUTHOR>
 */
public interface ISysOperLogService
{
    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    public void insertOperlog(SysOperLog operLog);

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> selectOperLogList(SysOperLog operLog);

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();

    /**
     * 新增操作日志
     * @param title 所属模块 (必填，参考AuthModuleEnum枚举类)
     * @param functionNode 所属功能节点 (必填，参考FunctionNodeEnum枚举类)
     * @param businessType 操作类型 (必填，详情见‘操作类型’数据字典，如新增操作传1,修改操作传2...)
     * @param operMsg 操作内容 (必填)
     * @param errorMsg 异常信息 (非必填)
     * @param relationId 关联id (非必填，未用到可以为空)
     */
    public void insertOperLogMessage(String title, String functionNode, String operMsg, Integer businessType, String errorMsg, String relationId);

    public Long insertOperLogMessageOfRecord(String title, String functionNode, String operMsg, Integer businessType, String errorMsg, String relationId);
}
