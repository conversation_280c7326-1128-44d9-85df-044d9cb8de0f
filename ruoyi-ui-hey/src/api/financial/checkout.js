/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : </li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月25日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
import Ajax from '@/assets/financial/js/common/ajax';
import request from "@/utils/request";

export default {
	list(params) {
    return request({
      url: '/checkout',
      method: 'get',
      params: params
    })
	},
	initialCheck(params) {
    return request({
      url: '/checkout/initialCheck',
      method: 'get',
      params: params
    })
	},
	finalCheck(params) {
    return request({
      url: '/checkout/finalCheck',
      method: 'get',
      params: params
    })
	},
	reportCheck(params) {
    return request({
      url: '/checkout/reportCheck',
      method: 'get',
      params: params
    })
	},
	brokenCheck(params) {
    return request({
      url: '/checkout/brokenCheck',
      method: 'get',
      params: params
    })
	},
	invoicing(params) {
    return request({
      url: '/checkout/invoicing',
      method: 'get',
      params: params
    })
	},
	unCheck(params) {
    return request({
      url: '/checkout/unCheck',
      method: 'get',
      params: params
    })
	}
}
