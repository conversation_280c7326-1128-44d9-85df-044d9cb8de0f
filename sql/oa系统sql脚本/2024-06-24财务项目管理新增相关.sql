ALTER TABLE oa_pay_rebate_record ADD COLUMN `project_type_id` int(60) DEFAULT NULL COMMENT '项目类型id' AFTER `project_id`;


ALTER TABLE oa_project_flow_main ADD COLUMN `project_type_field` VARCHAR(255) DEFAULT NULL COMMENT '项目类型字段' AFTER `accounting_field_name`;

ALTER TABLE oa_project_flow_main ADD COLUMN `project_type_field_name` VARCHAR(255) DEFAULT NULL COMMENT '项目类型字段' AFTER `project_type_field`;



ALTER TABLE oa_project_flow_main ADD COLUMN `fee_company_field` VARCHAR(255) DEFAULT NULL COMMENT '返费字段' AFTER `project_type_field_name`;

ALTER TABLE oa_project_flow_main ADD COLUMN `fee_company_field_name` VARCHAR(255) DEFAULT NULL COMMENT '返费字段名称' AFTER `fee_company_field`;



ALTER TABLE oa_pay_rebate_record ADD COLUMN `income_id` int(60) DEFAULT NULL COMMENT '期次id' AFTER `company_no`;