package com.ruoyi.financial.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.financial.controller.JsonResult;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.domain.FinancialSubject;
import com.ruoyi.financial.excel.SubjectBalanceExcel;
import com.ruoyi.financial.po.ProjectInfoPo;
import com.ruoyi.financial.vo.BalanceVo;
import com.ruoyi.financial.vo.BindProjectVo;
import com.ruoyi.financial.vo.SubjectVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface IFinancialSubjectService extends IService<FinancialSubject> {

    int batchInsert(List<FinancialSubject> list);

    List<SubjectVo> selectData(Wrapper<FinancialSubject> queryWrapper, boolean showAll);

    /**
     * 凭证明细的科目菜单
     *
     * @param startTime
     * @param endTime
     * @param accountSetsId
     * @return
     */
    List<FinancialSubject> accountBookList(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice);

    /**
     * 科目余额
     *
     * @param accountSetsId
     * @param startTime
     * @return
     */
    List<BalanceVo> subjectBalance(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice);

    /**
     * 科目汇总
     *
     * @param accountSetsId
     * @param startTime
     * @return
     */
    List subjectSummary(Integer accountSetsId, Date startTime, Date endTime, boolean showNumPrice);

    /**
     * vo
     *
     * @param queryWrapper
     * @return
     */
    List<SubjectVo> listVo(Wrapper<FinancialSubject> queryWrapper);

    /**
     * 检查科目是否已经被使用
     *
     * @param id
     * @return
     */
    Boolean checkUse(Integer id);

    /**
     * 科目余额
     *
     * @param accountSetsId
     * @param subjectId
     * @param categoryId
     * @param categoryDetailsId
     * @return
     */
    Double balance(Integer accountSetsId, Integer subjectId, Integer categoryId, Integer categoryDetailsId);

    /**
     * 过滤出所有没有子节点的科目
     *
     * @param accountSetsId
     * @return
     */
    List<Integer> leafList(Integer accountSetsId);

    List<FinancialSubject> balanceSubjectList(Integer accountSetsId, Date startTime, Date endTime, boolean b);

    void importVoucher(List<SubjectVo> voucherList, FinancialAccountSets accountSets);

    List<SubjectBalanceExcel> exportSubjectBalance(Integer accountSetsId, Date startTime, Date endTime, Boolean showNumPrice);

    List<Map<String,Object>> getSubjectSelectData(FinancialSubject financialSubject);

    List<Map<String,Object>> getSubjectTypeData(Long accountSetsId);

    /**
     * <AUTHOR>
     * @Description 复制科目
     * @Date 2023/9/28 11:25
     * @Param [sourceAccountSetsId, targetAccountSetsId]
     * @return void
     **/
    void copySubject(Integer sourceAccountSetsId, Integer targetAccountSetsId);

    List<Map<String, Object>> queryAllFirst();

    /**
     * <AUTHOR>
     * @Description 科目编码排序
     * @Date 2024/7/12 14:48
     * @Param [accountSetsId, id]
     * @return void
     **/
    void subjectCodeSort(Integer accountSetsId, Integer subjectId, Integer level);

    /**
     * <AUTHOR>
     * @Description 获取项目列表
     * @Date 2024/11/13 14:40
     * @Param [accountSetsId]
     * @return java.util.List<com.ruoyi.financial.po.ProjectInfoPo>
     **/
    List<ProjectInfoPo> getProjectList(Integer accountSetsId);

    /**
     * <AUTHOR>
     * @Description 绑定项目
     * @Date 2024/11/12 10:41
     * @Param [bindProjectVo]
     * @return void
     **/
    JsonResult bindProject(BindProjectVo bindProjectVo);
}





