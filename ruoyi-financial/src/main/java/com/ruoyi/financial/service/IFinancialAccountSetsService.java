package com.ruoyi.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.domain.FinancialUserAccountSets;

import java.util.List;
import java.util.Map;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : ${PACKAGE_NAME}</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface IFinancialAccountSetsService extends IService<FinancialAccountSets>{


    int batchInsert(List<FinancialAccountSets> list);

    List<FinancialAccountSets> myAccountSets(Long uid);

    void removeUser(Integer accountSetsId, Integer uid);

    void updateUserRole(Integer accountSetsId, Integer uid, String role);

    /**
     * 账套移交
     *
     * @param accountSetsId
     * @param currentUserId
     * @param mobile
     */
    void handOver(Integer accountSetsId, Long currentUserId, Long userId);

    void updateEncode(Integer accountSetsId, String encoding, String newEncoding);

    List<Map<String, Object>> queryAccountListByComId(Long companyId);

    List<Map<String, Object>> getAllList();

    /**
     * 通过账套所在公司查询该公司智慧财务系统赋权人员
     * @param companyId
     * @return
     */
    List<Map<String, Object>> queryRoleTypeUser(List<Long> companyId);
}




