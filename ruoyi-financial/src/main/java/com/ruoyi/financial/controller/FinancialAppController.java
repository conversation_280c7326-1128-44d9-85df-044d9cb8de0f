package com.ruoyi.financial.controller;

import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.financial.controller.base.BaseCrudController;
import com.ruoyi.financial.service.IFinancialAppService;
import com.ruoyi.financial.vo.FinancialUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.SessionAttribute;


/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class FinancialAppController extends BaseCrudController {

    @Autowired
    private IFinancialAppService financialAppService;

    /**
     * <AUTHOR>
     * @Description 返回用户信息
     * @Date 2023/5/29 17:31
     * @Param []
     * @return com.ruoyi.financial.controller.JsonResult
     **/
    @GetMapping("/init")
    public JsonResult init(Integer accountSetsId) {
        return JsonResult.successful(getUserVo(accountSetsId));
    }

    /**
     * <AUTHOR>
     * @Description 查询会计与会计主管
     * @Date 2023/6/19 10:51
     * @Param []
     * @return com.ruoyi.financial.controller.JsonResult
     **/
    @GetMapping("financial/getConfig")
    public JsonResult getConfig() {
        return JsonResult.successful(financialAppService.getConfig());
    }

    /**
     * <AUTHOR>
     * @Description 获取登录用户信息
     * @Date 2023/6/27 16:36
     * @Param []
     * @return com.ruoyi.financial.controller.JsonResult
     **/
    @GetMapping("financial/getUser")
    public JsonResult getUser(){
        return JsonResult.successful(getLoginUser().getUser());
    }

    /**
     * <AUTHOR>
     * @Description 获取菜单数据
     * @Date 2023/6/27 16:36
     * @Param []
     * @return com.ruoyi.financial.controller.JsonResult
     **/
    @GetMapping("financial/getMenu")
    public JSONArray getMenu(String roleKey){
        return financialAppService.getMenu(roleKey);
    }
}
