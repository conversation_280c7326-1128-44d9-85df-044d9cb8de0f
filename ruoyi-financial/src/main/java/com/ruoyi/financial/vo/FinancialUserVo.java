package com.ruoyi.financial.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.domain.FinancialCheckout;
import com.ruoyi.financial.domain.FinancialUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.model.vo</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年08月27日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FinancialUserVo extends FinancialUser {

    /**用户信息**/
    private String email;
    private String mobile;
    private String nickname;
    private String realName;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createDate;
    /**用户信息**/

    private FinancialAccountSets accountSets;

    private String role;

    private List<FinancialAccountSets> accountSetsList;

    private List<FinancialCheckout> checkoutList;
}
