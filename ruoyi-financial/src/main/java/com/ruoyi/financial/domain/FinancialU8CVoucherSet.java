package com.ruoyi.financial.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 凭证推送至用友U8C对象 financial_u8c_voucher_set
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
public class FinancialU8CVoucherSet extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 智慧财务系统凭证ID */
    @Excel(name = "智慧财务系统凭证ID")
    private Integer voucherId;

    /** u8c凭证ID */
    @Excel(name = "u8c凭证ID")
    private String pkVoucher;

    /** 推送规则ID，没有则直接按照原科目推送 */
    @Excel(name = "推送规则ID，没有则直接按照原科目推送")
    private Long ruleId;

    /** u8c凭证类型 */
    @Excel(name = "u8c凭证类型")
    private String u8cVouchertypeName;

    /** u8c凭证号 */
    @Excel(name = "u8c凭证号")
    private String u8cNo;

    /** 推送状态( 0 成功  1 失败) */
    @Excel(name = "推送状态( 0 成功  1 失败)")
    private String pushStatus;

    /** u8c返回状态 */
    @Excel(name = "u8c返回状态")
    private String u8cReturnMessage;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pushTime;

    /** 推送人 */
    @Excel(name = "推送人")
    private String pushBy;

    /**
     * 推送失败原因
     */
    private String failureReason;

    /**
     * 自动匹配规则标志 1：自动匹配
     */
    private String aotoFlag;

    //科目名称
    private String[] subjectName;

    //账簿主键
    private Long accountId;

    private Integer[] voucherIds;

    /**
     * 单层科目名称
     */
    private String subject;

    /**
     * 合并推送次数
     */
    private Integer mergeCount;

    /**
     * 单独推送次数
     */
    private Integer aloneCount;

    /**
     * 失败推送次数
     */
    private Integer failureCount;

    /**
     * 科目ID
     */
    private Integer subjectId;

    private Integer[] subjectIds;

    /**
     * 科目级次
     */
    private int level;

    /**
     * 科目类型
     */
    private String type;

    private String pushType;

    /**
     * U8C科目编码
     */
    private String U8CSubjectCode;

    /**
     * 是否已有对应科目 1：有
     */
    private String isFlag;

    private List<FinancialVoucher> voucherList;

    /**
     * 规则
     */
    private FinancialU8CRule rule;

    private Integer pageNum;

    private Integer pageSize;
}

