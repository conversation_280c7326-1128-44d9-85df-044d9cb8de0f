package com.ruoyi.financial.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 凭证推送规则明细表对象 financial_u8c_rule_details
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
public class FinancialU8CRuleDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则主键 */
    @Excel(name = "规则主键")
    private Long ruleId;

    /** 设置类型(0 固定文本  1 科目分级标识  2 动态字段) */
    @Excel(name = "设置类型(0 固定文本  1 科目分级标识  2 动态字段)")
    private String setType;

    /** 规则类型(来源于枚举) */
    @Excel(name = "规则类型(来源于枚举)")
    private String ruleType;

    /** 排序 */
    @Excel(name = "排序")
    private Long order;

    /**
     * 借贷类型(1 借  2 贷)
     */
    private String loanType;

    /**
     *规则条目
     */
    private Long ruleItem;

    /**
     * 开户行
     */
    private String bankOfDeposit;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 银行账户简称
     */
    private String abbreviation;

    /**
     * 项目名称
     */
    private String projectName;

    private Integer projectId;

}
