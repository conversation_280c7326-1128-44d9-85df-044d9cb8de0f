<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialAccountingCategoryDetailsMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.financial.domain.FinancialAccountingCategoryDetails">
        <!--@mbg.generated-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enable" jdbcType="BIT" property="enable"/>
        <result column="accounting_category_id" jdbcType="INTEGER" property="accountingCategoryId"/>
        <result column="cus_column_0" jdbcType="VARCHAR" property="cusColumn0"/>
        <result column="cus_column_1" jdbcType="VARCHAR" property="cusColumn1"/>
        <result column="cus_column_2" jdbcType="VARCHAR" property="cusColumn2"/>
        <result column="cus_column_3" jdbcType="VARCHAR" property="cusColumn3"/>
        <result column="cus_column_4" jdbcType="VARCHAR" property="cusColumn4"/>
        <result column="cus_column_5" jdbcType="VARCHAR" property="cusColumn5"/>
        <result column="cus_column_6" jdbcType="VARCHAR" property="cusColumn6"/>
        <result column="cus_column_7" jdbcType="VARCHAR" property="cusColumn7"/>
        <result column="cus_column_8" jdbcType="VARCHAR" property="cusColumn8"/>
        <result column="cus_column_9" jdbcType="VARCHAR" property="cusColumn9"/>
        <result column="cus_column_10" jdbcType="VARCHAR" property="cusColumn10"/>
        <result column="cus_column_11" jdbcType="VARCHAR" property="cusColumn11"/>
        <result column="cus_column_12" jdbcType="VARCHAR" property="cusColumn12"/>
        <result column="cus_column_13" jdbcType="VARCHAR" property="cusColumn13"/>
        <result column="cus_column_14" jdbcType="VARCHAR" property="cusColumn14"/>
        <result column="cus_column_15" jdbcType="VARCHAR" property="cusColumn15"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, code, `name`, remark, `enable`, accounting_category_id, cus_column_0, cus_column_1,
        cus_column_2, cus_column_3, cus_column_4, cus_column_5, cus_column_6, cus_column_7,
        cus_column_8, cus_column_9, cus_column_10, cus_column_11, cus_column_12, cus_column_13,
        cus_column_14, cus_column_15
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into financial_accounting_category_details
        (code, `name`, remark, `enable`, accounting_category_id, cus_column_0, cus_column_1,
        cus_column_2, cus_column_3, cus_column_4, cus_column_5, cus_column_6, cus_column_7,
        cus_column_8, cus_column_9, cus_column_10, cus_column_11, cus_column_12, cus_column_13,
        cus_column_14, cus_column_15)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            #{item.enable,jdbcType=BIT}, #{item.accountingCategoryId,jdbcType=INTEGER}, #{item.cusColumn0,jdbcType=VARCHAR},
            #{item.cusColumn1,jdbcType=VARCHAR}, #{item.cusColumn2,jdbcType=VARCHAR}, #{item.cusColumn3,jdbcType=VARCHAR},
            #{item.cusColumn4,jdbcType=VARCHAR}, #{item.cusColumn5,jdbcType=VARCHAR}, #{item.cusColumn6,jdbcType=VARCHAR},
            #{item.cusColumn7,jdbcType=VARCHAR}, #{item.cusColumn8,jdbcType=VARCHAR}, #{item.cusColumn9,jdbcType=VARCHAR},
            #{item.cusColumn10,jdbcType=VARCHAR}, #{item.cusColumn11,jdbcType=VARCHAR}, #{item.cusColumn12,jdbcType=VARCHAR},
            #{item.cusColumn13,jdbcType=VARCHAR}, #{item.cusColumn14,jdbcType=VARCHAR}, #{item.cusColumn15,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from financial_accounting_category_details where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByCodeSet" resultMap="BaseResultMap">
        select *
        from financial_accounting_category_details ffacd
        left join financial_accounting_category ffac on ffac.id = ffacd.accounting_category_id
        where ffac.account_sets_id = #{accountSetsId,jdbcType=INTEGER} and ffac.name = #{name,jdbcType=VARCHAR} and ffacd.code in
        <foreach collection="codeSet" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByCategoryId">
        delete from financial_accounting_category_details where accounting_category_id = #{accountingCategoryId}
    </delete>

    <select id="selectByCatetoryIdAndCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from financial_accounting_category_details
        where accounting_category_id = #{accountingCategoryId}
        <if test="code != null and code != ''">
            and code = #{code}
        </if>
        order by code
    </select>

    <select id="selectCountByCatetoryIdAndCode" resultType="Integer">
        select count(1) from financial_accounting_category_details
        where accounting_category_id = #{accountingCategoryId}
        <if test="code != null and code != ''">
            and code = #{code}
        </if>
    </select>

    <select id="selectByCatetoryIdAndCodes" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from financial_accounting_category_details
        where accounting_category_id = #{accountingCategoryId}
        and code in
        <foreach item="code" collection="codeSet" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <update id="batchUpdateById">
        update financial_accounting_category_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">`name` = #{name},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="accountingCategoryId != null">accounting_category_id = #{accountingCategoryId},</if>
            <if test="cusColumn0 != null and cusColumn0 != ''">cus_column_0 = #{cusColumn0},</if>
            <if test="cusColumn1 != null and cusColumn1 != ''">cus_column_1 = #{cusColumn1},</if>
            <if test="cusColumn2 != null and cusColumn2 != ''">cus_column_2 = #{cusColumn2},</if>
            <if test="cusColumn3 != null and cusColumn3 != ''">cus_column_3 = #{cusColumn3},</if>
            <if test="cusColumn4 != null and cusColumn4 != ''">cus_column_4 = #{cusColumn4},</if>
            <if test="cusColumn5 != null and cusColumn5 != ''">cus_column_5 = #{cusColumn5},</if>
            <if test="cusColumn6 != null and cusColumn6 != ''">cus_column_6 = #{cusColumn6},</if>
            <if test="cusColumn7 != null and cusColumn7 != ''">cus_column_7 = #{cusColumn7},</if>
            <if test="cusColumn8 != null and cusColumn8 != ''">cus_column_8 = #{cusColumn8},</if>
            <if test="cusColumn9 != null and cusColumn9 != ''">cus_column_9 = #{cusColumn9},</if>
            <if test="cusColumn10 != null and cusColumn10 != ''">cus_column_10 = #{cusColumn10},</if>
            <if test="cusColumn11 != null and cusColumn11 != ''">cus_column_11 = #{cusColumn11},</if>
            <if test="cusColumn12 != null and cusColumn12 != ''">cus_column_12 = #{cusColumn12},</if>
            <if test="cusColumn13 != null and cusColumn13 != ''">cus_column_13 = #{cusColumn13},</if>
            <if test="cusColumn14 != null and cusColumn14 != ''">cus_column_14 = #{cusColumn14},</if>
            <if test="cusColumn15 != null and cusColumn15 != ''">cus_column_15 = #{cusColumn15},</if>
        </trim>
        where id = #{id}
    </update>

    <insert id="insertCategoryDetails" parameterType="com.ruoyi.financial.domain.FinancialAccountingCategoryDetails" useGeneratedKeys="true" keyProperty="id">
        insert into financial_accounting_category_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">`name`,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="enable != null">enable,</if>
            <if test="accountingCategoryId != null">accounting_category_id,</if>
            <if test="cusColumn0 != null and cusColumn0 != ''">cus_column_0,</if>
            <if test="cusColumn1 != null and cusColumn1 != ''">cus_column_1,</if>
            <if test="cusColumn2 != null and cusColumn2 != ''">cus_column_2,</if>
            <if test="cusColumn3 != null and cusColumn3 != ''">cus_column_3,</if>
            <if test="cusColumn4 != null and cusColumn4 != ''">cus_column_4,</if>
            <if test="cusColumn5 != null and cusColumn5 != ''">cus_column_5,</if>
            <if test="cusColumn6 != null and cusColumn6 != ''">cus_column_6,</if>
            <if test="cusColumn7 != null and cusColumn7 != ''">cus_column_7,</if>
            <if test="cusColumn8 != null and cusColumn8 != ''">cus_column_8,</if>
            <if test="cusColumn9 != null and cusColumn9 != ''">cus_column_9,</if>
            <if test="cusColumn10 != null and cusColumn10 != ''">cus_column_10,</if>
            <if test="cusColumn11 != null and cusColumn11 != ''">cus_column_11,</if>
            <if test="cusColumn12 != null and cusColumn12 != ''">cus_column_12,</if>
            <if test="cusColumn13 != null and cusColumn13 != ''">cus_column_13,</if>
            <if test="cusColumn14 != null and cusColumn14 != ''">cus_column_14,</if>
            <if test="cusColumn15 != null and cusColumn15 != ''">cus_column_15,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="enable != null">#{enable},</if>
            <if test="accountingCategoryId != null">#{accountingCategoryId},</if>
            <if test="cusColumn0 != null and cusColumn0 != ''">#{cusColumn0},</if>
            <if test="cusColumn1 != null and cusColumn1 != ''">#{cusColumn1},</if>
            <if test="cusColumn2 != null and cusColumn2 != ''">#{cusColumn2},</if>
            <if test="cusColumn3 != null and cusColumn3 != ''">#{cusColumn3},</if>
            <if test="cusColumn4 != null and cusColumn4 != ''">#{cusColumn4},</if>
            <if test="cusColumn5 != null and cusColumn5 != ''">#{cusColumn5},</if>
            <if test="cusColumn6 != null and cusColumn6 != ''">#{cusColumn6},</if>
            <if test="cusColumn7 != null and cusColumn7 != ''">#{cusColumn7},</if>
            <if test="cusColumn8 != null and cusColumn8 != ''">#{cusColumn8},</if>
            <if test="cusColumn9 != null and cusColumn9 != ''">#{cusColumn9},</if>
            <if test="cusColumn10 != null and cusColumn10 != ''">#{cusColumn10},</if>
            <if test="cusColumn11 != null and cusColumn11 != ''">#{cusColumn11},</if>
            <if test="cusColumn12 != null and cusColumn12 != ''">#{cusColumn12},</if>
            <if test="cusColumn13 != null and cusColumn13 != ''">#{cusColumn13},</if>
            <if test="cusColumn14 != null and cusColumn14 != ''">#{cusColumn14},</if>
            <if test="cusColumn15 != null and cusColumn15 != ''">#{cusColumn15},</if>
        </trim>
    </insert>

    <delete id="deleteById">
        delete from financial_accounting_category_details where id = #{id}
    </delete>

    <select id="selectList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from financial_accounting_category_details
        order by id desc
    </select>

    <select id="selectListByCategoryId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from financial_accounting_category_details
        where accounting_category_id = #{accountingCategoryId} and enable = #{enable}
    </select>
</mapper>