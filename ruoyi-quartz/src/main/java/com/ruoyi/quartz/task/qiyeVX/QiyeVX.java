package com.ruoyi.quartz.task.qiyeVX;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.qiyeVX.AccessTokenUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.ruoyi.core.qiyeVX.domain.VxUser;
import org.ruoyi.core.qiyeVX.mapper.VXMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName QiyeVX.java
 * @Description TODO
 * @createTime 2023年05月23日 09:11:00
 */
public class QiyeVX {

    private static final Logger log = LoggerFactory.getLogger(QiyeVX.class);

    private String ID_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/list_id?access_token=";

    public void getVXUserId() throws IOException {

            Date now = new Date();
            AccessTokenUtils accessTockenUtil = SpringUtils.getBean(AccessTokenUtils.class);
            VXMapper vxUserMapper = SpringUtils.getBean(VXMapper.class);
            String accessToken = accessTockenUtil.getUserAccessTocken();
            List<Map<String, Object>> vxUserList = this.getVXUserList(accessToken);
            List<VxUser> vxUsers = new ArrayList<>();
            if(vxUserList.size()>0){

                for (Map<String, Object> map : vxUserList) {
                    VxUser vxUser = new VxUser();
                    vxUser.setVxId(map.get("userid").toString());
                    vxUser.setCreateTime(now);
                    vxUser.setUpdateTime(now);
                    vxUsers.add(vxUser);
                }
                log.debug("现在开始入库企业微信成员id");
                int i = vxUserMapper.batchInsert(vxUsers);
                log.debug("入库企业微信成员id已完成，此次入库或修改"+i+"条");
            }else {
                log.debug("需要入库的企业微信人员id为空");
            }


    }


    public List<Map<String,Object>> getVXUserList (String accessToken)  {

        AccessTokenUtils bean = SpringUtils.getBean(AccessTokenUtils.class);
        List<Map<String, Object>> list = new ArrayList<>();
            String url = ID_LIST_URL+accessToken;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("limit",10000);
            String json =jsonObject.toJSONString();

            String s = bean.doPostWithJson(url, json);

            JSONObject jsonObject1 = JSONObject.parseObject(s);
            if (jsonObject1.get("errcode").toString().equals("0")) {
                 list = JSONObject.parseObject(jsonObject1.get("dept_user").toString(), List.class);
            }else {
                log.debug("获取企业微信人员id出现问题，返回码为：“"+jsonObject1.get("errcode")+"“，返回码描述为：”"+jsonObject1.get("errmsg")+"“");

            }

            return list;

    }






}
