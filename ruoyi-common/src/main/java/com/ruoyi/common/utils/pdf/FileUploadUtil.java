package com.ruoyi.common.utils.pdf;

import java.io.File;



public class FileUploadUtil {
	public static final String flag = "/";
	public static final String baseUrl ="/commonFiles";
	
	
	public static String getRootPath() {
		String classPath = FileUploadUtil.class.getResource("/").getPath();
		String rootPath = "";
		//windows下
		if("\\".equals(File.separator)){
		rootPath = classPath.substring(1,classPath.indexOf("/WEB-INF/classes"));
		rootPath = rootPath.replace("/", "\\");
		}
		//linux下
		if("/".equals(File.separator)){
		rootPath = classPath.substring(0,classPath.indexOf("/WEB-INF/classes"));
		rootPath = rootPath.replace("\\", "/");
		}
		return rootPath;
	}
	
}
