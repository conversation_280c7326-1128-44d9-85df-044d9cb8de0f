package com.ruoyi.common.utils.encryption;

import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.UnsupportedEncodingException;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class RSAUtils {

	public static final String CHARSET = "UTF-8";
	public static final String RSA_ALGORITHM = "RSA";
	public static final String SIGNATURE_ALGORITHM="MD5withRSA";

	/**
	 * 根据密钥长度生成公钥私钥（经过base64编码）
	 * @param keySize
	 * @return
	 */
	public static Map<String, String> createKeys(int keySize){
		//为RSA算法创建一个KeyPairGenerator对象
		KeyPairGenerator kpg;
		try{
			kpg = KeyPairGenerator.getInstance(RSA_ALGORITHM);
		}catch(NoSuchAlgorithmException e){
			throw new IllegalArgumentException("没有找到该算法-->[" + RSA_ALGORITHM + "]");
		}

		//初始化KeyPairGenerator对象,密钥长度
		kpg.initialize(keySize);
		//生成密匙对
		KeyPair keyPair = kpg.generateKeyPair();
		//得到公钥
		Key publicKey = keyPair.getPublic();
		String publicKeyStr = Base64.getUrlEncoder().encodeToString(publicKey.getEncoded());

		//得到私钥
		Key privateKey = keyPair.getPrivate();
		String privateKeyStr = Base64.getUrlEncoder().encodeToString(privateKey.getEncoded());

		Map<String, String> keyPairMap = new HashMap<String, String>();
		keyPairMap.put("publicKey", publicKeyStr);
		keyPairMap.put("privateKey", privateKeyStr);

		return keyPairMap;
	}
	//显示字节数组
	/*private static String  showByteArray(byte[] data){
		if(null == data){
			return null;
		}
		StringBuilder sb = new StringBuilder("{");
		for(byte b:data){
			sb.append(b).append(",");
		}
		sb.deleteCharAt(sb.length()-1);
		sb.append("}");
		return sb.toString();
	}*/

	/**
	 * 得到公钥
	 * @param publicKey 密钥字符串（经过base64编码）
	 * @throws Exception
	 */
	public static RSAPublicKey getPublicKey(String publicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
		//通过X509编码的Key指令获得公钥对象
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
//		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
		X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(Base64.getUrlDecoder().decode(publicKey));
		RSAPublicKey key = (RSAPublicKey) keyFactory.generatePublic(x509KeySpec);
		return key;
	}

	/**
	 * 得到私钥
	 * @param privateKey 密钥字符串（经过base64编码）
	 * @throws Exception
	 */
	public static RSAPrivateKey getPrivateKey(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
		//通过PKCS#8编码的Key指令获得私钥对象
		KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
		PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(Base64.getUrlDecoder().decode(privateKey));
		RSAPrivateKey key = (RSAPrivateKey) keyFactory.generatePrivate(pkcs8KeySpec);
		return key;
	}

	/**
	 * 公钥加密
	 * @param data
	 * @param publicKey
	 * @return
	 */
	public static String publicEncrypt(String data, RSAPublicKey publicKey){
		try{
			Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, publicKey);
			return Base64.getUrlEncoder().encodeToString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(CHARSET), publicKey.getModulus().bitLength()));
		}catch(Exception e){
			throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
		}
	}

	/**
	 * 私钥解密
	 * @param data
	 * @param privateKey
	 * @return
	 */

	public static String privateDecrypt(String data, RSAPrivateKey privateKey){
		try{
			Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, privateKey);
			return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, Base64.getUrlDecoder().decode(data), privateKey.getModulus().bitLength()), CHARSET);
		}catch(Exception e){
			throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
		}
	}

//	/**
//	 * 私钥加密
//	 * @param data
//	 * @param privateKey
//	 * @return
//	 */
//
//	public static String privateEncrypt(String data, RSAPrivateKey privateKey){
//		try{
//			Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
//			cipher.init(Cipher.ENCRYPT_MODE, privateKey);
//			return Base64.getUrlEncoder().encodeToString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(CHARSET), privateKey.getModulus().bitLength()));
//		}catch(Exception e){
//			throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
//		}
//	}


	/**
	 * 私钥加密
	 * @param data
	 * @param privateKey
	 * @return
	 */

	public static String privateEncrypt(String data, RSAPrivateKey privateKey,String charSet){
		try{
			Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, privateKey);
			return Base64.getUrlEncoder().encodeToString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(charSet), privateKey.getModulus().bitLength()));
		}catch(Exception e){
			throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
		}
	}

	/**
	 * 公钥解密
	 * @param data
	 * @param publicKey
	 * @return
	 */

	public static String publicDecrypt(String data, RSAPublicKey publicKey){
		try{
			Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, publicKey);
			return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, Base64.getUrlDecoder().decode(data), publicKey.getModulus().bitLength()), CHARSET);
		}catch(Exception e){
			throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
		}
	}

	private static byte[] rsaSplitCodec(Cipher cipher, int opmode, byte[] datas, int keySize){
		int maxBlock = 0;
		if(opmode == Cipher.DECRYPT_MODE){
			maxBlock = keySize / 8;
		}else{
			maxBlock = keySize / 8 - 11;
		}
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		int offSet = 0;
		byte[] buff;
		int i = 0;
		try{
			while(datas.length > offSet){
				if(datas.length-offSet > maxBlock){
					buff = cipher.doFinal(datas, offSet, maxBlock);
				}else{
					buff = cipher.doFinal(datas, offSet, datas.length-offSet);
				}
				out.write(buff, 0, buff.length);
				i++;
				offSet = i * maxBlock;
			}
		}catch(Exception e){
			throw new RuntimeException("加解密阀值为["+maxBlock+"]的数据时发生异常", e);
		}
		byte[] resultDatas = out.toByteArray();
		IOUtils.closeQuietly(out);
		return resultDatas;
	}


	/**
	 * 私钥生成数字签名
	 * @param data 数据原文
	 * @param privateKey 私钥
	 * @return
	 * @throws Exception
	 */
	public static String sign(String data,String privateKey,String workType,String charSet){
		//解密私钥
		byte[] keyBytes = Base64.getUrlDecoder().decode(privateKey);
		//构造PKCS8EncodedKeySpec对象
		PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
		//指定加密算法
		KeyFactory keyFactory=null;
		try {
			keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("设置数字签名加密算法失败！无效的算法！", e);
		}
		//取私钥匙对象
		PrivateKey privateKey2 = null;
		try {
			privateKey2 = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
		} catch (InvalidKeySpecException e) {
			throw new RuntimeException("私钥对象转换失败！无效的私钥描述！", e);
		}
		//用私钥对信息生成数字签名
		Signature signature=null;
		try {
			signature = Signature.getInstance(workType);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("设置数字签名算法失败！无效的算法！", e);
		}
		try {
			signature.initSign(privateKey2);
		} catch (InvalidKeyException e) {
			throw new RuntimeException("设置数字签名私钥失败！无效的私钥！", e);
		}
		try {
			signature.update(data.getBytes(charSet));
		} catch (SignatureException e) {
			throw new RuntimeException("设置数字签名数据失败！", e);
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException("设置数字签名数据失败！不支持的编码！", e);
		}
		try {
			return Base64.getUrlEncoder().encodeToString(signature.sign());
		} catch (SignatureException e) {
			throw new RuntimeException("生成数字签名失败！", e);
		}
	}

	/**
	 * 私钥生成数字签名
	 * @param data 数据原文
	 * @param privateKey 私钥
	 * @return
	 * @throws Exception
	 */
	public static String sign(String data,String privateKey){
		//解密私钥
		byte[] keyBytes = Base64.getUrlDecoder().decode(privateKey);
		//构造PKCS8EncodedKeySpec对象
		PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(keyBytes);
		//指定加密算法
		KeyFactory keyFactory=null;
		try {
			keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("设置数字签名加密算法失败！无效的算法！", e);
		}
		//取私钥匙对象
		PrivateKey privateKey2 = null;
		try {
			privateKey2 = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
		} catch (InvalidKeySpecException e) {
			throw new RuntimeException("私钥对象转换失败！无效的私钥描述！", e);
		}
		//用私钥对信息生成数字签名
		Signature signature=null;
		try {
			signature = Signature.getInstance(SIGNATURE_ALGORITHM);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("设置数字签名算法失败！无效的算法！", e);
		}
		try {
			signature.initSign(privateKey2);
		} catch (InvalidKeyException e) {
			throw new RuntimeException("设置数字签名私钥失败！无效的私钥！", e);
		}
		try {
			signature.update(data.getBytes(CHARSET));
		} catch (SignatureException e) {
			throw new RuntimeException("设置数字签名数据失败！", e);
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException("设置数字签名数据失败！不支持的编码！", e);
		}
		try {
			return Base64.getUrlEncoder().encodeToString(signature.sign());
		} catch (SignatureException e) {
			throw new RuntimeException("生成数字签名失败！", e);
		}
	}
	/**
	 * 公钥校验数字签名
	 * @param data 数据原文
	 * @param publicKey 公钥
	 * @param sign 数字签名
	 * @return
	 * @throws Exception
	 */
	public static boolean verify(String data,String publicKey,String sign,String workType,String charSet){
		//解密公钥
		byte[] keyBytes = Base64.getUrlDecoder().decode(publicKey);
		//构造X509EncodedKeySpec对象
		X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(keyBytes);
		//指定加密算法
		KeyFactory keyFactory=null;
		try {
			keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("设置数字签名验证加密算法失败！无效的算法！", e);
		}
		//取公钥匙对象
		PublicKey publicKey2=null;
		try {
			publicKey2 = keyFactory.generatePublic(x509EncodedKeySpec);
		} catch (InvalidKeySpecException e) {
			throw new RuntimeException("公钥对象转换失败！无效的公钥描述！", e);
		}

		Signature signature=null;
		try {
			signature = Signature.getInstance(workType);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException("设置数字签名验证算法失败！无效的算法！", e);
		}
		try {
			signature.initVerify(publicKey2);
		} catch (InvalidKeyException e1) {
			throw new RuntimeException("设置数字签名验证公钥失败！无效的公钥！", e1);
		}
		try {
			signature.update(data.getBytes(charSet));
		} catch (SignatureException e) {
			throw new RuntimeException("设置数字签名验证数据失败！", e);
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException("设置数字签名验证数据失败！不支持的编码！", e);
		}
		//验证签名是否正常
		try {
			return signature.verify(Base64.getUrlDecoder().decode(sign));
		} catch (SignatureException e) {
			throw new RuntimeException("数字签名验证失败！", e);
		}

	}




}