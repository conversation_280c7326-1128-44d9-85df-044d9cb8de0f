package com.ruoyi.common.enums;

import com.alibaba.fastjson.JSONObject;

/**
 * @className: RequestCodeEnums
 * @packageName: com.ruoyi.common.enums
 * @createTime: 2022/3/11 15:23
 * @author: 左东冉
 * @description: 请求代码的枚举类
 **/
public enum RequestCodeEnums {


    SEARCH_BASICS_DATA("72722", "test", JSONObject.class, "001");


    // 请求编码
    private final String code;
    // 请求方法名字
    private final String methodName;
    // 返回的实体类 Class
    private final Class aClass;
    // redis 中存储的保存key值 对应请求码
    private final String keyCode;

    public Class getaClass() {
        return aClass;
    }

    public String getKeyCode() {
        return keyCode;
    }

    RequestCodeEnums(String code, String methodName, Class aClass, String keyCode) {
        this.code = code;
        this.methodName = methodName;
        this.aClass = aClass;
        this.keyCode = keyCode;
    }

    public String getCode() {
        return code;
    }

    public static String getCode(String methodName) {
        for (RequestCodeEnums value : RequestCodeEnums.values()) {
            if (value.getMethodName().equals(methodName)) {
                return value.getCode();
            }
        }
        return null;
    }

    public String getMethodName() {
        return methodName;
    }

    public static String getMethodName(String code) {
        for (RequestCodeEnums value : RequestCodeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getMethodName();
            }
        }
        return null;
    }


    public static Class getClassCoverObject(String code) {
        for (RequestCodeEnums value : RequestCodeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getaClass();
            }
        }
        return null;
    }

    /**
     * @param code 请求代码
     * @return 保存在redis 中的值
     */
    public static String getKeyCodeByRequestCode(String code) {
        for (RequestCodeEnums value : RequestCodeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getKeyCode();
            }
        }
        return null;
    }

    /**
     * @param code 请求代码
     * @return 保存在redis 中的值
     */
    public static RequestCodeEnums getRequestCodeEnums(String code) {
        for (RequestCodeEnums value : RequestCodeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
