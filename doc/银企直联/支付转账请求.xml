<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>DLINTTRN</action>
    <userName></userName><!--登录名varchar(30)-->
    <list name="userDataList">
    <row>
        <clientID></clientID><!--客户流水号 char(20)-->
        <preFlg></preFlg><!--预约支付标志 0：非预约；1：预约交易 ;2：次日 char(1)-->
        <preDate></preDate><!--延期支付日期char(8) 格式YYYYMMDD ，预约时非空-->
        <preTime></preTime><!--延期支付时间char(6) 格式hhmmss ，预约时非空-->
        <payType></payType><!--支付方式 1：跨行转账；2：行内；3：企业内部转账 char(1)-->
        <payFlg></payFlg><!--支付时效 0：加急；1普通 char(1)-->
        <payAccountNo></payAccountNo><!--付款账号 char(19)-->
        <recAccountNo></recAccountNo><!--收款账号 char(32)-->
        <recAccountName></recAccountName><!--收款账号名称 varchar(122)-->
        <!--当payType为1(跨行转账)时，收款账号开户行名与收款账号开户行联行网点号至少输一项  begin-->
        <recOpenBankName></recOpenBankName><!--收款账号开户行varchar(122)可空-->
        <recOpenBankCode></recOpenBankCode><!--收款账号开户行联行网点号 varchar(20)可空-->
        <!--当payType为1(跨行转账)时，收款账号开户行名与收款账号开户行联行网点号至少输一项  end-->
        <tranAmount></tranAmount><!--金额 decimal(15,2)-->
        <abstract></abstract><!--附言 varchar(102) 必输-->
        <memo></memo><!--备注 varchar(60) ，可空-->
        <chkNum></chkNum><!--对账编号char(20)，可空-->
    </row>
</list>
</stream>
