#类别维护表
CREATE TABLE `off_category_main` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `category_name` varchar(50) DEFAULT NULL COMMENT '类别名称',
  `parent_id` bigint(11) DEFAULT NULL COMMENT '上级类别',
  `company_id` bigint(11) DEFAULT NULL COMMENT '所属公司',
  `status` char(2) DEFAULT NULL COMMENT '启用状态(0启用 1停用)',
  `short_num` int(4) DEFAULT NULL COMMENT '排序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标识(0未删除 1已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='类别维护表';

#预警人员表
CREATE TABLE `off_notify_user` (
  `category_id` bigint(11) NOT NULL COMMENT '类别id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品库存预警表';

#办公用品申请详情表
CREATE TABLE `off_receive_detail` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `receive_id` bigint(11) DEFAULT NULL COMMENT '领用申请ID',
  `before_num` int(11) DEFAULT NULL COMMENT '申请前数量',
  `supply_id` bigint(11) DEFAULT NULL COMMENT '物品ID',
  `apply_num` int(11) DEFAULT NULL COMMENT '申请数量',
  `residue_num` int(11) DEFAULT NULL COMMENT '剩余数量',
  `process_id` varchar(64) DEFAULT NULL COMMENT '流程ID',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品申请详情表';

#办公用品领用主表
CREATE TABLE `off_receive_main` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `application_code` varchar(32) DEFAULT NULL COMMENT '申请单编号',
  `user_id` bigint(11) DEFAULT NULL COMMENT '申请人id',
  `process_id` varchar(64) DEFAULT NULL COMMENT '流程id',
  `approval_status` varchar(2) DEFAULT NULL COMMENT '状态(0未提交 1审核中 2审核不通过 3审核通过)',
  `company_id` bigint(11) DEFAULT NULL COMMENT '所属公司id',
  `del_flag` char(1) DEFAULT NULL COMMENT '删除标识(0未删除 1已删除)',
  `cause` varchar(500) DEFAULT NULL COMMENT '申请事由',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `approval_time` datetime DEFAULT NULL COMMENT '审核通过时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_process_id` (`process_id`) USING BTREE,
  KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品领用主表';

#办公用品表单
CREATE TABLE `off_receive_purchase_detail` (
  `process_id` varchar(64) DEFAULT NULL COMMENT '流程id',
  `json_data` json DEFAULT NULL COMMENT '表单json',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品表单';

#办公用品附件表
CREATE TABLE `off_supply_files` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `file_type` varchar(2) DEFAULT NULL COMMENT '文件类型(1办公用品图片 2办公用品领用 3办公用品采购发票 4其他附件 5办公用品附件)',
  `relevancy_id` bigint(11) DEFAULT NULL COMMENT '关联id',
  `file_path` varchar(200) DEFAULT NULL COMMENT '文件路径',
  `file_name` varchar(60) DEFAULT NULL COMMENT '文件名',
  `status` char(1) DEFAULT NULL COMMENT '状态(0最新 1历史)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品附件表';

#办公用品修改记录表
CREATE TABLE `off_supply_history` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `supply_id` bigint(11) DEFAULT NULL COMMENT '物品id',
  `supply_sys_code` varchar(64) DEFAULT NULL COMMENT '物品系统编号',
  `old_supply_name` varchar(64) DEFAULT NULL COMMENT '修改前物品名称',
  `new_supply_name` varchar(64) DEFAULT NULL COMMENT '修改后物品名称',
  `old_amount` int(9) DEFAULT NULL COMMENT '修改前数量',
  `new_amount` int(9) DEFAULT NULL COMMENT '修改后数量',
  `old_json` json DEFAULT NULL COMMENT '修改前的数据详情',
  `new_json` json DEFAULT NULL COMMENT '修改后的数据详情',
  `update_remark` varchar(500) DEFAULT NULL COMMENT '修改原因',
  `update_by` varchar(32) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品修改记录表';

#办公用品维护表
CREATE TABLE `off_supply_main` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `item_name` varchar(50) DEFAULT NULL COMMENT '物品名称',
  `sys_code` varchar(64) DEFAULT NULL COMMENT '系统编号',
  `item_type` varchar(8) DEFAULT NULL COMMENT '物品类型(1办公用品 2礼品)',
  `category_id` bigint(11) DEFAULT NULL COMMENT '所属类别',
  `measure_unit` varchar(9) DEFAULT NULL COMMENT '计量单位',
  `specification` varchar(40) DEFAULT NULL COMMENT '型号或规格',
  `amount` int(11) DEFAULT NULL COMMENT '数量',
  `expiration_date` datetime DEFAULT NULL COMMENT '保质期',
  `is_use_notify` varchar(2) DEFAULT NULL COMMENT '到期提醒(0开启 1关闭)',
  `notify_type` char(2) DEFAULT NULL COMMENT '通知形式(0系统代办 1企业微信 3所有)',
  `before_num` int(11) DEFAULT NULL COMMENT '到期前n天进行提醒',
  `negative_inventory` varchar(2) DEFAULT NULL COMMENT '负库存申请(0开启 1关闭)',
  `status` char(1) DEFAULT NULL COMMENT '启停状态(0启用 1停用)',
  `item_warning` int(6) DEFAULT NULL COMMENT '库存警示阈值',
  `version` int(6) DEFAULT NULL COMMENT '版本号',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标识(0未删除 1已删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_category_id` (`category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品维护表';

#办公用品采购表
CREATE TABLE `off_supply_purchase` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `supply_id` bigint(11) DEFAULT NULL COMMENT '物品id',
  `amount` int(11) DEFAULT NULL COMMENT '物品数量',
  `orders_code` varchar(64) DEFAULT NULL COMMENT '采购单编号',
  `price` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT NULL COMMENT '总价',
  `discount_rate` varchar(9) DEFAULT NULL COMMENT '折扣率',
  `total_payable` decimal(10,2) DEFAULT NULL COMMENT '应付总额',
  `purchase_date` datetime DEFAULT NULL COMMENT '采购日期',
  `status` char(1) DEFAULT NULL COMMENT '状态(0未提交 1审核中 2审核不通过 3审核通过)',
  `company_id` bigint(11) DEFAULT NULL COMMENT '所属公司id',
  `add_uuid` varchar(64) DEFAULT NULL COMMENT '同一批采购标识',
  `process_id` varchar(64) DEFAULT NULL COMMENT '流程id',
  `remark` varchar(600) DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) DEFAULT NULL COMMENT '删除标识(0未删除 1已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='办公用品采购单';


#初始化办公用品类别一级目录
INSERT INTO off_category_main ( category_name, parent_id, company_id, STATUS, short_num, del_flag, create_by, create_time )
	(
SELECT DISTINCT company_short_name, 0, id, '0', 0, '0',	'admin', now() FROM sys_company WHERE is_inside = '1' AND STATUS = '0' AND is_delete = '0' AND ( check_status = '3' OR check_status IS NULL )
);


#初始化办公用品类型字典
INSERT INTO `sys_dict_type`( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '办公物品类型', 'office_supply_type', '0', 'admin', now(), '',  NULL, '办公物品类型');
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `auxiliary_field`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1, '办公用品', 'BGYP', 'office_supply_type', NULL, NULL, 'default', 'N', '0', 'admin', NULL, '', NULL, NULL);
INSERT INTO `sys_dict_data`( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `auxiliary_field`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '礼品', 'LP', 'office_supply_type', NULL, NULL, 'default', 'N', '0', 'admin', NULL, '', NULL, NULL);


#初始化oa关联流程模板
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `auxiliary_field`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (45, '办公用品采购', 'offSupplyPurchase', 'oaModule_type', NULL, NULL, 'default', 'N', '0', 'admin', now(), 'admin',NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `auxiliary_field`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (44, '礼品领用', 'offGiftReceive', 'oaModule_type', NULL, NULL, 'default', 'N', '0', 'admin', now(), 'admin',NULL, NULL);
INSERT INTO `sys_dict_data`(`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `auxiliary_field`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (43, '办公用品领用', 'offSupplyReceive', 'oaModule_type', NULL, NULL, 'default', 'N', '0', 'admin', now(), 'admin', NULL, NULL);


#办公用品按钮父菜单ID
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('办公用品领用', 0, 2, 'officeSupplies', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'component', 'admin', now(), '', NULL, '');
SELECT @menuParendId := LAST_INSERT_ID();

#类别维护菜单
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('类别维护', @menuParendId, 1, 'category', 'officeSupplies/category', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', now(), '', NULL, '');
SELECT @menuCategoryId := LAST_INSERT_ID();
#按钮
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('删除-按钮', @menuCategoryId, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'categroy:del', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导出-按钮', @menuCategoryId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'categroy:export', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('新增/修改类别-按钮', @menuCategoryId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'categroy:addEdit', '#', 'admin', now(), 'admin', NULL, '');

#办公用品维护菜单
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('办公用品维护', @menuParendId, 2, 'maintenance', 'officeSupplies/maintenance', NULL, 1, 1, 'C', '0', '0', '', '#', 'admin', now(), 'admin', NULL, '');
SELECT @menuSupplyId := LAST_INSERT_ID();
#按钮
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('删除-按钮', @menuSupplyId, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'maintenance:del', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('物品整理-按钮', @menuSupplyId, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'maintenance:changeGroy', '#', 'admin', now(), 'admin', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导出-按钮', @menuSupplyId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'maintenance:export', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('新增/修改-按钮', @menuSupplyId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'maintenance:addEdit', '#', 'admin', now(), '', NULL, '');

#办公用品领用菜单
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('办公用品领用', @menuParendId, 3, 'officeUse', 'officeSupplies/officeUse', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', now(), '', NULL, '');
SELECT @menuOffUseId := LAST_INSERT_ID();
#按钮
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('删除-按钮', @menuOffUseId, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'officeUse:del', '#', 'admin', now(), 'admin', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('提交流程-按钮', @menuOffUseId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'officeUse:submit', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('新增/修改办公/礼品领用申请-按钮', @menuOffUseId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'officeUse:addEdit', '#', 'admin', now(), 'admin', NULL, '');

#物品领用报表
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('物品领用报表', @menuParendId, 4, 'useReport', 'officeSupplies/useReport', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', '2025-04-02 10:34:11', '', NULL, '');
SELECT @menuExportId := LAST_INSERT_ID();
#按钮
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导出-按钮', @menuExportId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'useReport:export', '#', 'admin', now(), '', NULL, '');

#办公用品采购单
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('办公用品采购单', @menuParendId, 5, 'purchase', 'officeSupplies/purchase', NULL, 1, 0, 'C', '0', '0', NULL, '#', 'admin', now(), '', NULL, '');
SELECT @menuPurchaseId := LAST_INSERT_ID();

INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('删除-按钮', @menuPurchaseId, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'purchase:del', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('导出-按钮', @menuPurchaseId, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'purchase:export', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('发起流程-按钮', @menuPurchaseId, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'purchase:submit', '#', 'admin', now(), '', NULL, '');
INSERT INTO `sys_menu`(`menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('新增/修改按钮', @menuPurchaseId, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'purchase:addEdit', '#', 'admin', now(), '', NULL, '');

#定时任务
INSERT INTO `sys_job`( `job_name`, `job_group`, `invoke_target`, `cron_expression`, `misfire_policy`, `concurrent`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('办公用品到期前n天给物品保管员发送通知', 'DEFAULT', 'com.ruoyi.quartz.task.core.OffSupplyMainTask.sendNotifyByOffSupplyExpirationDate()', '0 15 0 * * ?', '1', '1', '0', 'admin', NOW(), '', NULL, '');





