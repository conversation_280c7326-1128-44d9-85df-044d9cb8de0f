/******************************************/
/*   DatabaseName = prd_spark_db   */
/*   TableName = dim_bill_no_info   */
/******************************************/
CREATE TABLE `dim_bill_no_info` (
  `bill_app_no` varchar(100) NOT NULL COMMENT '借据编号',
  `loan_month` varchar(7) COMMENT '放款月份',
  `product_no` varchar(100) COMMENT '产品编号',
  `loan_date` date COMMENT '放款日期',
  `due_date` date COMMENT '到期日期',
  `loan_amt` decimal(17, 2) COMMENT '放款金额',
  `loan_period` int COMMENT '借款期限',
  `loan_term` int COMMENT '分期期数',
  `guarantor_no` varchar(10) COMMENT '担保公司',
  `id_card_type` varchar(3) COMMENT '证件类型',
  `id_card` varchar(21) COMMENT '证件号码',
  `mobile` varchar(11) COMMENT '手机号',
  `name` varchar(20) COMMENT '客户姓名',
  `farmer_flag` varchar(1) COMMENT '农户标识',
  `data_source` varchar(1) COMMENT '数据来源',
  `province_id` varchar(10) COMMENT '用户所在省份',
  `city_id` varchar(10) COMMENT '用户所在市',
  `dw_create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `dw_update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  KEY `idx_bill_app_no` (`bill_app_no`),
  PRIMARY KEY (`bill_app_no`)
)  COMMENT='借据信息表';



/******************************************/
/*   DatabaseName = prd_spark_db   */
/*   TableName = dim_base_prod_info   */
/******************************************/
CREATE TABLE `dim_base_prod_info` (
  `id` bigint NOT NULL COMMENT '主键',
  `product_no` varchar(64) COMMENT '产品编号',
  `product_name` varchar(100) COMMENT '产品名称',
  `guarantor_no` varchar(50) COMMENT '担保方编号',
  `guarantor_name` varchar(50) COMMENT '担保方',
  `platform_no` varchar(50) COMMENT '平台方编号',
  `platform_name` varchar(50) COMMENT '平台方',
  `financer_no` varchar(50) COMMENT '资金方编号',
  `financer_name` varchar(50) COMMENT '资金方',
  `project_no` varchar(64) COMMENT '项目编号',
  `system_no` bigint COMMENT '系统',
  `oa_platform_no` varchar(100) COMMENT '外部系统平台编码',
  `oa_cust_no` varchar(100) COMMENT '担保公司编码',
  `oa_partner_no` varchar(100) COMMENT '合作方编码',
  `oa_fund_no` varchar(100) COMMENT '资金方编码',
  `oa_it_no` varchar(100) COMMENT '科技方编码',
  `is_project_finish` varchar(1) COMMENT '项目是否已结束,Y是N否',
  `project_finish_date` date COMMENT '项目结束日期',
  `is_project_company` varchar(1) COMMENT '客户是否为企业,Y是C同时含有个人N否',
  `is_project_td` varchar(1) COMMENT '借据是否通道简版数据,Y是N否',
  `is_project_plan` varchar(1) COMMENT '借据是否有还款计划数据,Y有N否',
  `is_project_plan_update` varchar(1) COMMENT '借据是否有还款计划实还更新数据,Y有C有但未上线N否',
  `is_project_plan_reset` varchar(1) COMMENT '借据是否有还款计划缩期情况,Y有C有但未上线N否',
  `is_project_repay1` varchar(1) COMMENT '借据是否有正常还款数据,Y有C有但未上线N否',
  `is_project_repay4` varchar(1) COMMENT '借据是否有提前还款数据,Y有C有但未上线N否',
  `is_project_repay5` varchar(1) COMMENT '借据是否有提前结清数据,Y有C有但未上线N否',
  `is_project_repay7` varchar(1) COMMENT '借据是否有代偿还款数据,Y有C有但未上线N否',
  `is_project_total_repay7` varchar(1) COMMENT '借据是否有累计代偿还款数据,Y有C有但未上线N否',
  `is_project_total_repay` varchar(1) COMMENT '借据是否有用户累计还款数据,Y有C有但未上线N否',
  `is_project_repay8` varchar(1) COMMENT '借据是否有追偿还款数据,Y有C有但未上线N否',
  `is_project_repay7_finish` varchar(1) COMMENT '借据是否代偿时结清,Y是N否',
  `is_project_repay8_normal` varchar(1) COMMENT '借据追偿还款是否按正常还款提供,Y是N否',
  `is_result_fpd10` varchar(1) COMMENT 'FPD10是否可用，Y是N否',
  `is_result_vintage` varchar(1) COMMENT 'vintage是否可用，Y是N否',
  `is_result_balance_distribution` varchar(1) COMMENT '余额分布是否可用，Y是N否',
  `remark` varchar(255) COMMENT '备注',
  `status` varchar(1) COMMENT '状态（0正常 1停用）',
  `check_status` varchar(1) COMMENT '审核状态 0新增审核状态，1修改项目审核中，2删除项目审核中，3（-）',
  `description` varchar(500) COMMENT '说明',
  `create_time` timestamp,
  `update_time` timestamp,
  `create_by` varchar(64) COMMENT '创建者',
  `update_by` varchar(64) COMMENT '更新人',
  `biz_type` varchar(2) COMMENT '业务类型 01-个人业务 02-对公业务',
  `product_type` varchar(2) COMMENT '产品类型 11-个人住房商业贷款 12-个人商用房（含商住两用）贷款 13-个人住房公积金贷款 21-个人汽车消费贷款 41-个人经营性贷款 42-个人创业担保贷款 51-农户贷款 52-经营性农户贷款 53-消费性农户贷款 91-其他个人消费贷款 99-其他贷款',
  `min_credit_limit` bigint DEFAULT '0' COMMENT '最低授信额度',
  `max_credit_limit` bigint DEFAULT '0' COMMENT '最高授信额度',
  `min_inte_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '最低利率 年化小数',
  `max_inte_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '最高利率  年化小数',
  `min_credit_spread` int DEFAULT '0' COMMENT '最短授信期限',
  `max_credit_spread` int DEFAULT '0' COMMENT '最长授信期限',
  `grt_method` varchar(2) COMMENT '担保方式 01-信用 02-抵押 03-质押',
  `repayment_method` varchar(2) COMMENT '还款方式 11-分期等额本息 12-分期等额本金 13-到期还本分期结息 21-到期一次还本付息 90-不区分还款方式',
  `prepayment` varchar(1) COMMENT '是否允许提前还款 0-可以提前还款 1-不可以提前还款',
  `repayment_data` tinyint COMMENT '还款数据优先级 1-优先还款信息 2-优先还款计划',
  `act_year_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '利率（实际年化）',
  `service_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期服务费费率',
  `guarantee_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期担保费费率',
  `margin_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期保证金费率',
  `compensate_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '前期代偿金费率',
  `period_service_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化服务费费率',
  `period_guarantee_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化担保费费率',
  `period_margin_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化保证金费率',
  `period_compensate_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '年化代偿金费率',
  `oint_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '罚息日利率',
  `define_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '逾期违约金/滞纳金费率',
  `adv_define_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '提前还款违约金费率',
  `compensate_days` smallint COMMENT '代偿天数',
  `cpst_rule_no` varchar(5) COMMENT '代偿规则编号',
  `grace_day` smallint COMMENT '宽限期',
  `interest_free_period` smallint COMMENT '免息期',
  `is_cpst` varchar(1) COMMENT '是否提供代偿 0-是 1-否',
  `is_recovery` varchar(1) COMMENT '是否提供追偿 0-是 1-否',
  `fee1_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率1',
  `fee2_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率2',
  `fee3_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率3',
  `fee4_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率4',
  `fee5_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率5',
  `fee6_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率6',
  `fee7_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率7',
  `fee8_rate` decimal(11, 8) DEFAULT '0.00000000' COMMENT '费率8',
  `dw_create_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `dw_update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_end` varchar(1) COMMENT '是否结束 Y是  N否',
  PRIMARY KEY (`id`)
) COMMENT='产品信息表'
;
