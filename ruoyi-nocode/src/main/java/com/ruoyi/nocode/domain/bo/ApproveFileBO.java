package com.ruoyi.nocode.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 审批流程中的文件
 *
 * @Description
 * <AUTHOR>
 * @Date 2023/9/20 10:24
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ApproveFileBO {
    private static final long serialVersionUID = 1L;

    //源文件名（可能用于以后的文件页面展示）
    private String originalName;

    //获取文件的地址
    private String url;
}
