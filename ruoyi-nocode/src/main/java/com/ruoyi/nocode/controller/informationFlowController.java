package com.ruoyi.nocode.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.nocode.domain.NoaFlowInfomation;
import com.ruoyi.nocode.service.IInformationFlowService;
import com.ruoyi.nocode.service.IProcFormDefService;
import org.ruoyi.core.information.domain.Information;
import org.ruoyi.core.oasystem.domain.OaProcessTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/informationFlowController")
public class informationFlowController extends BaseController {
    @Autowired
    private IInformationFlowService informationFlowService;
    @Log(title = "资料流程", businessType = BusinessType.UPDATE)
    @PostMapping("/startInformationFlow")
    public AjaxResult startInformationFlow() {
        return informationFlowService.startInformationFlow();
    }

    @Log(title = "用印流程", businessType = BusinessType.UPDATE)
    @PostMapping("/startInformationUseFlow")
    public AjaxResult startInformationUseFlow(@RequestBody String theme) {
        return informationFlowService.startInformationUseFlow(theme);
    }

    @Log(title = "下载流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getDownloadFlow")
    public AjaxResult getDownloadFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getDownloadFlow(oaProcessTemplate);
    }

    @Log(title = "资料流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getInformationFlow")
    public AjaxResult getInformationFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getInformationFlow(oaProcessTemplate);
    }

    @Log(title = "用印流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getInformationUsedFlow")
    public AjaxResult getInformationUsedFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getInformationUsedFlow(oaProcessTemplate);
    }

    @Log(title = "直接用印流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getInformationDirectUsedFlow")
    public AjaxResult getInformationDirectUsedFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getInformationDirectUsedFlow(oaProcessTemplate);
    }

    /**
     * 根据选择的公司查询证照外借流程
     * @param oaProcessTemplate
     * @return
     */
    @Log(title = "证照外借流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getLicenseFlow")
    public AjaxResult getLicenseFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getLicenseFlow(oaProcessTemplate);
    }
    @GetMapping("/flowFullId")
    public TableDataInfo getListByIds(String flowFullId)
    {
        List<String> list = informationFlowService.getNodeToFlow(flowFullId);
        return getDataTable(list);
    }


    /**
     * 根据选择的公司查询证照外借流程
     * @param oaProcessTemplate
     * @return
     */
    @Log(title = "会议流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getMeetingFlow")
    public AjaxResult getMeetingFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getMeetingFlow(oaProcessTemplate);
    }

    @Log(title = "监管下载流程", businessType = BusinessType.UPDATE)
    @GetMapping("/supervise/getDownloadFlow")
    public AjaxResult getSuperviseDownloadFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSuperviseDownloadFlow(oaProcessTemplate);
    }

    @Log(title = "监管资料流程", businessType = BusinessType.UPDATE)
    @GetMapping("/supervise/getInformationFlow")
    public AjaxResult getSuperviseInformationFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSuperviseInformationFlow(oaProcessTemplate);
    }

    @Log(title = "监管(投诉)资料流程", businessType = BusinessType.UPDATE)
    @GetMapping("/supervise/ts/getInformationFlow")
    public AjaxResult getSuperviseJGTSInformationFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSuperviseJGTSInformationFlow(oaProcessTemplate);
    }
    @Log(title = "监管(风险)资料流程", businessType = BusinessType.UPDATE)
    @GetMapping("/supervise/fx/getInformationFlow")
    public AjaxResult getSuperviseJGFXInformationFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSuperviseJGFXInformationFlow(oaProcessTemplate);
    }
    @Log(title = "监管(会计)资料流程", businessType = BusinessType.UPDATE)
    @GetMapping("/supervise/kj/getInformationFlow")
    public AjaxResult getSuperviseJGKJInformationFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSuperviseJGKJInformationFlow(oaProcessTemplate);
    }

    @Log(title = "监管用印流程", businessType = BusinessType.UPDATE)
    @GetMapping("/supervise/getInformationUsedFlow")
    public AjaxResult getSuperviseInformationUsedFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSuperviseInformationUsedFlow(oaProcessTemplate);
    }
     @Log(title = "年度计划流程", businessType = BusinessType.UPDATE)
     @GetMapping("/getAnnualPlanFlow")
     public AjaxResult getAnnualPlanFlow(OaProcessTemplate oaProcessTemplate) {
         return informationFlowService.getAnnualPlanFlow(oaProcessTemplate);
     }

     @Log(title = "考核配置新增流程", businessType = BusinessType.UPDATE)
     @GetMapping("/getCheckConfigFlow")
     public AjaxResult getCheckConfigFlow(OaProcessTemplate oaProcessTemplate) {
         return informationFlowService.getCheckConfigFlow(oaProcessTemplate);
     }

     @Log(title = "考核配置修改流程", businessType = BusinessType.UPDATE)
     @GetMapping("/getCheckConfigEditFlow")
     public AjaxResult getCheckConfigEditFlow(OaProcessTemplate oaProcessTemplate) {
         return informationFlowService.getCheckConfigEditFlow(oaProcessTemplate);
     }

     @Log(title = "项目业绩新增流程", businessType = BusinessType.UPDATE)
     @GetMapping("/getAchievementEnterFlow")
     public AjaxResult getAchievementEnterFlow(OaProcessTemplate oaProcessTemplate) {
         return informationFlowService.getAchievementEnterFlow(oaProcessTemplate);
     }

     @Log(title = "项目业绩修改流程", businessType = BusinessType.UPDATE)
     @GetMapping("/getAchievementEnterEditFlow")
     public AjaxResult getAchievementEnterEditFlow(OaProcessTemplate oaProcessTemplate) {
         return informationFlowService.getAchievementEnterEditFlow(oaProcessTemplate);
     }

    @Log(title = "考核结果确认流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getCheckResult")
    public AjaxResult getCheckResult(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getCheckResult(oaProcessTemplate);
    }

    @Log(title = "不良系统机构审核", businessType = BusinessType.UPDATE)
    @GetMapping("/getBlMechanism")
    public AjaxResult getBlMechanism(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getBlMechanism(oaProcessTemplate);
    }

    @Log(title = "不良系统问委外分案", businessType = BusinessType.UPDATE)
    @GetMapping("/getBlOutsourcedProject")
    public AjaxResult getBlOutsourcedProject(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getBlOutsourcedProject(oaProcessTemplate);
    }

    @Log(title = "不良系统渠道业务对账单", businessType = BusinessType.UPDATE)
    @GetMapping("/getBlChannelBusinessReconciliation")
    public AjaxResult getBlChannelBusinessReconciliation(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getBlChannelBusinessReconciliation(oaProcessTemplate);
    }

    @Log(title = "不良系统财务结算单", businessType = BusinessType.UPDATE)
    @GetMapping("/getBlFinancialSettlement")
    public AjaxResult getBlFinancialSettlement(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getBlFinancialSettlement(oaProcessTemplate);
    }

    @Log(title = "奖惩流程(单个)", businessType = BusinessType.UPDATE)
    @GetMapping("/getRewardsPunishment")
    public AjaxResult getRewardsPunishment(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getRewardsPunishment(oaProcessTemplate);
    }

    @Log(title = "奖惩流程(多个)", businessType = BusinessType.UPDATE)
    @GetMapping("/getRewardsPunishmentList")
    public AjaxResult getRewardsPunishmentList(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getRewardsPunishmentList(oaProcessTemplate);
    }

    @Log(title = "出差流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getBusinessTrip")
    public AjaxResult getBusinessTrip(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getBusinessTrip(oaProcessTemplate);
    }

    /**
     * 根据选择的公司查询办公用品领用流程
     * @param oaProcessTemplate
     * @return
     */
    @Log(title = "办公用品领用流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getSupplyFlow")
    public AjaxResult getSupplyFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getSupplyFlow(oaProcessTemplate);
    }

    /**
     * 根据选择的公司查询礼品领用流程
     * @param oaProcessTemplate
     * @return
     */
    @Log(title = "礼品领用流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getGiftFlow")
    public AjaxResult getGiftFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getGiftFlow(oaProcessTemplate);
    }

    /**
     * 根据选择的公司查询办公用品采购流程
     * @param oaProcessTemplate
     * @return
     */
    @Log(title = "办公用品采购流程", businessType = BusinessType.UPDATE)
    @GetMapping("/getPurchaseFlow")
    public AjaxResult getPurchaseFlow(OaProcessTemplate oaProcessTemplate) {
        return informationFlowService.getPurchaseFlow(oaProcessTemplate);
    }
}
