### 基本使用

```javascript
  <MyTable :columns="columns" :source="dataList" :showIndex="true">
    <template #operate="{ record }">
      <span  @click="onEdit(record)">编辑</span>
      <span  @click="onDel(record)">删除</span>
    </template>
  </MyTable>
```

```javascript
columns: Object.freeze([
  { label: "标题", prop: "type", minWidth: "180" },
  { label: "发布人", prop: "createUser", minWidth: "180" },
  { label: "发布时间", prop: "createTime", minWidth: "180" },
  { label: "操作", key: "operate", fixed: "right", align: "left", width: "160" },
]);
```

### 传值

```javascript
//必传
columns:[],//列表项配置项目
//columns中的可配置项(label:标题、props:绑定字段,key:插槽，minWidth:最小宽度、fixed:固定列、align:对齐宽度、width:宽度)

source:[],//数据源(从后端配置)


//非必传
showIndex:false,//是否显示序列
className:'',//表格的个性化样式className
headerRowClassName:'',//表头行的 className
isStripe:''//是否为斑马纹 table
```
