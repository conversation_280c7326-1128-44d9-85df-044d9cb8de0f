<template>
  <a-config-provider :locale="locale">
    <a-form
      v-bind="$attrs"
      selfUpdate
      v-if="
        typeof value.list !== 'undefined' && typeof value.config !== 'undefined'
      "
      class="k-form-build-9136076486841527"
      :layout="value.config.layout"
      :hideRequiredMark="value.config.hideRequiredMark"
      :form="form"
      @submit="handleSubmit"
      :style="value.config.customStyle"
    >
      <buildBlocks
        ref="buildBlocks"
        @handleReset="reset"
        v-for="(record, index) in value.list"
        :record="record"
        :dynamicData="getDynamicData"
        :config="config"
        :disabled="disabled"
        :formConfig="value.config"
        :validatorError="validatorError"
        :key="index"
        @change="handleChange"
      />
    </a-form>
  </a-config-provider>
</template>
<script>
/*
 * author kcz
 * date 2019-11-20
 * description 将json数据构建成表单
 */
import buildBlocks from "./buildBlocks";
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import {lazyLoadTick, convert, formulaCal, moneyToUpCase} from "../../utils/index";
import {getProjectMapColl, getUserDepts, getUserPosts} from "@/api/form/formdesign";
import moment from 'moment';

export default {
  name: "KFormBuild",
  data() {
    return {
      locale: zhCN,
      form: this.$form.createForm(this),
      validatorError: {},
      defaultDynamicData: {}
    };
  },
  // props: ["value", "dynamicData"],
  props: {
    value: {
      type: Object,
      required: true
    },
    dynamicData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    config: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    outputString: {
      type: Boolean,
      default: false
    },
    defaultValue: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    buildBlocks
  },
  computed: {
    getDynamicData() {
      return typeof this.dynamicData === "object" &&
        Object.keys(this.dynamicData).length
        ? this.dynamicData
        : window.$kfb_dynamicData || {};
    }
  },
  methods: {
    // moment,
    handleSubmit(e) {
      // 提交按钮触发，并触发submit函数，返回getData函数
      e.preventDefault();
      this.$emit("submit", this.getData);
    },
    reset() {
      // 重置表单
      this.form.resetFields();
    },
    getData() {
      // 提交函数，提供父级组件调用
      return new Promise((resolve, reject) => {
        try {
          this.form.validateFields((err, values) => {
            if (err) {
              reject(err);
              /**
               * @author: lizhichao<<EMAIL>>
               * @Description: 多容器校验时，提供error返回给多容器进行判断。
               */
              this.validatorError = err;
              return;
            }
            this.validatorError = {};
            if(this.$refs.buildBlocks){
              this.$refs.buildBlocks.forEach(item => {
              if (!item.validationSubform()) {
                reject(err);
              }
            });
            }
           
            if (this.outputString) {
              // 需要所有value转成字符串
              for (const key in values) {
                const type = typeof values[key];
                if (type === "string" || type === "undefined") {
                  continue;
                } else if (type === "object") {
                  values[key] = `k-form-design#${type}#${JSON.stringify(
                    values[key]
                  )}`;
                } else {
                  values[key] = `k-form-design#${type}#${String(values[key])}`;
                }
              }

              resolve(values);
            } else {
              resolve(values);
            }
          });
        } catch (err) {
          reject(err);
        }
      });
    },
    setData(json) {
      return new Promise((resolve, reject) => {
        lazyLoadTick.nextTick(() => {
          try {
            if (this.outputString) {
              // 将非string数据还原
              for (const key in json) {
                if (!json[key].startsWith("k-form-design#")) {
                  continue;
                }
                const array = json[key].split("#");
                if (array[1] === "object") {
                  json[key] = JSON.parse(array[2]);
                } else if (array[1] === "number") {
                  json[key] = Number(array[2]);
                } else if (array[1] === "boolean") {
                  json[key] = Boolean(array[2]);
                }
              }
              this.form.setFieldsValue(json);
            } else {
              this.handleSpecialData(json)
              this.form.setFieldsValue(json);
            }
            resolve(true);
          } catch (err) {
            console.error(err);
            reject(err);
          }
        });
      });
    },

    // 批量设置某个option的值
    setOptions(fields, optionName, value) {
      fields = new Set(fields);

      // 递归遍历控件树
      const traverse = array => {
        array.forEach(element => {
          if (fields.has(element.model)) {
            this.$set(element.options, optionName, value);
          }
          if (element.type === "grid" || element.type === "tabs") {
            // 栅格布局 and 标签页
            element.columns.forEach(item => {
              traverse(item.list);
            });
          } else if (element.type === "card" || element.type === "batch") {
            // 卡片布局 and  动态表格
            traverse(element.list);
          } else if (element.type === "table") {
            // 表格布局
            element.trs.forEach(item => {
              item.tds.forEach(val => {
                traverse(val.list);
              });
            });
          }
        });
      };
      traverse(this.value.list);
    },
    // 隐藏表单字段
    hide(fields) {
      this.setOptions(fields, "hidden", true);
    },
    // 显示表单字段
    show(fields) {
      this.setOptions(fields, "hidden", false);
    },
    // 禁用表单字段
    disable(fields) {
      this.setOptions(fields, "disabled", true);
    },
    // 启用表单字段
    enable(fields) {
      this.setOptions(fields, "disabled", false);
    },
    handleChange(value, key) {
      this.value.list.forEach(e => {
        //布局组件
        this.handleLayoutComponent(value, key, e)
        //自定义组件
        this.handleSelfComponent(value, key, e)
      });
      // 触发change事件
      this.$emit("change", value, key);
    },
    //处理布局组件
    handleLayoutComponent(value, key, record){
      //卡片布局
      if(record.type=='card'){
        record.list.forEach((item, index)=>{
          this.handleSelfComponent(value, key, item)
          this.handleLayoutComponent(value, key, item)
        })
      }
      //标签页布局或栅格布局
      if(record.type=='tabs' || record.type=='grid'){
        record.columns.forEach((colItem, index)=>{
          colItem.list.forEach((item, index)=>{
            this.handleSelfComponent(value, key, item)
            this.handleLayoutComponent(value, key, item)
          })
        })
      }
      //表格布局
      if(record.type=='table'){
        record.trs.forEach((trItem, index)=>{
          trItem.tds.filter(
            item => item.colspan && item.rowspan
          ).forEach((tdItem, index)=>{
            tdItem.list.forEach((item, index)=>{
              this.handleSelfComponent(value, key, item)
              this.handleLayoutComponent(value, key, item)
            })
          })
        })
      }
    },
    //处理自定义组件
    handleSelfComponent(value, key, item){
      //时间差
      if(item.type=='diffTime' && key==item.options.associatedElement){
        var timeRange = this.form.getFieldValue(item.options.associatedElement)
        var hours = moment(timeRange[1]).diff(moment(timeRange[0]), 'hours');//小时
        var minutes = moment(timeRange[1]).diff(moment(timeRange[0]), 'minutes');//分钟
        if(hours > 0){
          minutes = minutes - 60*hours;
          var str = hours+"小时"+minutes+"分钟";
          this.form.setFieldsValue({[item.key]:str});
        }else{
          var str = minutes+"分钟";
          this.form.setFieldsValue({[item.key]:str});
        }
      }
      //金额转换
      if(item.type=='amtConvert' && key==item.options.associatedElement){
        this.$nextTick(() => {
          this.form.setFieldsValue({[item.key]:moneyToUpCase(value)});
        })
      }
      if(item.type=='amtConvert' && item.options.associatedElement.indexOf("amtFormula")==0){
        this.$nextTick(() => {
          this.form.setFieldsValue({[item.key]:moneyToUpCase(this.form.getFieldValue(item.options.associatedElement))});
        })
      }
      //收款人
      if(item.type=='payee' && key==item.options.associatedElement){
        this.$nextTick(() => {
          item.options.options = [];
          this.form.setFieldsValue({[item.key]:''});
        })
        getProjectMapColl({projectId: value, type: item.options.componentType}).then(response => {
          item.options.options = response[item.options.selectType].map((obj) => {
            return {
              value: obj,
              label: obj,
            };
          })
        });
      }
      //公式组件
      if(item.type=='amtFormula'){
        this.$nextTick(() => {
          if(item.options.calculateType=='1'){
            var itemsMap = this.formulaItemMap();
            var formula = item.options.formula;
            var labelArr = formula.match(/\$([^$]*)\$/g);
            labelArr.forEach(e => {
              formula = formula.replace(e, itemsMap[e][1]?itemsMap[e][1]:0)
            })
            if(item.options.selectType=='1'){
              this.form.setFieldsValue({[item.key]:eval(formula)});
            }else{
              this.form.setFieldsValue({[item.key]:eval(formula)});
            }
          }else if(item.options.calculateType=='2'){
            if(key.indexOf("batch") == 0){
              var sum = 0;
              value.forEach((e, index)=>{
                if(e.hasOwnProperty(item.options.associatedElement)){
                  sum+=Number(e[item.options.associatedElement]);
                }
              })
              if(!isNaN(sum) && typeof sum === 'number'){
                this.form.setFieldsValue({[item.key]:sum});
              }
            }
          }
        })
      }
    },
    formulaItemMap(){
      var itemsMap = {}
      this.value.list.forEach(record => {
        this.pushItemsMap(itemsMap, record);
        this.pushLayoutMap(itemsMap, record)
      })
      return itemsMap;
    },
    pushLayoutMap(itemsMap, record){
      //卡片布局
      if(record.type=='card'){
        record.list.forEach((item, index)=>{
          this.pushItemsMap(itemsMap, item);
          this.pushLayoutMap(itemsMap, item);
        })
      }
      //标签页布局或栅格布局
      if(record.type=='tabs' || record.type=='grid'){
        record.columns.forEach((colItem, index)=>{
          colItem.list.forEach((item, index)=>{
            this.pushItemsMap(itemsMap, item);
            this.pushLayoutMap(itemsMap, item);
          })
        })
      }
      //表格布局
      if(record.type=='table'){
        record.trs.forEach((trItem, index)=>{
          trItem.tds.filter(
            item => item.colspan && item.rowspan
          ).forEach((tdItem, index)=>{
            tdItem.list.forEach((item, index)=>{
              this.pushItemsMap(itemsMap, item);
              this.pushLayoutMap(itemsMap, item);
            })
          })
        })
      }
    },
    pushItemsMap(itemsMap, item){
      var noInclude = ['card','tabs','grid','table'].includes(item.type)
      if(!noInclude){
        itemsMap['$'+item.label+'$'] =  [item.key,this.form.getFieldValue(item.key)]
      }
    },
    handleSpecialData(json){
      this.value.list.forEach(e => {
        //布局组件
        this.handleLayoutValue(json, e)
        //自定义组件
        this.handleValue(json, e)
      });
    },
    //处理布局组件
    handleLayoutValue(json, record){
      //卡片布局
      if(record.type=='card'){
        record.list.forEach((item, index)=>{
          this.handleValue(json, item)
          this.handleLayoutValue(json, item)
        })
      }
      //标签页布局或栅格布局
      if(record.type=='tabs' || record.type=='grid'){
        record.columns.forEach((colItem, index)=>{
          colItem.list.forEach((item, index)=>{
            this.handleValue(json, item)
            this.handleLayoutValue(json, item)
          })
        })
      }
      //表格布局
      if(record.type=='table'){
        record.trs.forEach((trItem, index)=>{
          trItem.tds.filter(
            item => item.colspan && item.rowspan
          ).forEach((tdItem, index)=>{
            tdItem.list.forEach((item, index)=>{
              this.handleValue(json, item)
              this.handleLayoutValue(json, item)
            })
          })
        })
      }
    },
    //处理自定义组件
    handleValue(json, item){
      if(item.type=='addressNote' && json[item.key] != undefined){
        //部门信息处理
        if(item.options.selectType == '2' && item.options.selectRange == 2){
          getUserDepts({userId:json.userId}).then(response => {
            item.options.options = response.depts.map((obj) => {
              return {
                value: obj.deptId,
                label: obj.deptName,
              };
            });
          });
        }
        //岗位信息处理
        if(item.options.selectType == '3' && item.options.selectRange == 2){
          getUserPosts({userId:json.userId}).then(response => {
            item.options.options = response.posts.map((obj) => {
              return {
                value: obj.postId,
                label: obj.postName,
              };
            });
          });

        }
      }
      if(item.type=='payee' && json[item.key] != undefined){
        getProjectMapColl({projectId: json[item.options.associatedElement], type: item.options.componentType}).then(response => {
          item.options.options = response[item.options.selectType].map((obj) => {
            return {
              value: obj,
              label: obj,
            };
          })
        });
      }
    },
  },
  mounted() {
    this.setData(this.defaultValue);

    // this.$nextTick(() => {
    //   this.setData(this.defaultValue);
    // });
  },
  created() {
    lazyLoadTick.reset();
  }
};
</script>
