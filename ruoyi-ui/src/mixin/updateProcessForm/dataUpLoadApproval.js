export default {
  data() {
    return {
      dataListMultipleSelection: [],
    };
  },
  methods: {
    handleSelectionChangeDataList(val) {
      this.dataListMultipleSelection = val;
    },
    async batchDownload() {
      const name=this.$route.query.themeTypeXz?"资料下载审核.zip":"资料用印审核.zip";
      if (!this.dataListMultipleSelection.length) {
        this.$modal.msgError("请勾选列表数据进行操作");
        return;
      }
      const ids=this.dataListMultipleSelection.map(item=>item.id);
      this.download(
        "system/information/downloadFileZip",
        {idArray:ids},
        name
      );
    },
  },
};
