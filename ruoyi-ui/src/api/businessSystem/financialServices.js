import request from "@/utils/request";
export function queryCompanyInfo(params) {
    return request({
        url: "/businessControl/queryCompanyInfo",
        method: "get",
        params,
    });
}
export function queryAccountConfigInfo(params) {
    return request({
        url: "/businessControl/queryAccountConfigInfo",
        method: "get",
        params,
    });
}
export function queryGuaranteeCompanyDetail(params) {
    return request({
        url: "/businessControl/queryGuaranteeCompanyDetail",
        method: "get",
        params,
    });
}
export function queryNewProject(params) {
    return request({
        url: "/businessControl/queryNewProject",
        method: "get",
        params,
    });
}
export function queryProjectDetails(params) {
    return request({
        url: "/businessControl/queryProjectDetails",
        method: "get",
        params,
    });
}
export function queryProjectDetailsById(params) {
    return request({
        url: "/businessControl/queryProjectDetailsById",
        method: "get",
        params,
    });
}
export function queryRuleDetailOverLimitInfo(params) {
    return request({
        url: "/businessControl/queryRuleDetailOverLimitInfo",
        method: "get",
        params,
    });
}
export function queryBusinessOperateLog(params) {
    return request({
        url: "/businessControl/queryBusinessOperateLog",
        method: "get",
        params,
    });
}
export function getOldProjectInfo(params) {
    return request({
        url: "/businessControl/getOldProjectInfo",
        method: "get",
        params,
    });
}
export function getOldProjectLoanInfo(params) {
    return request({
        url: "/businessControl/getOldProjectLoanInfo",
        method: "get",
        params,
    });
}
export function queryControlProjectRuleByIdList(params) {
    return request({
        url: "/businessControl/queryControlProjectRuleByIdList",
        method: "get",
        params,
    });
}
export function queryControlProjectRuleById(params) {
    return request({
        url: "/businessControl/queryControlProjectRuleById",
        method: "get",
        params,
    });
}
export function getTemplateInfo(params) {
    return request({
        url: "/businessControl/getTemplateInfo",
        method: "get",
        params,
    });
}
export function queryLoanAmountById(params) {
    return request({
        url: "/businessControl/queryLoanAmountById",
        method: "get",
        params,
    });
}
export function deleteControlProjectRuleById(params) {
    return request({
        url: "/businessControl/deleteControlProjectRuleById",
        method: "get",
        params,
    });
}
export function queryControlRuleProcessRecordInfo(params) {
    return request({
        url: "/businessControl/queryControlRuleProcessRecordInfo",
        method: "get",
        params,
    });
}
export function updateControlRuleProcessRecord(params) {
    return request({
        url: "/businessControl/updateControlRuleProcessRecord",
        method: "get",
        params,
    });
}
export function updateControlRuleStatusById(params) {
    return request({
        url: "/businessControl/updateControlRuleStatusById",
        method: "get",
        params,
    });
}
export function updateGuaranteeCompanyDetail(data) {
    return request({
        url: "/businessControl/updateGuaranteeCompanyDetail",
        method: "post",
        data,
    });
}
export function batchUpdateWarningRuleInfo(data) {
    return request({
        url: "/businessControl/batchUpdateWarningRuleInfo",
        method: "post",
        data,
    });
}
export function addProjectDetails(data) {
    return request({
        url: "/businessControl/addProjectDetails",
        method: "post",
        data,
    });
}
export function controlStartProd(data) {
    return request({
        url: "/businessControl/startProd",
        method: "post",
        data,
    });
}
export function updateControlProjectRule(data) {
    return request({
        url: "/businessControl/updateControlProjectRule",
        method: "post",
        data,
    });
}
export function updateProjectDetails(data) {
    return request({
        url: "/businessControl/updateProjectDetails",
        method: "post",
        data,
    });
}
export function queryControlProjectSummary(data) {
    return request({
        url: "/businessControl/queryControlProjectSummary",
        method: "post",
        data,
    });
}
export function queryControlProjectRule(data) {
    return request({
        url: "/businessControl/queryControlProjectRule",
        method: "post",
        data,
    });
}
export function addControlProjectRule(data) {
    return request({
        url: "/businessControl/addControlProjectRule",
        method: "post",
        data,
    });
}

export function updateNotifyInfo(params) {
  return request({
    url: "/businessControl/updateNotifyInfo",
    method: "get",
    params,
  });
}

export function rollBackControlRule(params) {
  return request({
    url: "/businessControl/rollBackControlRule",
    method: "get",
    params,
  });
}

export function deleteProjectDetailById(params) {
  return request({
    url: "/businessControl/deleteProjectDetail",
    method: "get",
    params,
  });
}

export function queryBusinessMarginEditRecord(params) {
  return request({
    url: "/businessControl/queryBusinessMarginEditRecord",
    method: "get",
    params,
  });
}
