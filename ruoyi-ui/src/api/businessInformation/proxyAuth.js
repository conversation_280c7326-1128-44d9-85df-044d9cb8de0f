import request from "@/utils/request";

export function getAgentList(params) {
    return request({
        url: "/system/newAuthority/getAgentList",
        method: "get",
        params
    });
}
export function subordinateForAgencyAuth(params) {
    return request({
        url: "/personnel/archives/subordinateForAgencyAuth",
        method: "get",
        params
    });
}
export function getAuthCode(params) {
    return request({
        url: "/system/newAuthority/getAuthCode",
        method: "get",
        params
    });
}
export function getAgencyAuthRecord(params) {
    return request({
        url: "/system/newAuthority/getAgencyAuthRecord",
        method: "get",
        params
    });
}
export function haveAgencyQuery(params) {
    return request({
        url: "/system/newAuthority/haveAgencyQuery",
        method: "get",
        params
    });
}
export function agent<PERSON>andle(data) {
    return request({
        url: "/system/newAuthority/agentHandle",
        method: "post",
        data
    });
}
export function cancelAgent(data) {
    return request({
        url: "/system/newAuthority/cancelAgent",
        method: "post",
        data
    });
}