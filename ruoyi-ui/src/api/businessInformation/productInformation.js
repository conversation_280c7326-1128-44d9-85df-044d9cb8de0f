import request from "@/utils/request";

export function projectParameter(params) {
  return request({
    url: "/project/parameter/list",
    method: "get",
    params,
  });
}
export function addProjectParameter(data) {
  return request({
    url: "/project/parameter",
    method: "post",
    data,
  });
}
export function updateProjectParameter(data) {
  return request({
    url: "/project/parameter",
    method: "put",
    data,
  });
}
export function deleteProjectParameter(configId) {
  return request({
    url: "/project/parameter/" + configId,
    method: "delete",
  });
}
export function splicingListByCode(params) {
  return request({
    url: "/systemData/manage/splicingListByCode",
    method: "get",
    params,
  });
}