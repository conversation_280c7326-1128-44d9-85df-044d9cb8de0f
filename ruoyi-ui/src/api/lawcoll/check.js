import request from '@/utils/request'
// 查询法催对账历史列表
export function listCheck(query) {
  return request({
    url: '/lawcoll/check/list',
    method: 'get',
    params: query
  })
}

// 查询法催对账历史详细
export function getCheckEm(importIdentify) {
  return request({
    url: '/lawcoll/check/em/' + importIdentify,
    method: 'get'
  })
}

// 查询法催对账历史详细
export function getCheckLp(importIdentify) {
  return request({
    url: '/lawcoll/check/lp/' + importIdentify,
    method: 'get'
  })
}

// 查询法催对账历史详细
export function getCheckLpAh(importIdentify) {
  return request({
    url: '/lawcoll/check/lpAh/' + importIdentify,
    method: 'get'
  })
}

// 新增法催对账历史
export function addCheck(data) {
  return request({
    url: '/lawcoll/check',
    method: 'post',
    data: data
  })
}

// 修改法催对账历史
export function updateCheck(data) {
  return request({
    url: '/lawcoll/check',
    method: 'put',
    data: data
  })
}

// 删除法催对账历史
export function delCheck(importIdentify) {
  return request({
    url: '/lawcoll/check/' + importIdentify,
    method: 'delete'
  })
}
//保存比对结果
export function saveResult(data) {
  return request({
    url: '/lawcoll/check/saveResult',
    method: 'post',
    data: data
  })
}
//上传非安徽文件
export function uploadFile(data) {
  return request({
    url: '/lawcoll/check/importLp',
    method: 'post',
    data: data,
  })
}
//上传安徽文件
export function uploadAhFile(data) {
  return request({
    url: '/lawcoll/check/importAh',
    method: 'post',
    data: data,
  })
}
