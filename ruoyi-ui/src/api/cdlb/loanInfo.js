import request from '@/utils/request'

// 查询车贷借据信息列表
export function listloaninfo(query) {
  return request({
    url: '/cdlb/loaninfo/list',
    method: 'get',
    params: query
  })
}

// 查询车贷借据信息详细
export function getloaninfo(id) {
  return request({
    url: '/cdlb/loaninfo/' + id,
    method: 'get'
  })
}

// 新增车贷借据信息
export function addloaninfo(data) {
  return request({
    url: '/cdlb/loaninfo',
    method: 'post',
    data: data
  })
}

// 修改车贷借据信息
export function updateloaninfo(data) {
  return request({
    url: '/cdlb/loaninfo',
    method: 'put',
    data: data
  })
}

// 删除车贷借据信息
export function delloaninfo(id) {
  return request({
    url: '/cdlb/loaninfo/' + id,
    method: 'delete'
  })
}
