import request from '@/utils/request'

// 查询字典数据映射列表
export function listMapping(query) {
  return request({
    url: '/system/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询字典数据映射列表
export function listnopageMapping(query) {
  return request({
    url: '/system/mapping/noPagelist',
    method: 'get',
    params: query
  })
}




// 新增字典数据映射
export function addMapping(data) {
  return request({
    url: '/system/mapping',
    method: 'post',
    data: data
  })
}

// 修改字典数据映射
export function updateMapping(data) {
  return request({
    url: '/system/mapping',
    method: 'put',
    data: data
  })
}

// 删除字典数据映射
export function delMapping(dictddmId) {
  return request({
    url: '/system/mapping/' + dictddmId,
    method: 'delete'
  })
}

// 刷新字典缓存
export function refreshCache() {
  return request({
    url: '/system/mapping/refreshCache',
    method: 'delete'
  })
}

// 查询字典数据映射详细
export function getMapping(dictddmId) {
  return request({
    url: '/system/mapping/' + dictddmId,
    method: 'get'
  })
}
//获取映射数据
export function getParamMapping(selectParam){
  return request({
    url: '/system/mapping/select/'+selectParam,
    method: 'get',
  })
}
