import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listParams(query) {
  return request({
    url: '/system/params/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getParams(id) {
  return request({
    url: '/system/params/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addParams(data) {
  return request({
    url: '/system/params',
    method: 'post',
    data: data
  })
}

// 校验新增的数据是否存在【请填写功能名称】
export function checkParams(data) {
  return request({
    url: '/system/params/checkParams',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateParams(data) {
  return request({
    url: '/system/params',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delParams(id) {
  return request({
    url: '/system/params/' + id,
    method: 'delete'
  })
}
