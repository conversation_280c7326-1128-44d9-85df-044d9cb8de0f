import request from '@/utils/request'

// 查询财务项目管理-打款信息列表
export function listPay(query) {
  return request({
    url: '/cwxmgl/pay/list',
    method: 'get',
    params: query
  })
}

// 查询财务项目管理-打款信息详细
export function getPay(id) {
  return request({
    url: '/cwxmgl/pay/' + id,
    method: 'get'
  })
}

// 新增财务项目管理-打款信息
export function addPay(data) {
  return request({
    url: '/cwxmgl/pay',
    method: 'post',
    data: data
  })
}

// 修改财务项目管理-打款信息
export function updatePay(data) {
  return request({
    url: '/cwxmgl/pay',
    method: 'put',
    data: data
  })
}

// 删除财务项目管理-打款信息
export function delPay(id) {
  return request({
    url: '/cwxmgl/pay/' + id,
    method: 'delete'
  })
}

// 查询财务项目管理-打款信息列表-待出纳打款
export function listFlagZero(query) {
  return request({
    url: '/cwxmgl/pay/list/flagzero',
    method: 'get',
    params: query
  })
}
