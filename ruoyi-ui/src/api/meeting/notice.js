import request from "@/utils/request";

export function selectNotification(params) {
  return request({
    url: "/meeting/selectNotification",
    method: "get",
    params,
  });
}
export function notifyList(params) {
  return request({
    url: "/meeting/notify/list",
    method: "get",
    params,
  });
}
export function meetingNotify(data) {
  return request({
    url: "/meeting/notify",
    method: "put",
    data,
  });
}
