import request from '@/utils/request'
export function caiwuList(query) {
    return request({
        url: '/oasystem/caiwuDataquery/caiwuList',
        method: 'get',
        params: query
    })
}
export function getTemplateList(query) {
    return request({
        url: '/oaSystem/templateType/getTemplateList',
        method: 'get',
        params: query
    })
}
export function yspcxList(query) {
    return request({
        url: '/yspProcess/template/yspcxList',
        method: 'get',
        params: query
    })
}
export function csList(query) {
    return request({
        url: '/yspUrgent/record/csList',
        method: 'get',
        params: query
    })
}
export function voucharList(query) {
    return request({
        url: '/oasystem/caiwuDataquery/voucharList',
        method: 'get',
        params: query
    })
}
export function addYspUrgent(data) {
    return request({
        url: '/yspUrgent/record/addYspUrgent',
        method: 'post',
        data
    })
}
export function addOrUpdateTemplateType(data) {
    return request({
        url: '/oaSystem/templateType/addOrUpdateTemplateType',
        method: 'post',
        data
    })
}
//获取账套
export function getAllAccountSetsList() {
    return request({
      url: '/account-sets/getAllAccountSetsList',
      method: 'get'
    })
  }