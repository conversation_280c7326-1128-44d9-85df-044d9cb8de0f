import Layout from '@/layout'

export default [
  {
    path: '/lawcoll/check',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:list'],
    children: [
      {
        path: 'entrustedMediation',
        component: () => import('@/views/lawcoll/check/entrustedMediation/index'),
        name: 'entrustedMediation',
        meta: { title: '委托调解业务', activeMenu: '/lawcoll/check/entrustedMediation/index' }
      }
    ]
  },
  {
    path: '/lawcoll/entrustedMediation',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:list'],
    children: [
      {
        path: 'openUpload',
        component: () => import('@/views/lawcoll/check/entrustedMediation/upload'),
        name: 'entrustedMediationUpload',
        meta: { title: '委托调解业务', activeMenu: '/lawcoll/check/entrustedMediation/upload' }
      }
    ]
  },
  {
    path: '/lawcoll/entrustedMediation',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:query'],
    children: [
      {
        path: 'showDetail',
        component: () => import('@/views/lawcoll/check/entrustedMediation/detail'),
        name: 'entrustedMediationDetail',
        meta: { title: '委托调解-详情', activeMenu: '/lawcoll/check/entrustedMediation/detail' }
      }
    ]
  },
  {
    path: '/lawcoll/check',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:list'],
    children: [
      {
        path: 'legalProceedings',
        component: () => import('@/views/lawcoll/check/legalProceedings/index'),
        name: 'legalProceedings',
        meta: { title: '法律诉讼业务', activeMenu: '/lawcoll/check/legalProceedings/index' }
      }
    ]
  },
  {
    path: '/lawcoll/legalProceedings',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:list'],
    children: [
      {
        path: 'openUpload',
        component: () => import('@/views/lawcoll/check/legalProceedings/upload'),
        name: 'legalProceedingsUpload',
        meta: { title: '法律诉讼业务-非安徽', activeMenu: '/lawcoll/check/legalProceedings/upload' }
      }
    ]
  },
  {
    path: '/lawcoll/legalProceedings',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:query'],
    children: [
      {
        path: 'showDetail',
        component: () => import('@/views/lawcoll/check/legalProceedings/detail'),
        name: 'legalProceedingsDetail',
        meta: { title: '法律诉讼业务-非安徽', activeMenu: '/lawcoll/check/legalProceedings/detail' }
      }
    ]
  },
  {
    path: '/lawcoll/legalProceedingsAh',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:list'],
    children: [
      {
        path: 'openUpload',
        component: () => import('@/views/lawcoll/check/legalProceedingsAh/upload'),
        name: 'legalProceedingsAhUpload',
        meta: { title: '法律诉讼业务-安徽', activeMenu: '/lawcoll/check/legalProceedingsAh/upload' }
      }
    ]
  },
  {
    path: '/lawcoll/legalProceedingsAh',
    component: Layout,
    hidden: true,
    permissions: ['lawcoll:check:query'],
    children: [
      {
        path: 'showDetail',
        component: () => import('@/views/lawcoll/check/legalProceedingsAh/detail'),
        name: 'legalProceedingsAhDetail',
        meta: { title: '法律诉讼业务-安徽', activeMenu: '/lawcoll/check/legalProceedingsAh/detail' }
      }
    ]
  },
]

