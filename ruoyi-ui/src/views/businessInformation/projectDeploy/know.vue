<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="580px"
      :before-close="handleClose"
    >
      <p>
        项目名称的{{
          detailData.applyType == 0
            ? "新增"
            : detailData.applyType == 1
            ? "修改"
            : "删除"
        }}申请已由以下人员审核{{ detailData.rejectFlag == 0 ? "通过" : "驳回" }}
      </p>
      <p>
        <span style="font-weight: bold">{{
          detailData.checkIdentity == 0
            ? "财务负责人"
            : detailData.checkIdentity == 1
            ? "业务负责人"
            : detailData.checkIdentity == 2
            ? "财务管理员"
            : "业务管理员"
        }}</span
        >：{{ detailData.checkUserNickName }}
      </p>
      <p v-if="detailData.applyType == 1 && detailData.rejectFlag == 0">
        结果已生效
      </p>
      <p v-if="detailData.rejectFlag == 1">
        驳回原因：{{ detailData.checkRejectInfo }}
      </p>
      <p>点击已知悉，关闭本条通知</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirm">已知悉</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    detailData: Object,
  },
  data() {
    return {
      title: "",
      dialogVisible: true,
    };
  },
  mounted() {
    this.title = this.detailData.rejectFlag == 0 ? "审核已通过" : "审核已驳回";
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm");
    },
  },
};
</script>

<style lang="less" scoped>
</style>