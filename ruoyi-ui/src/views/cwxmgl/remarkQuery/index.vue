<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" >
      <el-form-item label="备注关键字" prop="remark">
<!--          <el-select v-model="queryParams.custName" filterable :data="feeCustList" placeholder="请选择" clearable :style="{width: '100%'}">-->
<!--            <template slot-scope="scope">-->
<!--            <el-option v-for="(item, index) in feeCustList" :label="item" :key="index"-->
<!--                       :value="item" :disabled="item.disabled"></el-option>-->
<!--            </template>-->
<!--          </el-select>-->
        <el-input v-model="queryParams.remark" placeholder="请输入备注关键字" maxlength="10"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" style="height: 32px">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery" style="height: 32px">重置</el-button>
      </el-form-item>
    </el-form>

    <div v-show="unbalanceon" style="width: 100%;height:100px;line-height: 300px;" align="center">
      <span  class="spancol2">请输入关键字后进行查询</span>
    </div>

    <div v-show="notfound" style="width: 100%;height:100px;line-height: 300px;" align="center">
      <span  class="spancol2">没有查询到相关的数据</span>
    </div>

<!--  <div v-show="balanceon">-->
<!--    <div style="width: 100%;height:6px;background-color:#FBFBFB;"></div>-->
<!--    <div style="display: flex;width: 100%;background-color:#FBFBFB;flex-wrap: wrap;flex-direction: row;">-->
<!--&lt;!&ndash;      <div class="inneraaa">&ndash;&gt;-->
<!--&lt;!&ndash;        <div class="divsecend" style="line-height: 40px;padding-top:12px;"> <span  class="spancol2">信息费总计</span>&ndash;&gt;-->
<!--&lt;!&ndash;          <span style="margin-left: 24px; color:#333333;font-size:24px;display: inline-block;">{{formaterMoney(this.sum_fee_amt)}}</span>&ndash;&gt;-->
<!--&lt;!&ndash;        </div>&ndash;&gt;-->
<!--&lt;!&ndash;      </div>&ndash;&gt;-->
<!--      <div class="inneraaaA" style="margin-bottom: 30px">-->
<!--        <div class="divsecend" style="line-height: 40px;padding-top:12px;">-->
<!--              <span class="spancol2">信息费未结清合计</span>-->
<!--          <span style="margin-left: 24px; color:#333333;font-size:24px;display: inline-block;">{{formaterMoney(this.sum_fee_no_already)}}</span>-->
<!--        </div>-->
<!--      </div>-->


<!--&lt;!&ndash;      <div class="innerone" >&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; <div style="width: 40%;height:90px;float:left " > &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <div style="width: 100%;height:15px;"></div>&ndash;&gt;-->
<!--&lt;!&ndash;        <div style="width: 100%;height:35px;height:35px;line-height:35px;">&ndash;&gt;-->
<!--&lt;!&ndash;          <span class="grid-content">已结清：</span><span style="color: #333333;font-size: 15px"> {{formaterMoney(this.sum_fee_already)}}</span>&ndash;&gt;-->
<!--&lt;!&ndash;        </div>&ndash;&gt;-->
<!--&lt;!&ndash;        <div style="width: 100%;height:35px;height:35px;line-height:35px;">&ndash;&gt;-->
<!--&lt;!&ndash;          <span class="grid-content">未结清：</span><span style="color: #333333;font-size: 15px"> {{formaterMoney(this.sum_fee_no_already)}}</span>&ndash;&gt;-->
<!--&lt;!&ndash;        </div>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; </div> &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;      </div>&ndash;&gt;-->

<!--&lt;!&ndash;      <div class="innerone" >&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; <div style="width: 40%;height:90px;float:left " > &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <div style="width: 100%;height:15px;"></div>&ndash;&gt;-->
<!--&lt;!&ndash;        <div style="width: 100%;height:35px;height:35px;line-height:35px;">&ndash;&gt;-->
<!--&lt;!&ndash;          <span class="grid-content">实际打款:</span><span style="color: #333333;font-size: 15px"> {{formaterMoney(this.sum_pay_amt)}}</span>&ndash;&gt;-->
<!--&lt;!&ndash;        </div>&ndash;&gt;-->
<!--&lt;!&ndash;        <div style="width: 100%;height:35px;height:35px;line-height:35px;">&ndash;&gt;-->
<!--&lt;!&ndash;          <span class="grid-content">抹平差额:</span><span style="color: #333333;font-size: 15px"> {{formaterMoney(this.sum_difference_amt)}}</span>&ndash;&gt;-->
<!--&lt;!&ndash;        </div>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; </div> &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;      </div>&ndash;&gt;-->

<!--    </div>-->
<!--    </div>-->

<!--    <div v-show="balanceon" style="margin-top: 15px;margin-bottom: 20px">-->
<!--      <el-button size="mini" type="warning" @click = "handleExport()" plain icon="el-icon-download">导出</el-button>-->
<!--    </div>-->
<!--    <div v-for="detail in feeCustListDetail" :key="detail.projectId">-->
    <div v-if="projectTypeFlag === '0' || projectTypeFlag === '2'|| projectTypeFlag === '3' || projectTypeFlag === 'all'">
      <span v-if="projectTypeFlag === '0'">通道业务：</span>
      <span v-if="projectTypeFlag === '2'">分润业务：</span>
      <span v-if="projectTypeFlag === '3'">保函业务：</span>
      <span v-if="projectTypeFlag === 'all'">通道业务、分润业务、保函业务：</span>
    </div>
    <div v-show="projectTypeFlag === '0' || projectTypeFlag === '2'|| projectTypeFlag === '3' || projectTypeFlag === 'all'">
    <el-table :data="feeCustListDetail"
                :span-method="overDetailBodyDetailList_v2panMethod"
                :header-cell-style="{fontSize:'14px'}"
                border
      >
<!--        <el-table :data="detail.default" v-show="balanceon"-->
<!--                  :span-method="overDetailBodyDetailList_v2panMethod"-->
<!--                  :show-header="detail.showHeader"-->
<!--                  :header-cell-style="{fontSize:'14px'}"-->
<!--                  border-->
<!--        >-->
            <el-table-column label="项目名称"  prop="projectName" min-width="20%" >
              <template slot-scope="scope">
                <a
                    @click="handleProjectDetail(scope.row.projectId, scope.row.projectIncomeId, scope.row.phaseId, scope.row.projectType)">
                  <span style="color:#1E90FF">{{scope.row.projectName}}</span>
                </a>
<!--                <span v-if="scope.row.controlFlag === '1'">{{scope.row.project_name}}</span>-->
              </template>
            </el-table-column>
            <el-table-column label="业务期次"  prop="feeNoAlreadyTerm" min-width="20%"  />
        <el-table-column label="收入"  prop="incomeAmt" min-width="20%"  >
          <template slot-scope="scope">
            <span>{{formaterMoney(scope.row.incomeAmt)}}</span>
          </template>
        </el-table-column>
      <el-table-column label="收款时间"  prop="collectionTime" min-width="20%">
        <template slot-scope="scope">
          {{ $format(scope.row.collectionTime,'yyyy年MM月') }}
        </template>
      </el-table-column>
        <el-table-column label="出信息费公司"  prop="custName" min-width="20%"  />
        <el-table-column label="信息费公司"  prop="feeCustName" min-width="20%"  />
        <el-table-column label="实付信息费"  prop="actuallyPayFeeAmt" min-width="10%"  >
          <template slot-scope="scope">
            <span>{{formaterMoney(scope.row.actuallyPayFeeAmt)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="毛利"  prop="grossProfitAmt" min-width="20%"  >
          <template slot-scope="scope">
            <span>{{formaterMoney(scope.row.grossProfitAmt)}}</span>
          </template>
        </el-table-column>

<!--            <el-table-column label="提成信息费"  prop="fee_amt2" min-width="10%"  >-->
<!--              <template slot-scope="scope">-->
<!--                <span>{{formaterMoney(scope.row.fee_amt2)}}</span>-->
<!--              </template>-->
<!--            </el-table-column>-->
        
        <el-table-column label="备注"  prop="remark" min-width="20%"  >
<!--          <template slot-scope="scope">-->
<!--            <span v-if="scope.row.remark !== null && scope.row.remark.length>15">-->
<!--                      {{scope.row.remark.substring(0,10)}}<el-button-->
<!--              type="text"-->
<!--              size="mini"-->
<!--              @click="textDetail(scope.row.remark)"-->
<!--            >更多-->
<!--                  </el-button>-->
<!--                    </span>-->
<!--            <span v-if="scope.row.remark !== null && scope.row.remark.length<=15">-->
<!--                      {{scope.row.remark}}-->
<!--                    </span>-->
<!--          </template>-->
          <template slot-scope="scope">
                  <span v-if="scope.row.remark !== null && scope.row.remark.length>15">
                    {{scope.row.remark.substring(0,10)}}<el-button
                    type="text"
                    size="mini"
                    @click="textDetail(scope.row.remark, scope.row.projectIncomeId)"
                  >更多
                </el-button>
                  </span>
            <span v-if="scope.row.remark !== null && scope.row.remark.length<=15">
                    <span v-if="scope.row.remark !== ''">
                      {{scope.row.remark}}<el-button
                      type="text"
                      size="mini"
                      @click="textDetail(scope.row.remark, scope.row.projectIncomeId)"
                    >更多</el-button>
                      </span>
                  </span>
            <span v-if="scope.row.remark === null || scope.row.remark === ''">
                    <span v-if="(scope.row.incomeRejectionReason !== null && scope.row.incomeRejectionReason !== '') || (scope.row.feeRejectionReason !== null && scope.row.feeRejectionReason !== '')">
                      <el-button
                        type="text"
                        size="mini"
                        @click="textDetail(null, scope.row.projectIncomeId)"
                      >更多</el-button>
                    </span>
                  </span>
          </template>
        </el-table-column>
      </el-table>
      <br/>
    </div>

    <br v-if="projectTypeFlag === '0' || projectTypeFlag === '2'|| projectTypeFlag === '3' || projectTypeFlag === 'all'"/>
    <br v-if="projectTypeFlag === '0' || projectTypeFlag === '2'|| projectTypeFlag === '3' || projectTypeFlag === 'all'"/>
    <div v-if="projectTypeFlag === '1' || projectTypeFlag === 'all'">
      <span>法催业务：</span>
    </div>
    <div v-if="projectTypeFlag === '1' || projectTypeFlag === 'all'">
      <el-table :data="feeCustListDetailForLaw"
                :span-method="overDetailBodyDetailList_v2panMethodForLaw"
                :header-cell-style="{fontSize:'14px'}"
                border
      >
        <!--        <el-table :data="detail.default" v-show="balanceon"-->
        <!--                  :span-method="overDetailBodyDetailList_v2panMethod"-->
        <!--                  :show-header="detail.showHeader"-->
        <!--                  :header-cell-style="{fontSize:'14px'}"-->
        <!--                  border-->
        <!--        >-->
        <el-table-column label="项目名称"  prop="projectName" min-width="20%" >
          <template slot-scope="scope">
            <a
              @click="handleProjectDetail(scope.row.projectId, scope.row.projectIncomeId, scope.row.phaseId, scope.row.projectType)">
              <span style="color:#1E90FF">{{scope.row.projectName}}</span>
            </a>
            <!--                <span v-if="scope.row.controlFlag === '1'">{{scope.row.project_name}}</span>-->
          </template>
        </el-table-column>
        <el-table-column label="业务期次"  prop="feeNoAlreadyTerm" min-width="20%"  />
        <el-table-column label="法催收入"  prop="incomeAmt" min-width="20%"  >
          <template slot-scope="scope">
            <span>{{formaterMoney(scope.row.incomeAmt)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="收款时间"  prop="collectionTime" min-width="20%"  />
        <el-table-column label="出信息费公司"  prop="custName" min-width="20%"  />
        <el-table-column label="信息费公司"  prop="feeCustName" min-width="20%"  />
        <!--        <el-table-column label="信息费"  prop="feeAmt" min-width="10%"  >-->
        <!--          <template slot-scope="scope">-->
        <!--            <span>{{formaterMoney(scope.row.feeAmt)}}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <el-table-column label="信息费取整"  prop="feeRound" min-width="10%"  >
          <template slot-scope="scope">
            <span>{{formaterMoney(scope.row.feeRound)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="法催利润"  prop="lawProfit" min-width="20%"  >
          <template slot-scope="scope">
            <span>{{formaterMoney(scope.row.lawProfit)}}</span>
          </template>
        </el-table-column>

        <!--            <el-table-column label="提成信息费"  prop="fee_amt2" min-width="10%"  >-->
        <!--              <template slot-scope="scope">-->
        <!--                <span>{{formaterMoney(scope.row.fee_amt2)}}</span>-->
        <!--              </template>-->
        <!--            </el-table-column>-->
      
        <el-table-column label="备注"  prop="remark" min-width="20%"  >
          <!--          <template slot-scope="scope">-->
          <!--            <span v-if="scope.row.remark !== null && scope.row.remark.length>15">-->
          <!--                      {{scope.row.remark.substring(0,10)}}<el-button-->
          <!--              type="text"-->
          <!--              size="mini"-->
          <!--              @click="textDetail(scope.row.remark)"-->
          <!--            >更多-->
          <!--                  </el-button>-->
          <!--                    </span>-->
          <!--            <span v-if="scope.row.remark !== null && scope.row.remark.length<=15">-->
          <!--                      {{scope.row.remark}}-->
          <!--                    </span>-->
          <!--          </template>-->
          <template slot-scope="scope">
                  <span v-if="scope.row.remark !== null && scope.row.remark.length>15">
                    {{scope.row.remark.substring(0,10)}}<el-button
                    type="text"
                    size="mini"
                    @click="textDetail(scope.row.remark, scope.row.projectIncomeId)"
                  >更多
                </el-button>
                  </span>
            <span v-if="scope.row.remark !== null && scope.row.remark.length<=15">
                    <span v-if="scope.row.remark !== ''">
                      {{scope.row.remark}}<el-button
                      type="text"
                      size="mini"
                      @click="textDetail(scope.row.remark, scope.row.projectIncomeId)"
                    >更多</el-button>
                      </span>
                  </span>
            <span v-if="scope.row.remark === null || scope.row.remark === ''">
                    <span v-if="(scope.row.incomeRejectionReason !== null && scope.row.incomeRejectionReason !== '') || (scope.row.feeRejectionReason !== null && scope.row.feeRejectionReason !== '')">
                      <el-button
                        type="text"
                        size="mini"
                        @click="textDetail(null, scope.row.projectIncomeId)"
                      >更多</el-button>
                    </span>
                  </span>
          </template>
        </el-table-column>
      </el-table>
    </div>


    <el-dialog :title="title" :visible.sync="remarkDeliog" width="50%" append-to-body :close-on-click-modal = "false">
      <div v-if="this.remarkObj !== null && this.remarkObj !==''">
        <span class="remarkDetailSpan">备注</span>
        <div style="width: 100%;height:30px;"></div>
        <span>{{ this.remarkObj }}</span>
      </div><br/>
      <div v-if="rejectionObjs.length !== 0">
        <span class="remarkDetailSpan">驳回记录</span>
        <div style="width: 100%;height:30px;"></div>
        <div v-for="(item, index) in this.rejectionObjs">
          <span>{{item.rejectionTime}}</span><span style="margin-left: 8px">{{item.rejectionUserName}}</span><span style="margin-left: 8px">驳回</span><br/>
          <span>{{item.parameterCode}}</span><span style="margin-left: 8px">驳回原因：</span><span>{{item.rejectionReason}}</span><br/><br/>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeRemarkDetail">关 闭</el-button>
      </div>
    </el-dialog>
<!--    <el-dialog :title="title" :visible.sync="remarkDeliog" width="50%" append-to-body :close-on-click-modal = "false">-->
<!--      <div v-if="this.remarkObj !== null && this.remarkObj !==''">-->
<!--        <span class="remarkDetailSpan">备注</span>-->
<!--        <div style="width: 100%;height:30px;"></div>-->
<!--        <span>{{ this.remarkObj }}</span>-->
<!--      </div><br/>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="closeRemarkDetail">关 闭</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
<!--    </div>-->

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { first, remarkQueryDetail } from "@/api/cwxmgl/cust";
import { getRejectionDetails } from '@/api/caiwu/project'

export default {
  name: "RemarkQuery",
  data() {
    return {
      //合并法催单元格参数
      merageArr4: [],
      meragePos4: 0,
      merageArr5: [],
      meragePos5: 0,
      merageArr6: [],
      meragePos6: 0,
      //法催项目详情
      feeCustListDetailForLaw: [],
      //要展示的数据
      projectTypeFlag: null,
      //驳回对象
      rejectionObjs:[],
      //备注详情展示
      remarkObj: null,
      remarkDeliog: false,
      //合并单元格参数
      merageArr1: [],
      meragePos1: 0,
      merageArr2: [],
      meragePos2: 0,
      merageArr3: [],
      meragePos3: 0,
      // showHeader: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //是否展示数据
      balanceon:false,
      notfound:false,
      unbalanceon:true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 财务项目管理-初次进入页面加载的信息费公司名称
      feeCustList: [],
      // 列表详情
      feeCustListDetail: [],
      // feeCustListSum: null,
      //页面参数
      sum_fee_already:"",
      sum_difference_amt:"",
      sum_fee_no_already:"",
      sum_pay_amt:"",
      sum_fee_amt_2:"",
      sum_fee_amt:"",
      not_found_by_java:"",

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        projectId: null,
        remark: null,
        rate: null,
        taxRate: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目管理表主键不能为空", trigger: "blur" }
        ],
        custName: [
          { required: true, message: "信息费公司名称不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // objectSpanMethod1 ({ row, column, rowIndex, columnIndex }) {
    //   if (columnIndex === 0) {
    //     if (rowIndex === 0) {
    //       let maxLen
    //       this.res.forEach(val => {
    //         const arr = [val.ext.default.length, val.ext.free.length, val.ext.pay.length]
    //         arr.sort((a, b) => a - b)// arr数组 按数字大小从小到大排序
    //         maxLen = arr.pop()// 取出排序后的数组arr中的最后一个元素
    //       })
    //       return {
    //         // 这个rowspan应该据 ext的default,pay,free的长度不同来定，取最大长度
    //         rowspan: maxLen,
    //         colspan: 1
    //       }
    //     } else {
    //       return {
    //         rowspan: 0,
    //         colspan: 0
    //       }
    //     }
    //   }
    // },

    /** 提交有查询条件的表单 */
    submitQueryForm() {

      this.loading = true;
      remarkQueryDetail(this.queryParams).then(response => {
        // this.feeCustListDetail = response.list;
        // this.merage(this.feeCustListDetail);
        // if (this.feeCustListDetail !== undefined){
        //   for (let i = 0; i < this.feeCustListDetail.length; i++) {
        //     // this.merageInit();
        //     this.merage(this.feeCustListDetail[i].default);
        //     if (i === 0) {
        //       this.feeCustListDetail[i].showHeader = true
        //     } else {
        //       this.feeCustListDetail[i].showHeader = true
        //     }
        //   }
        // }
        // this.feeCustListSum = response.sum;
        // this.feeCustListDetail = response.rows;
        this.not_found_by_java = response.info.error_code;
        this.projectTypeFlag = response.projectTypeFlag;
        if (response.info.error_code === "200") {
          if (response.list != undefined) {
            this.feeCustListDetail = response.list;
            this.merage(this.feeCustListDetail);
          }
          //todo 法催
          if (response.lawList != undefined) {
            this.feeCustListDetailForLaw = response.lawList;
            this.merageLaw(this.feeCustListDetailForLaw);
          }
          // for (var i in this.feeCustListDetail) {
          //   this.sum_fee_already = this.feeCustListSum.sum_fee_already;
          //   this.sum_fee_no_already = response.sum;
            // this.sum_difference_amt = this.feeCustListSum.sum_difference_amt;
            // this.sum_pay_amt = this.feeCustListSum.sum_pay_amt;
            // this.sum_fee_amt_2 = this.feeCustListSum.sum_fee_amt_2;
            // this.sum_fee_amt = this.feeCustListSum.sum_fee_amt;
            this.not_found_by_java = response.info.error_code;
        }
        if (this.not_found_by_java === "404"){
          // this.balanceon=false;
          this.unbalanceon=false;
          this.notfound=true;
          this.not_found_by_java="";
        // }else if (response.rows.length !== 0){
        }else if (this.not_found_by_java === "200"){
          // this.balanceon=true;
          this.unbalanceon=false;
          this.notfound=false;
          this.not_found_by_java="";
        }else  {
          this.unbalanceon=true;
          this.notfound=false;
          // this.balanceon=false;
          this.not_found_by_java="";
        }

      })
    },
    resetQueryForm() {
      this.$refs['queryForm'].resetFields()
    },

    // 要合并的数组的方法
    merage(tableData) {
      this.merageInit();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          this.merageArr1.push(1);
          this.meragePos1 = 0;
          this.merageArr2.push(1);
          this.meragePos2 = 0;
          this.merageArr3.push(1);
          this.meragePos3 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if ((typeof tableData[i].projectId !== 'undefined' && tableData[i].projectId != null && tableData[i].projectId !== '') && tableData[i].projectId === tableData[i - 1].projectId) {
            this.merageArr1[this.meragePos1] += 1;
            this.merageArr1.push(0);
          } else {
            this.merageArr1.push(1);
            this.meragePos1 = i;
          }

          if ((typeof tableData[i].phaseId !== 'undefined' && tableData[i].phaseId != null && tableData[i].phaseId !== '') && tableData[i].phaseId === tableData[i - 1].phaseId) {
            this.merageArr2[this.meragePos2] += 1;
            this.merageArr2.push(0);
          } else {
            this.merageArr2.push(1);
            this.meragePos2 = i;
          }

          if ((typeof tableData[i].projectFeeId !== 'undefined' && tableData[i].projectFeeId != null && tableData[i].projectFeeId !== '') && tableData[i].projectFeeId === tableData[i - 1].projectFeeId) {
            this.merageArr3[this.meragePos3] += 1;
            this.merageArr3.push(0);
          } else {
            this.merageArr3.push(1);
            this.meragePos3 = i;
          }
        }
      }
    },
    // 要合并的数组的方法
    merageLaw(tableData) {
      this.merageLawInit();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          this.merageArr4.push(1);
          this.meragePos4 = 0;
          this.merageArr5.push(1);
          this.meragePos5 = 0;
          this.merageArr6.push(1);
          this.meragePos6 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if ((typeof tableData[i].projectId !== 'undefined' && tableData[i].projectId != null && tableData[i].projectId !== '') && tableData[i].projectId === tableData[i - 1].projectId) {
            this.merageArr4[this.meragePos4] += 1;
            this.merageArr4.push(0);
          } else {
            this.merageArr4.push(1);
            this.meragePos4 = i;
          }

          if ((typeof tableData[i].phaseId !== 'undefined' && tableData[i].phaseId != null && tableData[i].phaseId !== '') && tableData[i].phaseId === tableData[i - 1].phaseId) {
            this.merageArr5[this.meragePos5] += 1;
            this.merageArr5.push(0);
          } else {
            this.merageArr5.push(1);
            this.meragePos5 = i;
          }

          if ((typeof tableData[i].projectFeeId !== 'undefined' && tableData[i].projectFeeId != null && tableData[i].projectFeeId !== '') && tableData[i].projectFeeId === tableData[i - 1].projectFeeId) {
            this.merageArr6[this.meragePos6] += 1;
            this.merageArr6.push(0);
          } else {
            this.merageArr6.push(1);
            this.meragePos6 = i;
          }
        }
      }
    },
    merageInit() {
      this.merageArr1 = [];
      this.meragePos1 = 0;
      this.merageArr2 = [];
      this.meragePos2 = 0;
      this.merageArr3 = [];
      this.meragePos3 = 0;
    },
    merageLawInit() {
      this.merageArr4 = [];
      this.meragePos4 = 0;
      this.merageArr5 = [];
      this.meragePos5 = 0;
      this.merageArr6 = [];
      this.meragePos6 = 0;
    },
    //合并单元格
    overDetailBodyDetailList_v2panMethod({ row, column, rowIndex, columnIndex }) {
      // if (columnIndex === 0) {
      //   if (rowIndex === 0) {
      //     return {
      //       rowspan: 99999,
      //       colspan: 1
      //     };
      //   } else {
      //     return {
      //       rowspan: 0,
      //       colspan: 0
      //     };
      //   }
      // } else
      if (columnIndex === 0) {
        const _row = this.merageArr1[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }

      if (columnIndex === 1 || columnIndex === 2
        // || columnIndex === 3
        || columnIndex === 6 || columnIndex === 7 || columnIndex === 8 || columnIndex === 12) {
        const _row = this.merageArr2[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }

      if (columnIndex === 4 || columnIndex === 5 || columnIndex === 3) {
        const _row = this.merageArr3[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    //合法催并单元格
    overDetailBodyDetailList_v2panMethodForLaw({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.merageArr4[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }

      if (columnIndex === 1 || columnIndex === 2
        || columnIndex === 3 || columnIndex === 4
        || columnIndex === 5
        || columnIndex === 6 || columnIndex === 7 || columnIndex === 8 || columnIndex === 9 || columnIndex === 13) {
        const _row = this.merageArr5[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }

      if (columnIndex === 4 || columnIndex === 5 || columnIndex === 3) {
        const _row = this.merageArr6[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return {
            rowspan: 99999,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },


    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      let i = 1;
      columns.forEach((column, index) => {
        i++;
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } /*else if (Array.isArray(value)) {
              let sum = 0;
              for (let v of value) {
                sum += v.
              }
              return sum
            }*/ else {
              return prev;
            }
          }, 0);
          var formaterMoney = this.formaterMoney(sums[index])
          // sums[index] += ' 元';
          sums[index] = formaterMoney;
        } else {
          // sums[index] = 'N/A';
          sums[index] =  '共 '+data.length+' 期';
        }
      });

      return sums;
    },

    /** 格式化金额 */
    formaterMoney(data) {
      if (!data) return '0.00'
      if (data === '-') return '-'
      if (data === '您的业务流程有误，请再次核对！') return '数据错误！'
      // 将数据分割，保留两位小数
      data = data.toFixed(2)
      // 获取整数部分
      const intPart = Math.trunc(data)
      // 整数部分处理，增加,
      const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      // 预定义小数部分
      let floatPart = '.00'
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split('.')
      if (newArr.length === 2) { // 有小数部分
        floatPart = newArr[1].toString() // 取得小数部分
        if (1/intPart < 0 && intPart === 0) {
          return '-' + intPartFormat + '.' + floatPart
        }
        return intPartFormat + '.' + floatPart
      }
      if (1/intPart < 0 && intPart === 0) {
        return '-' + intPartFormat + '.' + floatPart
      }
      return intPartFormat + floatPart
    },
    /** 查询财务项目管理-信息费公司与费率列表 */
    getList() {
      this.balanceon=false;
      this.loading = true;
      // first(this.queryParams).then(response => {
      //   this.feeCustList = response.cust_name;
      //   // this.total = response.total;
      //   this.loading = false;
      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        custName: null,
        rate: null,
        taxRate: null,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageNum = 1;
      this.submitQueryForm();
      // this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.projectTypeFlag = null;
      this.resetForm("queryForm");
      // this.handleQuery();
      this.sum_fee_no_already="";
      this.queryParams.remark=null;
      this.balanceon=false;
      this.unbalanceon=true;
      this.notfound=false;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加财务项目管理-信息费公司与费率";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCust(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改财务项目管理-信息费公司与费率";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCust(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCust(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除财务项目管理-信息费公司与费率编号为"' + ids + '"的数据项？').then(function() {
        return delCust(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('cwxmgl/cust/exportFeeNoAlreadyQueryDetail', {
        ...this.queryParams
      }, `信息费未结清查询_${new Date().getTime()}.xlsx`)
    },
    /** 跳转项目详情*/
    handleProjectDetail(projectId, projectIncomeId, phaseId, projectType) {
      const productId = projectId;
      if (projectType === '0' || projectType === '2') {
        const incomeId = projectIncomeId;
        //该项目是普通项目，那么就跳转普通项目的详情。传参项目id和收入id
        this.$router.push({path: '/caiwu/projectDetails', query:{productId, incomeId}})
      }
      if (projectType === '1') {
        const incomeId = phaseId;
        //该项目是法催项目，那么就跳转法催项目的详情。传参项目id和期次id还有收入id都传过去，用到什么拿什么
        this.$router.push({path: '/caiwu/projectDetailsLaw', query:{productId, incomeId}})
      }
    },
    closeRemarkDetail() {
      this.remarkDeliog = false;
      this.remarkObj = null;
    },
    /** 备注显示更多按钮 */
    textDetail(remark, incomeId) {
      // this.remarkObj = remark;
      // this.remarkDeliog = true;
      var a = {
        projectIncomeId : incomeId
      };
      //用incomeId去查对应的驳回表
      getRejectionDetails(a).then((response) => {
        this.rejectionObjs = response;
        if (remark !== null && remark !== '') {
          this.remarkObj = remark;
        }
        this.remarkDeliog = true;
      });
    }
  }
};
</script>

<style>
.el-row {
  margin-bottom: 20px;
}
.grid-content {
  /* border-radius: 10px;
  height: 50px;
  line-height: 14px; */
  color:#9D9D9D;
  /* font-weight:bold; */
  font-size:15px;
  text-align: center;
  margin-left: 24px;
}
.grid-contentfont{
  color:#333333;
  /*font-weight:bold;*/
  font-size:15px;
}
.grid-contentcol{
  height: 20px;
  line-height: 40px;
  left: 30px;
}
.grid-col1 {
  border-radius: 4px;
  height: 36px;

}
.bg-purple {
  background: #9D9D9D;
}
#col-line {
  float: left;
  width: 1px;
  height: 60px;
  background: 	#E6E6E6;
}

.span{
  color:#ff8000;
  font-weight:bold;
  margin-left: 24px;
  font-size:24px;
}
.spancol{
  color:#333333;
  /*font-weight:bold;*/
  font-size:24px;
  display: inline-block;
  margin-left: 24px;
  /* padding-top:10px; */

}
.amounting{
  font-size:14px;
  color:#9D9D9D;
  margin-left: 24px;
  margin-top:10px;
  display:block;
}
.spancol2{
  font-size:15px;
  color: 	#9D9D9D;
  margin-left: 24px;
  display:block;
}
.spancol3{
  font-size:15px;
  color: 	#363636;
  /*margin-left: 24px;*/
  /*display:block;*/
}
.echartspan{
  color: 	#007fff;
  font-size:12px;
  /* font-weight:bold; */
  /* margin-left: 60px; */
}
.balancediv{
  width: 100px;
  height:35px;
}
.item {
  margin: 4px;
}
.divsecend {
  /* border-radius: px; */
  min-height: 100px;
  background-color: #FFFFFF;
}
.spanfont {
  color: 	#333333;
  font-size:16px;
  font-weight:normal;
  font-family:"Microsoft YaHei";
  margin-left: 24px;
}

.inner{
  width: 49%;
  height: 12%;
  background: #FFFFFF;
  margin: 4px;
}
.inneraaa{
  width: 24.3%;
  height: 100px;
  background: #FFFFFF;
  margin: 4px;
}
.innerone{
  width: 24.3%;
  height: 100px;
  background: #FFFFFF;
  margin: 4px;
}
.dialogspan{
  font-size:10px;
  color: 	#afadad;
  /* font-weight:bold; */
}
/* 设置el-select */
.el-select .el-input__inner {
  height: 32px;
}
.inneraaaA{
  width: 100%;
  height: 100px;
  background: #FFFFFF;
  margin: 1px;
}
</style>
