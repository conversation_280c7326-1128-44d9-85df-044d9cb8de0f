<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="custNo">
        <el-select v-model="queryParams.custNo" placeholder="请选择公司" clearable size="small">
          <el-option
            v-for="dict in custList"
            :key="dict.guaranteeCompanyId"
            :label="dict.guaranteeCompanyName"
            :value="dict.guaranteeCompanyId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报表类型" prop="reportType">
        <el-select v-model="queryParams.reportType" placeholder="请选择报表类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.report_date_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报表名称" prop="reportFormsName">
        <el-input
          v-model="queryParams.reportFormsName"
          placeholder="请输入报表名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
    
      
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="recordList">
      <el-table-column label="公司名称" align="center" prop="custNo">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cust_no" :value="scope.row.custNo"/>
        </template>
      </el-table-column>
      <el-table-column label="报表类型" align="center" prop="reportType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.report_date_type" :value="scope.row.reportType"/>
        </template>
      </el-table-column>
      <el-table-column label="报表名称" align="center" prop="reportFormsName" />
      <el-table-column label="校验人" align="center" prop="createBy" >
        <template slot-scope="scope">
          <template v-for="(item) in userListAll.filter(item => item.userName===scope.row.createBy)">
            {{item.nickName}} 
          </template>
        </template>
      </el-table-column>
      <el-table-column label="校验时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['core:cwbbjy:record:view']"
          >查看</el-button>
          
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改校验记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1100px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        
        <el-form-item label="公司名称" prop="custNo">
          <dict-tag :options="dict.type.cust_no" :value="form.custNo"/>
        </el-form-item>
        <el-form-item label="报表类型" prop="reportType">
          <dict-tag :options="dict.type.report_date_type" :value="form.reportType"/>
        </el-form-item>
        <el-form-item label="报表名称" prop="reportFormsName">
          {{form.reportFormsName}}
        </el-form-item>

        
        <el-form-item label="校验文件" >
          <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport(form.fileName,form.url)"
        >{{form.fileName}}</el-button>
        </el-form-item>
        <el-form-item label="校验人" prop="createBy">
          <template v-for="(item) in userListAll.filter(item => item.userName===form.createBy)">
            {{item.nickName}} 
          </template>
        </el-form-item>
        <el-form-item label="校验时间" prop="createTime">
          <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </el-form-item>
          


          <el-table v-loading="loading" :data="form.result" border  >
            <el-table-column label="效验规则" align="center" prop="key"  />
            <el-table-column label="结果" align="center" prop="value" >
              <template slot-scope="scope">
                <span v-if="scope.row.value == '校验通过'"><i class="el-icon-success"  style="color: #1ab394 "></i> 通过</span>
                <span v-if="scope.row.value !== '校验通过'" style="color: #f34303 "><i class="el-icon-circle-close" style="color: #f34303 "></i>{{scope.row.value}}</span>
              </template>
            </el-table-column>
          </el-table>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {listRecord, getRecord,custList,reportTypeList} from "@/api/cwbbjy/cwbbjy";

import { getUserListAll } from "@/api/system/user";

import { downloadByUrl } from "@/api/oa/processTemplate";

export default {
  name: "Record",
  dicts: ['report_date_type','cust_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 校验记录表格数据
      recordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        custNo: null,
        reportType: null,
        reportFormsName: null,
        result: null,
      },
      userListAll: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      custList: [],
      reportTypeList: [],
    };
  },
  created() {
    this.getList();
    this.getUserListAllF();
    this.getcustList();
    this.getreportTypeList();
  },
  methods: {
    
    getcustList() {
      custList(this.fromData).then(response => {
        this.custList = response.data;
      });
    },
    getreportTypeList() {
      reportTypeList(this.fromData).then(response => {
        var tempList=response.data;
        for(var itemE of tempList){
          this.reportTypeList.push(itemE.reportType);
        }
      });
    },
    /** 查询所有用户 */
    getUserListAllF() {
      getUserListAll().then(response => {
        this.userListAll = response.data;
      });
    },
    /** 查询校验记录列表 */
    getList() {
      this.loading = true;
      listRecord(this.queryParams).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        custNo: null,
        reportType: null,
        reportFormsName: null,
        result: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
 
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids
      getRecord(id).then(response => {
        this.form = response.data;

        this.form.result=JSON.parse(this.form.result);
        this.open = true;
        this.title = "查看校验记录";
      });
    },
    /** 导出按钮操作 */
    handleExport(fileName,url) {

      downloadByUrl({
        url: url,
      }).then((res) => {
        let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
        const link = document.createElement("a"); //创建一个隐藏的a标签
        link.target = "_blank";
        link.href = href; //设置下载的url
        link.download = fileName; //设置下载的文件名
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(href); // 释放掉blob对象
      });
    }
  }
};
</script>
