<template>
  <div>
    <el-dialog
      title="生成记账凭证"
      :visible.sync="dialogVisible"
      width="1050px"
      :before-close="handleClose"
    >
      <div v-if="form">
        <div>收款日期：{{ form.lyxDayCheck.dayBillDate }}</div>
        <div>提交后，将按照以下规则生成记账凭证</div>
        <div style="margin-top: 16px">
          <span style="font-weight: bold">摘要</span>：{{
            form.lyxDayCheck.dayBillDate
          }}营业款
        </div>
        <div class="content" style="margin-top: 12px">
          <div class="left" style="margin-right: 16px">
            <div class="type">
              <div class="_title">借方科目</div>
              <div>应收帐款-中信大厦</div>

              <div
                v-for="(item, index) in leftArr"
                :key="index"
                v-if="leftArr.length > 0"
              >
                应收帐款-{{ item.revenueItem }}
              </div>
              <div>现金</div>
              <div>银行存款-樊楼民生行</div>
              <div>银行存款-樊楼民生行POS机到账</div>
              
              <div>财务费用-手续费</div>
            </div>
            <div class="money">
              <div class="_title" style="border-right: 1px solid #ccc">
                金额
              </div>
              <div class="tab">
                {{ formatAmount(form.lyxDayCheck.mealCard) }}
              </div>

              <div
                class="tab"
                v-for="(item, index) in leftArr"
                :key="index"
                v-if="leftArr.length > 0"
              >
                {{
                  item.money === null || item.money === undefined
                    ? formatAmount(item.amount)
                    : formatAmount(item.money)
                }}
              </div>
              <div class="tab">
                {{ formatAmount(form.lyxDayCheck.cash) }}
              </div>
              <div class="tab">
                {{ formatAmount(form.lyxDayCheck.szzxkInto) }}
              </div>
              <div class="tab">
                {{
                  formatAmount(
                    form.lyxDayCheck.msNormalAccountInto +
                      form.lyxDayCheck.zxBasicAccountInto
                  )
                }}
              </div>
              <div class="tab">
                {{ formatAmount(form.lyxDayCheck.bankServiceCharge) }}
              </div>
            </div>
          </div>
          <div class="center">
            <div class="center_top">
              <div class="type">
                <div class="_title">贷方科目</div>
                <div>饭卡收入</div>
                <div>餐费收入</div>
              </div>
              <div class="money">
                <div class="_title">金额</div>
                <div class="tab">
                  {{ formatAmount(form.lyxDayCheck.mealCard) }}
                </div>
                <div class="tab">
                  {{ formatAmount(form.lyxDayCheck.cfsk) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <el-button type="text" v-if="!editType" @click="editType = true"
          >编辑动态收入项科目</el-button
        >
        <div
          v-if="editType"
          style="background: #f7f7f7; width: 100%; padding: 16px"
        >
          <div>
            以下是根据动态收入项自动生成的科目，您可以自定义科目名称与取值来源
          </div>
          <el-table
            :data="tableData"
            border=""
            style="width: 100%; margin-top: 16px"
          >
            <el-table-column prop="revenueItem" label="科目名称">
              <template slot-scope="scope">
                应收账款-<el-input
                  style="width: 250px; margin-left: 9px"
                  v-model="scope.row.revenueItem"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="取值来源" width="300">
              <template slot-scope="scope">
                <div v-for="(item, index) in scope.row.list" :key="index">
                  <el-select
                    @change="changeItem(scope.row, index, $event)"
                    v-model="item.dynamicIncomeId"
                  >
                    <el-option
                      v-for="item in form.lyxDynamicIncomeList"
                      :key="item.dynamicIncomeId"
                      :label="item.revenueItem"
                      :value="item.dynamicIncomeId"
                    ></el-option></el-select
                  ><el-button
                    type="text"
                    style="margin-left: 8px"
                    @click="delItem(scope.row, index)"
                    >删除</el-button
                  >
                </div>
                <el-button type="text" @click="addItem(scope.row)"
                  >添加动态收入项</el-button
                >
              </template>
            </el-table-column>
            <el-table-column prop="money" label="金额" width="120">
              <template slot-scope="scope">{{
                formatAmount(scope.row.money)
              }}</template></el-table-column
            >
            <el-table-column prop="date" label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="delRow(scope.row)"
                  >删除科目</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 16px">
            <el-button
              @click="addSub"
              style="margin-right: 766px"
              type="primary"
              size="mini"
              >添加科目</el-button
            >
            <el-button type="primary" @click="set" size="mini">保存</el-button>
            <el-button size="mini" @click="reset">取消</el-button>
          </div>
        </div>
        <el-divider></el-divider>
        <div>
          <span style="font-weight: bold">合计</span>
          <span style="margin-left: 250px">{{ formatAmount(jfMoney) }}</span>
          <span style="margin-left: 300px">{{ formatAmount(dfMoney) }}</span>
        </div>
        <div style="margin-top: 16px">
          <span style="font-weight: bold">制单人：</span>{{ userName }}
        </div>
        <div>
          <span style="font-weight: bold">账套：</span>北京樊楼餐饮有限公司
        </div>
        <div style="color: #999">如果某科目项下金额为0，则不会生成该条科目</div>
        <div style="color: #999">生成凭证后，当日收款记录将不能再被编辑</div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">提 交</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { createDayVouchar } from "@/api/lyxManage/dailyLedger";

export default {
  props: {
    detail: Object,
  },
  data() {
    return {
      userName: sessionStorage.getItem("userNickName"),
      editType: false,
      tableData: [],
      inComeList: [],
      dialogVisible: true,
      form: null,
      mtMoney: 0,
      jfMoney: 0,
      dfMoney: 0,
      options: [],
    };
  },
  mounted() {
    this.form = JSON.parse(JSON.stringify(this.detail));
    this.mtMoney = 0;
    let arr = [];
    let arr2 = [];
    if (this.form.lyxDynamicIncomeList.length > 0) {
      this.form.lyxDynamicIncomeList.forEach((item) => {
        if (item.revenueItem.includes("美团")) {
          this.mtMoney += item.amount;
          arr2.push(item);
        } else {
          arr.push(item);
        }
      });
    }
    this.leftArr = JSON.parse(JSON.stringify(arr));
    this.leftArr.unshift({
      revenueItem: "美团券",
      amount: this.mtMoney,
    });
    this.jfMoney =
      this.form.lyxDayCheck.mealCard +
      this.form.lyxDayCheck.cash +
      this.form.lyxDayCheck.szzxkInto +
      this.form.lyxDayCheck.msNormalAccountInto +
      this.form.lyxDayCheck.zxBasicAccountInto +
      this.form.lyxDayCheck.bankServiceCharge;
    this.leftArr.forEach((item) => {
      this.jfMoney += item.amount;
    });
    this.dfMoney = this.form.lyxDayCheck.mealCard + this.form.lyxDayCheck.cfsk;
    this.tableData = JSON.parse(JSON.stringify(this.form.lyxDynamicIncomeList));
    this.tableData.forEach((item) => {
      this.$set(item, "list", [
        {
          amount: item.amount,
          dynamicIncomeId: item.dynamicIncomeId,
          revenueItem: item.revenueItem,
        },
      ]);
    });
    this.tableData.forEach((item) => {
      item.money = item.list.reduce((a, b) => {
        return a + b.amount;
      }, 0);
    });
    if (arr2.length > 0) {
      this.tableData.unshift({
        revenueItem: "美团券",
        list: arr2,
        money: this.mtMoney,
      });
      this.tableData.forEach((item, index) => {
        if (item.revenueItem.includes("美团") && item.revenueItem != "美团券") {
          this.tableData.splice(index, 1);
        }
      });
    }

    console.log(this.tableData);
  },
  methods: {
    submit() {
      console.log(this.formatAmount(this.jfMoney),this.formatAmount(this.dfMoney));
  
      if (this.formatAmount(this.jfMoney) != this.formatAmount(this.dfMoney)) {
        this.$message.warning("借贷不平衡！");
        return;
      }
      this.$confirm(
        "是否确定对该日记录生成记账凭证？生成凭证后，该日收款记录将不能再被修改编辑",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          let data = {
            collectionDate: this.$format(
              this.form.lyxDayCheck.dayBillDate,
              "yyyy-MM-dd"
            ),
            zxds: this.form.lyxDayCheck.mealCard,
            cash: this.form.lyxDayCheck.cash,
            szkdz: this.form.lyxDayCheck.szzxkInto,
            msyhPOS:
              this.form.lyxDayCheck.msNormalAccountInto +
              this.form.lyxDayCheck.zxBasicAccountInto,
            serviceCharge: this.form.lyxDayCheck.bankServiceCharge,
            fkIncome: this.form.lyxDayCheck.mealCard,
            cfIncome: this.form.lyxDayCheck.cfsk,
            voucharAbstract: this.form.lyxDayCheck.dayBillDate + "营业款",
            dynamicVoList: [],
            dayCheckId: this.form.lyxDayCheck.id,
          };
          console.log(this.leftArr);
          data.dynamicVoList = this.leftArr.map((item) => {
            return {
              name: item.revenueItem,
              amount: item.money ? item.money : item.amount,
            };
          });
          console.log(data);
          createDayVouchar({ ...data }).then((res) => {
            this.$message.success("生成记账凭证成功");
            this.$emit("submit");
          });
        })
        .catch(() => {});
    },
    set() {
      let flag = false;
      this.tableData.forEach((item) => {
        if (!item.revenueItem) {
          flag = true;
        }
      });
      if (flag) {
        this.$message.warning("请填写科目名称");
        return;
      }
      this.leftArr = JSON.parse(JSON.stringify(this.tableData));
      console.log(this.leftArr);
      this.jfMoney = 0;
      this.jfMoney =
        this.form.lyxDayCheck.mealCard +
        this.form.lyxDayCheck.cash +
        this.form.lyxDayCheck.szzxkInto +
        this.form.lyxDayCheck.msNormalAccountInto +
        this.form.lyxDayCheck.zxBasicAccountInto +
        this.form.lyxDayCheck.bankServiceCharge;
      this.leftArr.forEach((item) => {
        this.jfMoney += item.money;
      });
      this.editType = false;
    },
    addSub() {
      console.log(this.tableData);
      this.tableData.push({
        revenueItem: "",
        list: [],
        money: 0,
        id: new Date().getTime(),
      });
    },
    reset() {
      this.editType = false;
    },
    delRow(row) {
      this.tableData.forEach((item, index) => {
        if (item.id == row.id) {
          this.tableData.splice(index, 1);
        }
      });
    },
    changeItem(row, i, e) {
      this.tableData.forEach((item) => {
        if (item.id == row.id) {
          let data = this.form.lyxDynamicIncomeList.find((item) => {
            return item.dynamicIncomeId == e;
          });
          item.list[i] = {
            amount: data.amount,
            dynamicIncomeId: data.dynamicIncomeId,
            revenueItem: data.revenueItem,
          };
        }
      });
      console.log(this.tableData);
      this.tableData.forEach((item) => {
        item.money = item.list.reduce((a, b) => {
          return a + b.amount;
        }, 0);
      });
    },
    addItem(row) {
      this.tableData.forEach((item) => {
        if (item.id == row.id) {
          item.list.push({ revenueItem: "", dynamicIncomeId: "", amount: "" });
        }
      });
    },
    delItem(row, i) {
      console.log(row, i);
      this.tableData.forEach((item) => {
        if (item.id == row.id) {
          item.list.splice(i, 1);
        }
        item.money = item.list.reduce((a, b) => {
          return a + b.amount;
        }, 0);
      });
    },
    handleClose() {
      this.$emit("close");
    },

    formatAmount(a, b) {
      if (a == "" || a == "undefined" || a == null) {
        return "0.00";
      }
      var c = "number" == typeof b && b > 0 && 20 >= b ? b : 2;
      a = parseFloat((a + "").replace(/[^\d.-]/g, "")).toFixed(c) + "";
      for (
        var d = a.split(".")[0].split("").reverse(),
          e = a.split(".")[1],
          f = "",
          g = 0;
        g < d.length;
        g++
      )
        f += d[g] + ((g + 1) % 3 == 0 && g + 1 != d.length ? "," : "");
      var re = f.split("").reverse().join("") + "." + e;
      if (re.substr(0, 2) == "-,") {
        return re.replace(",", "");
      } else {
        return re;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  .left {
    width: 400px;
    display: flex;

    box-sizing: border-box;
    border-bottom: none;
    .type {
      div {
        border-left: 1px solid #ccc;
      }
    }
    .money {
      .tab {
        border-right: 1px solid #ccc;
        text-align: right;
        padding-right: 12px;
      }
    }
    ._title {
      border-top: 1px solid #ccc;
    }
  }
  .center,
  .right {
    width: 320px;
    .center_top {
      display: flex;
      border: 1px solid #ccc;
      border-bottom: none;
    }
    .tab {
      border-right: 1px solid #ccc;
      text-align: right;
      padding-right: 12px;
    }
    .center_top2 {
    }
  }
}
.right {
  margin-left: 56px;
  .type {
    width: 190px !important;
    div {
      padding-left: 10px;
    }
  }
  .money {
    width: 80px !important;
    div {
      padding-left: 10px;
    }
  }
}
.type {
  width: 260px;

  div {
    padding-left: 10px;
    box-sizing: border-box;
    height: 32px;
    line-height: 32px;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
  }
}
.money {
  width: 120px;
  div {
    padding-left: 2px;
    padding-right: 2px;
    box-sizing: border-box;
    height: 32px;
    line-height: 32px;

    border-bottom: 1px solid #ccc;
    /deep/ .el-input__inner {
      height: 28px !important;
    }
  }
}
._title {
  text-align: center;
  background: #f2f2f2;
  font-weight: bold;
}
/deep/ .el-dialog__footer {
  text-align: center !important;
}
.words {
  text-align: center;
  margin-bottom: 0;
  margin-top: 10px;
  color: #ccc;
}
.item {
  i {
    color: red;
    margin-right: 5px;
  }
}
/deep/ .el-select {
  padding: 0 !important;
}
.del_btn {
  position: absolute;
  top: 0;
  right: -40px;
}
/deep/.content .el-select .el-input__inner {
  height: 28px !important;
}
/deep/ .el-input-number {
  width: 100%;
}
</style>