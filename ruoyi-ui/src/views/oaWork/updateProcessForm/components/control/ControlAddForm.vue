<template>
  <div>
    <div class="data_content">
      <div style="display: flex">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          {{ type ? "删除管控规则信息：" : "新增管控规则信息：" }}
        </p>
        <div>
          <div v-if="!type">
            以下管控规则已由风险经理确认可装入 [展示系统]，
            需要由管理员进行审核是否允许装入
          </div>
          <div v-else>
            风险经理申请删除以下规则，需要由管理员进行审核是否允许删除
          </div>
          <div v-if="!type">共{{ myForm.length }}条</div>
          <div v-for="(item, index) in myForm" :key="index">
            <div class="solid" v-if="!type">管控规则{{ index + 1 }}</div>
            <div style="display: flex">
              <div style="margin-right: 140px">
                <div class="item">
                  <span>新项目名称：</span>{{ item.controlProjectRuleBo.projectName }}
                </div>
                <div class="item">
                  <span>原项目（数据源）：</span>{{ item.controlProjectRuleBo.oldProjectName }}
                </div>
                <div class="item">
                  <span>规则状态：</span>{{ item.controlProjectRuleBo.status | status }}
                </div>
                <div class="item">
                  <span>导入方式：</span>{{
                    item.controlProjectRuleBo.importType == 1 ? "自动" : "手动"
                  }}
                </div>
                <div class="item">
                  <span>预计可导入最大在贷余额：</span>{{ $formaterMoney(item.controlProjectRuleBo.overPredictLoanAmount != null ? item.controlProjectRuleBo.overPredictLoanAmount : item.controlProjectRuleBo.predictLoanAmount) }}元，实际导入不超过新项目设置的管控限额
                </div>
                <div class="item">
                  <span>规则说明：</span>{{ item.controlProjectRuleBo.ruleDescription }}
                </div>
              </div>
              <div>
                <div class="item">
                  <span>担保类型：</span>
                  {{
                    item.projectDetailsBo.guaranteeType == 1
                      ? "自有业务担保"
                      : item.projectDetailsBo.guaranteeType == 2
                        ? "拓展业务直保"
                        : "拓展业务分保"
                  }}
                </div>
                <div class="item">
                  <span>保证金：</span>{{ $formaterMoney(item.projectDetailsBo.earnestMoney) }}元
                </div>
                <div class="item">
                  <span>规则创建人：</span>{{ item.controlProjectRuleBo.userName }}
                </div>
                <div class="item" style="display: flex">
                  <span>放款起止日期：</span>
                  <div>
                    <div v-for="(v, i) in item.controlProjectRuleBo
                      .loanDateDetailList" :key="i">
                      {{ v.loanBeginDate }}-{{ v.loanEndDate }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-divider></el-divider>
            <div>
              新项目当前信息<span>管控规则总数：{{ item.currentProjectInfo.ruleNum }}</span>
            </div>
            <el-table :data="item.tableData" border style="width: 720px">
              <el-table-column prop="name" label="管控指标" width="180" />
              <el-table-column prop="loanAmount" label="已装入数据合计" width="180"><template slot-scope="scope">
                  {{
                    scope.row.name == "项目保证金比例"
                      ? (scope.row.loanAmount * 10000 / 100).toFixed(2) + "%"
                      : $formaterMoney(scope.row.loanAmount) || 0 + "元"
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="maxAmount" label="限额" width="180">
                <template slot-scope="scope">
                  {{
                    scope.row.name == "项目保证金比例"
                      ? (scope.row.maxAmount ? (scope.row.maxAmount * 10000 / 100).toFixed(2) + "%" : '-')
                      : $formaterMoney(scope.row.maxAmount) + "元"
                  }}
                </template>
              </el-table-column>
              <el-table-column prop="overLimit" label="是否达到限额">
                <template slot-scope="scope">
                  {{ scope.row.overLimit == 1 ? "是" : "否" }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div style="display: flex; margin-top: 16px">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          <i style="color: red; margin-right: 5px">*</i>{{ type ? "删除管控规则说明：" : "新增管控规则说明：" }}
        </p>
        <el-input type="textarea" :rows="4" :disabled="disabled" @input="inputChange" show-word-limit maxlength="200"
          placeholder="请输入内容" v-model="textarea">
        </el-input>
      </div>
    </div>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
export default {
  dicts: ["system_product"],
  props: {
    data: {
      type: Array || Object,
      default: () => [],
    },
    disabled: Boolean,
    info: String,
    type: Boolean,
  },
  data() {
    return {
      myForm: {},
      textarea: "",
    };
  },
  filters: {
    status(v) {
      if (v == 1) {
        return "计算在贷中";
      } else if (v == 2) {
        return "等待风险确认";
      } else if (v == 3) {
        return "新增规则待审核";
      } else if (v == 4) {
        return "新增规则审核中";
      } else if (v == 5) {
        return "自动导入中";
      } else if (v == 6) {
        return "自动导入暂停";
      } else if (v == 7) {
        return "自动导入完成";
      } else if (v == 8) {
        return "手动导入完成";
      } else if (v == 9) {
        return "新增规则驳回";
      } else if (v == 10) {
        return "新增规则装入失败";
      } else if (v == 11) {
        return "修改规则计算中";
      } else if (v == 12) {
        return "修改规则待审核";
      } else if (v == 13) {
        return "修改规则审核中";
      } else if (v == 14) {
        return "修改规则驳回";
      } else if (v == 15) {
        return "修改规则装入失败";
      } else if (v == 16) {
        return "删除规则审核中";
      } else if (v == 17) {
        return "删除规则驳回";
      }
    },
  },
  mounted() {
    this.textarea = this.info;
    console.log(this.type);

    if (this.type) {
      this.myForm = [this.data];
    } else {
      this.myForm = this.data;
    }

    console.log(this.myForm);

    this.myForm.forEach((item) => {
      item.tableData = [];
      item.tableData = [
        {
          name: "项目在贷余额",
          overLimit: item.currentProjectInfo.projectOverLimit,
          maxAmount: item.currentProjectInfo.projectMaxAmount,
          loanAmount: "",
        },
        {
          name: "项目保证金比例",
          overLimit: item.currentProjectInfo.bondOverLimit,
          maxAmount: item.currentProjectInfo.bondLimitAmount,
          loanAmount: item.currentProjectInfo.bondLoanAmount,
        },
        {
          name: "担保公司担保余额",
          overLimit: item.currentProjectInfo.companyOverLimit,
          maxAmount: item.currentProjectInfo.companyMaxAmount,
          loanAmount: item.currentProjectInfo.companyLoanAmount,
        },
      ];
    });


  },
  methods: {
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
  },
};
</script>

<style lang="less" scoped>
.data_content {
  margin: 0 auto;

  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
    width: 140px;
    text-align: right;
  }
}

.left,
.right {
  width: 50%;

  div {
    margin-top: 16px;

    span {
      font-weight: bold;
      display: inline-block;
      width: 190px;
      text-align: right;
    }
  }
}

.item {
  margin-bottom: 14px;

  span {
    font-weight: bold;
    display: inline-block;
    width: 140px;
    text-align: right;
  }
}

.solid {
  padding: 8px 14px;
  border: 1px solid #cccccc;
  background: #f2f2f2;
  font-weight: bold;
  margin: 16px 0;
}
</style>
