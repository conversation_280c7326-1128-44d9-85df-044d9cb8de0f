<template>
  <div>
    <div class="flex justify-end mb-1">
      <el-button type="primary" @click="batchDownloadSupervise">
        批量下载附件</el-button
      >
      <el-button
        @click="handleExportSupervise"
        style="border-color: #ffc26c; background: #fff8e6; color: #ffc30d"
        icon="el-icon-download"
        size="mini"
        >导出列表</el-button
      >
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
      @selection-change="handleSelectionChangeDataList"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column prop="index" label="序号" width="50" />
      <el-table-column prop="informationCode" label="资料编号" width="180">
        <template #default="{ row }">
          <span>{{ row.informationCode || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column prop="informationName" label="资料文件/名称" min-width="350">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.informationSystemCode"
            @click="see(scope.row)"
            type="text"
            >{{ scope.row.informationName }}</el-button
          >
          <span v-else>{{ scope.row.informationName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="informationYear" label="资料年度" width="120">
        <template #default="{ row }">
          <span>{{ row.informationYear || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column prop="catalogueName" label="目录" width="120">
        <template #default="{ row }">
          <span>{{ row.catalogueName || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column
        width="120"
        show-overflow-tooltip
        prop="informationSystemCode"
        label="保管期限"
        v-if="processType != 1"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.saveStartTime"
            >{{ scope.row.saveStartTime }} - {{ scope.row.saveEndTime }}</span
          >
          <span v-else>永久</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="cooperationCompanyName"
        label="合作公司"
        width="250"
        show-overflow-tooltip
        ><template #default="{ row }">
          <span>{{ row.cooperationCompanyName || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column
        prop="cooperationProjectName"
        label="合作项目"
        width="380"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.cooperationProjectName || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column prop="createBy" label="资料创建人" width="180">
        <template #default="{ row }">
          <span>{{ row.createBy || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column
        prop="createTime"
        label="上传时间"
        width="180"
        v-if="processType == 1"
      >
        <template #default="{ row }">
          <span>{{ row.createTime || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column
        v-if="processType != 1"
        show-overflow-tooltip
        prop="downloadProcessTime"
        label="资料下载审核时间"
        width="180px"
      >
        <template #default="{ row }">
          <span>{{ row.downloadProcessTime || "-" }}</span>
        </template></el-table-column
      >
      <el-table-column
        prop="fileName"
        label="附件"
        width="160"
      />
      <el-table-column prop="uploadInfo" label="附件下载" fixed="right">
        <template slot-scope="scope">
          <span
            v-if="
              scope.row.fileName.endsWith('.pdf') ||
              scope.row.fileName.endsWith('.jpg') ||
              scope.row.fileName.endsWith('.png') ||
              scope.row.fileName.endsWith('.gif') ||
              scope.row.fileName.endsWith('.jpeg')
            "
          >
            <el-button type="text" @click="handlePreview(scope.row)"
              >查看</el-button
            >
            <el-button type="text" @click="handleDownload(scope.row)"
              >下载</el-button
            >
            <br />
          </span>
          <span v-else>
            <el-button type="text" @click="handleDownload(scope.row)"
              >下载</el-button
            >
          </span>
        </template>
      </el-table-column>
    </el-table>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      @close="addItemType = false"
    />
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
  </div>
</template>

<script>
import dataUpLoadApprovalSupervise from "@/mixin/processFormView/dataUpLoadApprovalSupervise";
import addItem from "@/views/dataManagementSupervise/inputData/addItem.vue";
import privew from "@/mixin/privew";

export default {
  name: "Table",
  mixins: [dataUpLoadApprovalSupervise, privew],
  props: {
    followData: {
      type: Object,
      required: true,
      default:()=>{}
    },
    tableData: {
      type: Array,
      required: true,
    },
    processType: {
      type: [String, Number],
      default: "",
    },
  },
  components: { addItem },
  data() {
    return {
      editData: null,
      seeType: false,
      addItemType: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {},
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
  },
};
</script>
<style lang="less" scoped>
</style>
