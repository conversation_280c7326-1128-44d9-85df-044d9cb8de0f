<template>
  <div>
    <div class="search">
      <div class="item">
        <span>查询范围</span>
        <el-select
          style="width: 200px"
          v-model="queryParams.queryScope"
        >
          <el-option
            v-for="item in queryScopeList"
            :key="item.id"
            :value="item.id"
            :label="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>流程主题</span>
        <el-input
          v-model="queryParams.theme"
          placeholder="请输入流程主题"
          style="width: 250px"
        ></el-input>
      </div>
      <div class="item">
        <span>所属公司</span>
        <el-select
          filterable=""
          clearable=""
          placeholder="请选择所属公司"
          style="width: 250px"
          v-model="queryParams.companyId"
        >
          <el-option
            v-for="item in companyList"
            :key="item.unitId"
            :value="item.unitId"
            :label="item.unitShortName"
          ></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>流程发起人</span>
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入流程发起人姓名"
          style="width: 250px"
        ></el-input>
      </div>
      <div class="item">
        <span>审核时间</span>
        <el-date-picker
          v-model="time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00','23:59:59']"
        >
        </el-date-picker>
      </div>
      <div class="item">
        <span>金额</span>
        <el-input
          v-model="queryParams.minAmount"
          style="width: 150px"
          placeholder="请输入最小值"
        ></el-input
        ><span style="margin: 0 5px">-</span>
        <el-input
          v-model="queryParams.maxAmount"
          style="width: 150px"
          placeholder="请输入最大值"
        ></el-input>
      </div>
      <div class="item">
        <span>所属流程模板</span>
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入所属流程模板名称"
          style="width: 250px"
        ></el-input>
      </div>
      <div class="item">
        <span>出纳审核</span>
        <el-select
          v-model="queryParams.chunaCheck"
          placeholder="请选择审核状态"
        >
          <el-option label="未审核" value="1"></el-option>
          <el-option label="已审核" value="0"></el-option>
        </el-select>
      </div>
      <div class="item">
        <span>凭证生成</span>
        <el-select
          v-model="queryParams.voucharGenerate"
          placeholder="请选择凭证生成状态"
        >
          <el-option label="已生成" value="0"></el-option>
          <el-option label="未全部生成-异常" value="1"></el-option>
          <el-option label="未生成-异常" value="2"></el-option>
          <el-option label="未生成" value="3"></el-option>
        </el-select>
      </div>
      <el-button type="primary" @click="search">搜 索</el-button>
      <el-button @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="btn">
      <el-button
        @click="handleExport"
        style="border-color: #ffc26c; background: #fff8e6; color: #ffc30d"
        icon="el-icon-download"
        size="mini"
        >导出列表</el-button
      >
      <el-button
        icon="el-icon-refresh"
        style="margin-right: 16px"
        @click="getList"
        >刷新</el-button
      >
    </div>
    <div style="padding: 16px; padding-top: 0">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column label="流程主题" width="520">
          <template slot-scope="scope">
            <el-button type="text" @click="toTheme(scope.row)">{{
              scope.row.theme
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip=""
          prop="companyName"
          label="所属公司"
          width="180"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="userName"
          label="流程发起人"
          width="100"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="checkTime"
          label="审核时间"
          width="220"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="amount"
          label="金额(元)"
          width="100"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="templateName"
          label="所属流程模板"
          width="150"
        />
        <el-table-column
          show-overflow-tooltip=""
          prop="informationYear"
          label="出纳审核"
        >
          <template slot-scope="scope">
            {{ scope.row.chunaCheck == 1 ? "未审核" : "已审核" }}
          </template>
        </el-table-column>
        <el-table-column prop="informationYear" label="凭证生成">
          <template slot-scope="scope">
            <span v-if="scope.row.voucharGenerate == 0">已生成</span>
            <span style="color: red" v-if="scope.row.voucharGenerate == 1"
              >未全部生成-异常</span
            >
            <span style="color: red" v-if="scope.row.voucharGenerate == 2"
              >未生成-异常</span
            >
            <span v-if="scope.row.voucharGenerate == 3">未生成</span>
          </template>
        </el-table-column>
        <el-table-column prop="voucharNum" label="凭证数量" />
        <el-table-column label="操作" align="center" fixed="right" width="200">
          <template slot-scope="scope">
            <el-button type="text" @click="toTheme(scope.row)"
              >查看流程详情</el-button
            >
            <el-button
              type="text"
              @click="toDetail(scope.row)"
              v-if="scope.row.chunaCheck == 0"
              >查看凭证</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="queryParams.total > 0"
        :total="queryParams.total"
        :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import { getUnit } from "@/api/oa/processTemplate";
import { caiwuList } from "@/api/oa/financeProcess";

export default {
  data() {
    return {
      tableData: [],
      time: [],
      queryParams: {
        queryScope:"1",
        theme: "",
        companyId: "",
        userName: "",
        minAmount: "",
        maxAmount: "",
        templateName: "",
        chunaCheck: "",
        voucharGenerate: "",
        beginTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      companyList: [],
      queryScopeList:[
        {
          id:"1",
          value:"我发起的流程"
        },
        {
          id:"2",
          value: "我已审批"
        },
        {
          id:"3",
          value:"抄送给我"
        }
      ]
    };
  },
  mounted() {
    getUnit().then((res) => {
      this.companyList = res.rows;
    });
    this.getList();
  },
  methods: {
    toDetail(v) {
      this.$router.push({
        path: "/oaWork/voucherDetails",
        query: {
          pfdId: v.pfdId,
        },
      });
    },
    handleExport() {
      this.download(
        "/oasystem/caiwuDataquery/caiwuExport",
        {
          ...this.queryParams,
        },
        `财务流程导出.xlsx`
      );
    },
    toTheme(v) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: { oid: v.pfdId, businessId: v.pfdId,financeProcess:'true' },
      });
    },
    search() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getList() {
      if (this.time && this.time.length > 0) {
        this.queryParams.beginTime = this.$format(this.time[0], "yyyy-MM-dd HH:mm:ss");
        this.queryParams.endTime = this.$format(this.time[1], "yyyy-MM-dd HH:mm:ss");
      } else {
        this.queryParams.beginTime = "";
        this.queryParams.endTime = "";
      }
      caiwuList({ ...this.queryParams }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.queryParams.total = res.total;
        }
      });
    },
    reset() {
      this.queryParams = {
        theme: "",
        companyId: "",
        userName: "",
        minAmount: "",
        maxAmount: "",
        templateName: "",
        chunaCheck: "",
        voucharGenerate: "",
        beginTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1,
      };
      this.time = [];
      this.getList();
    },
  },
};
</script>

<style lang="less" scoped>
.btn {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
}
.search {
  padding: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .item {
    margin-right: 16px;
    display: flex;
    align-items: center;
  }
  span {
    margin-right: 9px;
  }
}

.el-button {
  height: 36px;
  margin-left: 16px;
}
.solid {
  width: 100%;
  height: 10px;
  background: #f8f8f9;
}
</style>
