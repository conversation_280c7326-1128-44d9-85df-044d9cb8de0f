<template>
  <div>
    <el-dialog
      append-to-body
      :title="
        editData && editData.id && !seeType
          ? '修改证照'
          : !editData && !seeType
          ? '新增证照'
          : '查看证照'
      "
      :visible.sync="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <el-form ref="form" :model="params" label-width="130px" :rules="rules">
        <div class="flex">
          <div class="item">
            <el-form-item label="证照名称" prop="licenseName">
              <el-input
                type="text"
                :disabled="seeType"
                v-model="params.licenseName"
                placeholder="请输入证照名称"
                style="width: 220px"
                maxlength="100"
              ></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="所属目录" prop="catalogueId">
              <el-select
                v-model="params.catalogueId"
                clearable
                @clear="delml"
                :disabled="seeType"
                style="width: 220px"
                placeholder="请选择所属目录"
                ref="selectUpResId1"
              >
                <el-option
                  hidden
                  :value="params.catalogueId"
                  :label="params.catalogueName"
                >
                </el-option>
                <el-tree
                  :data="deTreeList"
                  :props="defaultProps"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @node-click="handleNodeClick"
                >
                </el-tree>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div class="item">
            <el-form-item label="证照系统编号">
              <el-input
                type="text"
                v-model="params.licenseSystemCode"
                disabled
                placeholder="保存后自动生成"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="证照编号">
              <el-input
                type="text"
                :disabled="seeType"
                v-model="params.licenseCode"
                placeholder="证照编号"
                style="width: 220px"
                maxlength="60"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div class="item">
            <el-form-item label="发证单位">
              <el-select
                v-model="params.issuingUnit"
                placeholder="请选择发证单位"
                clearable
                :disabled="seeType"
                style="width: 220px"
              >
                <el-option
                  v-for="item in issuingUnitList"
                  :key="item.unitId"
                  :label="item.unitName"
                  :value="item.unitName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="发证日期" prop="issuingTime">
              <el-date-picker
                :disabled="seeType"
                style="width: 220px"
                v-model="params.issuingTime"
                type="date"
                value-format="yyyy-MM-dd"
              
              >
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div class="item">
            <el-form-item label="有效期" prop="indateFlagShow">
              <el-checkbox
                v-model="params.indateFlag"
                :disabled="Boolean(seeType)"
                >永久</el-checkbox
              >
              <el-date-picker
                v-model="time"
                :disabled="Boolean(params.indateFlag || seeType)"
                type="daterange"
                style="width: 225px"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="下次年审时间" prop="annualAuditTime">
              <el-date-picker
                :disabled="seeType"
                v-model="params.annualAuditTime"
                type="date"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div class="item">
            <el-form-item label="证照保管人" prop="licenseCustodyMiddleList">
              <el-select
                multiple
                collapse-tags
                class="applySelect"
                type="text"
                v-model="params.licenseCustodyMiddleList"
                placeholder="点击右侧按钮选择"
                style="width: 270px"
                :disabled="seeType"
              >
                <el-option
                  v-for="item in personList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                ></el-option
              ></el-select>
              <i
                @click.stop="PersonnelAuthorityType = true"
                style="
                  position: absolute;
                  right: 12px;
                  top: 12px;
                  cursor: pointer;
                "
                class="el-icon-search"
              ></i>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="持证人员">
              <el-input
                type="text"
                v-model="params.certificatePerson"
                disabled
                placeholder="证照借出后自动填写"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div class="item">
            <el-form-item label="证照状态">
              <el-input
                type="text"
                v-model="dict.label.license_status[params.licenseStatus]"
                disabled
                placeholder="系统自动填写"
                style="width: 220px"
              ></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="附件上传" prop="fileList">
              <el-upload
                style="width: 240px"
                :disabled="seeType"
                :headers="upload.headers"
                :action="upload.url"
                :on-success="handleFileSuccess"
                :on-remove="handleRemove"
                multiple
                :file-list="fileList"
                  :on-preview="openFile"
              >
                <el-button :disabled="seeType"
                  ><i class="el-icon-upload2"></i>上传附件</el-button
                >
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <div class="item" style="display: flex">
            <el-form-item label="备注">
              <el-input
                type="textarea"
                :disabled="seeType"
                :rows="2"
                style="width: 590px"
                placeholder="请输入内容"
                v-model="params.remark"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="flex" v-if="editData && editData.id && !seeType">
          <div class="item" style="display: flex">
            <el-form-item label="本次修订说明" prop="revisionNote">
              <el-input
                type="textarea"
                :rows="2"
                style="width: 590px"
                placeholder="请输入备注"
                v-model="params.revisionNote"
              >
              </el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <el-table v-if="seeType" border :data="tableData" style="width: 100%">
        <el-table-column
          align="center"
          width="60"
          prop="version"
          label="版本号"
        >
        </el-table-column>
        <el-table-column align="center" prop="updateTime" label="修改日期" width="150px">
        </el-table-column>
        <el-table-column align="center" label="生效日期" width="150px">
          <template slot-scope="scope">
            {{ params.indateFlag == 1 ? "永久" : scope.row.annualAuditTime }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="revisionNote" label="修订说明">
        </el-table-column>
        <el-table-column align="center" fixed="right" width="100" label="操作">
          <template slot-scope="scope">
            <el-button @click="seeHistory(scope.row)" type="text" size="small"
              >查看历史详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="seeType && pataion.total > 0"
        :total="pataion.total"
        :page.sync="pataion.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :limit.sync="pataion.pageSize"
        @pagination="getHistoryLicense"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>

        <el-button type="primary" v-if="!seeType" @click="submit"
          >提 交</el-button
        >
      </span>
      <PersonnelAuthority
        :auUserIds="params.licenseCustodyMiddleList"
        @confrimPer="confrimPer"
        v-if="PersonnelAuthorityType"
        @close="PersonnelAuthorityType = fasle"
      />
      <AddItemHistory
        :editData="editDataHistory"
        :catalogueName="catalogueName"
        :catalogueId="catalogueId"
        v-if="addItemTypeDetail"
        :deTreeList="deTreeList"
        @close="addItemTypeDetail = false"
      />
      <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
    </el-dialog>
  </div>
</template>

<script>
import {
  getHistoryLicense,
  getUnit,
  licenseMainLicenseeDetail,
} from "@/api/certificate/allLicenses";
import { licenseCatalogueCatalogueDetail } from "@/api/certificate/directory";
import PersonnelAuthority from "./PersonnelAuthority.vue";
import AddItemHistory from "./addItemHistory.vue";
import privew from "@/mixin/privew";
import config from "./config";

export default {
  
    mixins: [ privew],
  props: {
    deTreeList: Array,
    editData: Object,
    catalogueName: String,
    catalogueId: [Number, String],
    seeType: Boolean,
  },
  components: {
    PersonnelAuthority,
    AddItemHistory,
  },
  dicts: ["license_status"],
  data() {
    return {
      ...config,
      tableData: [],
      issuingUnitList: [],
      time: [],
      dialogVisible: true,
      licenseCustodyMiddleListShow:[],
      params: {
        catalogueId: "",
        catalogueName: "",
        remark: "",
        revisionNote: "",
        licenseName: "",
        issuingTime: "",
        licenseCustodyMiddleList: [],
        
        licenseCode: "",
        indateFlagShow: "",
        indateFlag: "",
        indateStartTime: "",
        indateEndTime: "",
        issuingUnit: "",
        licenseStatus: "",
        certificatePerson: "",
      },
      defaultProps: {
        children: "fPiattaformas",
        label: "catalogueName",
      },
      PersonnelAuthorityType: false,
      fileList: [],
      fileListIds: [],
      personList: [],

      pataion: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      addItemTypeDetail: false,
      editDataHistory: {},
    };
  },
  watch: {
    "params.indateFlag"(val) {
      if (val) {
        this.$set(this.params, "indateFlagShow", true);
      } else {
        this.$set(this.params, "indateFlagShow", "");
      }
    },
    time(val) {
      if (val) {
        this.$set(this.params, "indateFlagShow", true);
      } else {
        this.$set(this.params, "indateFlagShow", "");
      }
    },

    "params.licenseFilesList"(val) {
      if (this.fileList.length) return;
      if (val && val.length) {
        this.fileList = val.map((item) => {
          return {
            name: item.fileName,
            id: item.id,
            url: process.env.VUE_APP_BASE_API + item.filePath,
            downLoadUrl: item.filePath,
            fileId: item.fileId,
          };
        });
        this.getFileListIds(this.fileList);
        this.$set(this.params, "fileList", true);
      }
    },
  },
  mounted() {
    this.opens();
    this.getIssuingUnitList();

    if (this.catalogueId) {
      this.params.catalogueId = this.catalogueId;
      this.params.catalogueName = this.catalogueName;
    }
    if (this.editData?.id || this.editData?.informationId) {
      this.params = this.editData;
      if (this.params.indateStartTime) {
        this.time = [this.params.indateStartTime, this.params.indateEndTime];
      }
    }
    if (this.seeType) {
      this.getHistoryLicense();
    }
    this.params.indateFlag = this.params.indateFlag == 1 ? true : false;
    this.getUserAuthorizationList();
  },
  methods: {
    getHistoryLicense() {
      let params = {
        licenseSystemCode: this.editData.licenseSystemCode,
        pageSize: this.pataion.pageSize,
        pageNum: this.pataion.pageNum,
      };
      getHistoryLicense({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.pataion.total = res.total;
        }
      });
    },
    opens() {
      if (this.editData && this.editData.id && !this.seeType) {
        this.$set(this.rules, "revisionNote", [
          { required: true, message: "请输入修订说明", trigger: "blur" },
        ]);
        if (this.editData.licenseCustodyMiddleList) {
          console.log(this.editData.licenseCustodyMiddleList,1111111)
          const temp=[...this.editData.licenseCustodyMiddleList]
          this.licenseCustodyMiddleListShow =
            temp.map((itme) => {
              return {
                userId: itme.userId,
                userName: itme.userName,
              };
            });
            
        }
      } else {
        this.$delete(this.rules, "revisionNote");
      }
    },
    getUserAuthorizationList() {
      this.personList = this.editData.licenseCustodyMiddleList
        ? [...this.editData.licenseCustodyMiddleList]
        : [];
      if (this.personList.length) {
        this.params.licenseCustodyMiddleList = this.personList.map(
          (item) => item.userId
        );
      }
    },
    confrimPer(e) {
      console.log(e, 111111);
      this.PersonnelAuthorityType = false;
      this.personList = e;
      this.params.licenseCustodyMiddleList = [];
      this.licenseCustodyMiddleListShow = e.map((itme) => {
        return {
          userId: itme.userId,
          userName: itme.userName,
        };
      });
      this.personList.forEach((item) => {
        this.params.licenseCustodyMiddleList.push(item.userId);
      });
    },
    async getIssuingUnitList() {
      const { rows } = await getUnit();
      this.issuingUnitList = rows;
    },
    changeCatalogueId() {
      if (this.params.catalogueId) {
        licenseCatalogueCatalogueDetail(this.params.catalogueId).then((res) => {
          if (res.code == 200) {
            this.params.issuingUnit = res.data.unitName;
          }
        });
      }
    },
    delml() {
      this.params.catalogueId = "";
      this.params.catalogueName = "";
    },

    submit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let ZzLicenseMainVo = {};
          if (this.params.id)
            ZzLicenseMainVo = {
              oldLicenseId: this.params.id,
              revisionNote: this.params.revisionNote,
            };
          const params = {
            ...this.params,
            licenseFilesList: this.fileListIds,
            indateFlag: this.params.indateFlag ? 1 : 0,
            licenseCustodyMiddleList: this.licenseCustodyMiddleListShow,
            indateStartTime: this.time[0],
            indateEndTime: this.time[1],
            ...ZzLicenseMainVo,
          };
          this.$emit("save", params);
        }
      });
    },
    changeFlag(e) {
      console.log(e);
      if (e) {
        this.time = [];
        this.params.indateStartTime = "";
        this.params.indateEndTime = "";
      }
    },
    seeHistory(row) {
      licenseMainLicenseeDetail(row.id).then((res) => {
        if (res.code == 200) {
          this.editDataHistory = res.data;
          console.log(this.editDataHistory, 222222222);
          this.addItemTypeDetail = true;
        }
      });
    },
    handleNodeClick(data) {
      this.params.catalogueId = data.id;
      this.params.catalogueName = data.catalogueName;
      this.$refs.selectUpResId1.blur();
      this.changeCatalogueId();
    },
    handleClose() {
      this.$emit("close");
      this.fileList = [];
      this.fileListIds = [];
    },
      openFile(value) {
      this.handlePreview(value);
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.getFileListIds(fileList);
    },
    handleRemove(file, fileList) {
      this.getFileListIds(fileList);
    },
    getFileListIds(value) {
      console.log(value);
      this.fileListIds = value.map((item) => {
        return {
          fileName: item.name,
          filePath:
            item.filePath || item.response.data.filePath,
            fileId:item.fileId||item.response.data.fileId
        };
      });
      this.$set(this.params, "fileList", this.fileListIds.length ? true : "");
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  span {
    margin-right: 9px;
    display: inline-block;
    width: 100px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
/deep/ .el-checkbox .el-checkbox__label {
  padding: 0px 2px !important;
}
/deep/ .applySelect .el-icon-arrow-up:before {
  content: "";
}
</style>