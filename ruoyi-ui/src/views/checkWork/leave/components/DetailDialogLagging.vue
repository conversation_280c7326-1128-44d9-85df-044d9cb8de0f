<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="补充滞后申请原因"
      :visible.sync="innerValue"
      width="1150px"
      @open="handleOpen"
    >
      <div>
        <div class="text-xl mb-4 text-opacity-5"> 您提交的请假申请，申请时间晚于请假时间，请说明原因</div> 
        <el-form ref="form" :model="form" label-width="130px" :rules="rulesLagging">
          <el-form-item
            label="滞后申请原因"
            prop="lagReason"
            style="margin-bottom: 20px"
          >
            <el-input
              type="textarea"
              :autosize="{ minRows: 6, maxRows: 8 }"
              placeholder="请输入滞后申请原因"
              v-model="form.lagReason"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button type="primary" @click="onSubmit">确定并提交</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";

export default {
  mixins: [vModelMixin],

  data() {
    return {
      ...config,
      form: {
        lagReason:"",
      },
    };
  },
  mounted() {},
  methods: {
    handleOpen() {
      this.init();
    },
    async init() {
      this.form.lagReason="";
    },

    onSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$emit("on-submit", this.form);
        }
      });
    },
  },
};
</script>

