<template>
  <div>
    <el-dialog
      :title="
        itemDetail && !seeType
          ? '修改流程提醒配置'
          : itemDetail && seeType
          ? '流程提醒配置详情'
          : '新增流程提醒配置'
      "
      :visible.sync="dialogVisible"
      width="750px"
      :before-close="close"
    >
      <div class="flex">
        <div class="item">
          <span><i>*</i>OA流程</span>
          <el-select
            :disabled="seeType || itemDetail"
            @change="changeFlow"
            v-model="params.flowId"
            placeholder="请选择OA流程"
            style="width: 220px"
            filterable=""
          >
            <el-option
              v-for="item in flowList"
              :key="item.flowId"
              :value="item.flowId"
              :label="item.flowName"
            ></el-option>
          </el-select>
        </div>
        <div class="item" style="margin-left: 40px">
          <span><i>*</i>流程节点</span>
          <el-select
            :disabled="seeType || itemDetail"
            v-model="params.nodeId"
            placeholder="请选择流程节点"
            style="width: 220px"
            filterable=""
          >
            <el-option
              v-for="item in nodeList"
              :key="item.nodeId"
              :label="item.nodeName"
              :value="item.nodeId"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex">
        <div class="item" style="position: relative">
          <span>提醒部门</span>
          <el-select
            multiple
            :disabled="seeType"
            class="applySelect"
            type="text"
            collapse-tags
            v-model="params.deptAuths"
            placeholder="点击右侧按钮选择"
            style="width: 220px"
          >
            <el-option
              v-for="item in departList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            ></el-option>
          </el-select>
          <i
            @click.stop="DepartmentAuthorityType = true"
            style="position: absolute; right: 12px; top: 12px; cursor: pointer"
            class="el-icon-search"
          ></i>
        </div>
        <div class="item" style="position: relative; margin-left: 40px">
          <span>提醒岗位</span>
          <el-select
            multiple
            :disabled="seeType"
            collapse-tags
            type="text"
            class="applySelect"
            v-model="params.postAuths"
            placeholder="点击右侧按钮选择"
            style="width: 220px"
          >
            <el-option
              v-for="item in postList"
              :key="item.postId"
              :label="item.postName"
              :value="item.postId"
            ></el-option
          ></el-select>
          <i
            @click.stop="PostAuthorityType = true"
            style="position: absolute; right: 12px; top: 12px; cursor: pointer"
            class="el-icon-search"
          ></i>
        </div>
      </div>
      <div class="flex">
        <div class="item" style="position: relative">
          <span>提醒人员</span>
          <el-select
            multiple
            :disabled="seeType"
            collapse-tags
            class="applySelect"
            type="text"
            v-model="params.userAuths"
            placeholder="点击右侧按钮选择"
            style="width: 220px"
          >
            <el-option
              v-for="item in personList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            ></el-option
          ></el-select>
          <i
            @click.stop="PersonnelAuthorityType = true"
            style="position: absolute; right: 12px; top: 12px; cursor: pointer"
            class="el-icon-search"
          ></i>
        </div>
        <div class="item" style="margin-left: 40px">
          <span>状态</span>
          <el-radio :disabled="seeType" v-model="params.state" label="0"
            >正常</el-radio
          >
          <el-radio :disabled="seeType" v-model="params.state" label="1"
            >停用</el-radio
          >
        </div>
      </div>
      <div class="solid"></div>
      <div class="word">
        <span class="span1">系统说明：</span><br />
        <span class="span1"
          ><span class="span2">提醒标题</span>和<span class="span2"
            >提醒内容</span
          >可进行公式编辑</span
        ><br />
        <span class="span1">流程名称：$flowName$</span><br />
        <span class="span1">发起人：$createUser$</span><br />
        <span class="span1">发起时间：$createTime$</span><br />
        <span class="span1">配置节点审核人：$auditUser$</span><br />
        <span class="span1">配置节点名称：$nodeId$</span><br />
        <span class="span1">配置节点审核通过时间：$auditTime$</span><br />
        <span class="span1">配置内容示例：</span><br />
        <span class="span1">
          <span class="span3">$createUser$</span>在<span class="span3"
            >$createTime$</span
          >申请了<span class="span3">$flowName$</span>,<span class="span3"
            >$nodeId$</span
          >节点的审核人<span class="span3">$auditUser$</span>在<span
            class="span3"
            >$auditTime$</span
          >审批通过 <br> 展示效果：<br />
          <span class="span3">张三</span>在<span class="span3"
            >2023-10-11 17:28:04</span
          >申请了<span class="span3">资料录入，部门负责人审批</span
          >节点的审核人<span class="span3">李四</span>在
          <span class="span3">2023-10-12 9:28:04</span>审批通过
        </span>
      </div>
      <div class="flex">
        <div class="item">
          <span><i>*</i>提醒标题</span>
          <el-input
            :disabled="seeType"
            v-model="params.remindTitle"
            placeholder="请输入提醒标题"
            style="width: 565px"
          ></el-input>
        </div>
      </div>
      <div class="flex">
        <div class="item" style="display: flex">
          <span style="flex-shrink: 0"><i>*</i>提醒内容</span>
          <el-input
            :disabled="seeType"
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="params.remindText"
            style="width: 565px"
          >
          </el-input>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button v-if="!seeType" type="primary" @click="submit"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <DepartmentAuthority
      :auDeptIds="params.deptAuths"
      @confimrDe="confimrDe"
      v-if="DepartmentAuthorityType"
      @close="DepartmentAuthorityType = false"
    />
    <PostAuthority
      :auPostIds="params.postAuths"
      @confirmPost="confirmPost"
      v-if="PostAuthorityType"
      @close="PostAuthorityType = false"
    />
    <PersonnelAuthority
      :auUserIds="params.userAuths"
      @confrimPer="confrimPer"
      v-if="PersonnelAuthorityType"
      @close="PersonnelAuthorityType = fasle"
    />
  </div>
</template>

<script>
import {
  getNodeToFlow,
  OASettingUp,
  editOASettingUp,
} from "@/api/oa/processConfig";
import {
  treeselect,
  getUserAuthorizationList,
  getPostAuthorizationList,
} from "@/api/directoryMation/directoryMation";
import DepartmentAuthority from "../../dataManagement/directoryMation/DepartmentAuthority.vue";
import PersonnelAuthority from "../../dataManagement/directoryMation/PersonnelAuthority.vue";
import PostAuthority from "../../dataManagement/directoryMation/PostAuthority.vue";
export default {
  props: {
    flowList: Array,
    itemDetail: Object,
    seeType: Boolean,
  },
  components: {
    DepartmentAuthority,
    PersonnelAuthority,
    PostAuthority,
  },
  data() {
    return {
      departList: [],
      nodeList: [],
      postList: [],
      personList: [],
      params: {
        flowId: "",
        nodeId: "",
        flowFullId: "",
        state: "0",
        remindTitle: "",
        remindText: "",
        deptAuths: [],
        userAuths: [],
        postAuths: [],
      },
      dialogVisible: true,
      PersonnelAuthorityType: false,
      PostAuthorityType: false,
      DepartmentAuthorityType: false,
    };
  },
  mounted() {
    if (this.itemDetail) {
      this.params = Object.assign(this.params, this.itemDetail);
      getNodeToFlow({ flowFullId: this.params.flowFullId }).then((res) => {
        this.nodeList = res.data;
      });
    }
    treeselect().then((res) => {
      if (res.code == 200) {
        if (this.params.deptAuths && this.params.deptAuths.length > 0) {
          this.departList = this.dg(res.data, this.params.deptAuths, []);
        }
      }
    });
    getUserAuthorizationList().then((res) => {
      if (res.code == 200) {
        if (this.params.userAuths && this.params.userAuths.length > 0) {
          res.data.forEach((item) => {
            this.params.userAuths.forEach((i) => {
              if (item.userId == i) {
                this.personList.push(item);
              }
            });
          });
        }
      }
    });
    getPostAuthorizationList().then((res) => {
      if (res.code == 200) {
        if (this.params.postAuths && this.params.postAuths.length > 0) {
          res.rows.forEach((item) => {
            this.params.postAuths.forEach((i) => {
              if (item.postId == i) {
                this.postList.push(item);
              }
            });
          });
        }
      }
    });
  },
  methods: {
    changeFlow(e) {
      let data = this.flowList.find((item) => {
        return item.flowId == e;
      });
      console.log(data);
      this.params.flowFullId = data.flowFullId;
      getNodeToFlow({ flowFullId: data.flowFullId }).then((res) => {
        this.params.nodeId = "";
        this.nodeList = res.data;
      });
    },
    close() {
      this.$emit("close");
    },
    dg(list, list2, arr) {
      list.forEach((item) => {
        if (item.children && item.children.length > 0) {
          this.dg(item.children, list2, arr);
        }
        list2.forEach((i) => {
          if (item.id == i) {
            arr.push(item);
          }
        });
      });
      return arr;
    },
    submit() {
      if (!this.params.flowId) {
        this.$methods.warning("请选择OA流程");
        return;
      }
      if (!this.params.nodeId) {
        this.$methods.warning("请选择流程节点");
        return;
      }
      if (!this.params.remindTitle) {
        this.$methods.warning("请输入提醒标题");
        return;
      }
      if (!this.params.remindText) {
        this.$methods.warning("请输入提醒内容");
        return;
      }
      if (this.itemDetail) {
        editOASettingUp(this.params).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$emit("submit");
          }
        });
      } else {
        OASettingUp(this.params).then((res) => {
          if (res.code == 200) {
            this.$message.success("操作成功");
            this.$emit("submit");
          }
        });
      }
    },
    confirmPost(e) {
      this.PostAuthorityType = false;
      this.postList = e;
      this.params.postAuths = [];
      this.postList.forEach((item) => {
        this.params.postAuths.push(item.postId);
      });
    },
    confrimPer(e) {
      this.PersonnelAuthorityType = false;
      this.personList = e;
      this.params.userAuths = [];
      this.personList.forEach((item) => {
        this.params.userAuths.push(item.userId);
      });
    },
    confimrDe(e) {
      console.log(e);
      this.DepartmentAuthorityType = false;
      this.departList = e;
      this.params.deptAuths = [];
      this.departList.forEach((item) => {
        this.params.deptAuths.push(item.id);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;

  margin-bottom: 12px;
  span {
    margin-right: 9px;
    display: inline-block;
    width: 67px;
    text-align: right;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
/deep/ .el-select .el-input__inner {
  height: 36px !important;
}
/deep/ .applySelect .el-icon-arrow-up:before {
  content: "";
}
.solid {
  width: 100%;
  height: 1px;
  background: #ccc;
  margin: 20px 0;
}
.word {
  .span1 {
    color: #999;
  }
  .span2 {
    color: #d9001b;
  }
  .span3 {
    color: #027db4;
  }
}
</style>