<template>
  <div>
    <el-dialog
      :title="'已选择' + tableData.length + '条'"
      :visible.sync="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <el-table
        :data="tableData"
        style="width: 100%; margin-top: 16px; margin-left: 4px"
      >
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="archivistCode"
          label="档案编号"
          width="180"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="archivstName"
          label="档案名称"
          width="180"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="archivistBy"
          label="归档人"
          width="120"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="archivistTime"
          label="归档时间"
          width="220"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="cw"
          label="所属项目"
          width="140"
        >
        </el-table-column>
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="pertainCompanyName"
          label="所属公司"
          width="140"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="pertainDeptName"
          label="所属部门"
          width="120"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="catalogueName"
          label="所属目录"
          width="120"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="pertainLib"
          label="所属卷库"
          width="120"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="flowInitiator"
          label="流程发起人"
          width="100"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="initiateTime"
          label="流程发起时间"
          width="220"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button @click="del(scope.row)" type="text" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    multipleSelection: Array,
  },
  data() {
    return {
      dialogVisible: true,
      tableData: [],
    };
  },
  mounted() {
    this.tableData = JSON.parse(JSON.stringify(this.multipleSelection));
    this.tableData.map((item) => {
      item.cw = "";
      if (item.cwProjectList && item.cwProjectList.length > 0) {
        item.cwProjectList.forEach((i) => {
          item.cw += i.projectName + "，";
        });
      }
    });
  },
  methods: {
    submit() {
      this.$emit("confirm", this.tableData);
    },
    del(v) {
      this.tableData.map((item, index) => {
        if (item.id == v.id) {
          this.tableData.splice(index, 1);
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>