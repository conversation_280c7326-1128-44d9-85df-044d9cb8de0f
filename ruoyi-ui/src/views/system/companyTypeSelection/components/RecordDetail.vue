<template>
  <div>
    <el-dialog
      title="编辑记录"
      :visible.sync="dialogVisible"
      width="700px"
      :before-close="handleClose"
      ><div style="max-height: 80vh">
        <div class="data_content">
          <div class="table">
            <div class="left">
              <div style="background: #e4e4e4; height: 30px"></div>
              <div>下拉选择名称</div>
              <div>编码</div>
              <div :style="{ height: div3 + 'px' }">关联公司类型</div>
              <div :style="{ height: div4 + 'px' }">备注说明</div>
            </div>
            <div class="center">
              <div style="background: #e4e4e4;height: 30px;">修改前</div>
              <div style="height: 30px">
                {{ oldData && oldData.selectName }}
              </div>
              <div style="height: 30px">
                {{ oldData && oldData.selectCode }}
              </div>
              <div :style="{ height: div3 + 'px' }" ref="oldDiv3">
                {{
                  oldData
                    ? oldData.comtypes.map((item) => item.dictLabel).join("，")
                    : ""
                }}
              </div>
              <div :style="{ height: div4 + 'px' }" ref="oldDiv4">
                {{ oldData && oldData.remark }}
              </div>
            </div>
            <div class="right">
              <div style="background: #e4e4e4;height: 30px;">修改后</div>
              <div style="height: 30px">{{ newData.selectName }}</div>
              <div style="height: 30px">{{ newData.selectCode }}</div>
              <div :style="{ height: div3 + 'px' }" ref="newDiv3">
                {{ newData.comtypeNames.join("，") }}
              </div>
              <div :style="{ height: div4 + 'px' }" ref="newDiv4">
                {{ newData.remark }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
export default {
  props: {
    data: Object,
  },
  data() {
    return {
      dialogVisible: true,
      oldData: null,
      newData: null,
      div3: null,
      div4: null,
    };
  },
  mounted() {
    this.oldData = JSON.parse(this.data.oaApplyRecordsOldData);

    this.newData = JSON.parse(this.data.oaApplyRecordsNewData);
    console.log(this.oldData);
    console.log(this.newData);
    this.$nextTick(() => {
      console.log(this.$refs.newDiv3.offsetHeight);
      if (this.$refs.newDiv3.offsetHeight > this.$refs.oldDiv3.offsetHeight) {
        this.div3 = this.$refs.newDiv3.offsetHeight;
      } else {
        this.div3 = this.$refs.oldDiv3.offsetHeight;
      }
      if (this.$refs.newDiv4.offsetHeight > this.$refs.oldDiv4.offsetHeight) {
        this.div4 = this.$refs.newDiv4.offsetHeight;
      } else {
        this.div4 = this.$refs.oldDiv4.offsetHeight;
      }
      console.log(this.div3, this.div4);
    });
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
  
  <style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
  }
  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    .left,
    .center,
    .right {
      width: 35%;
      > div {
        width: 100%;

        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
      }
    }
    .left div {
      font-weight: bold;
    }
    .left {
      width: 30%;
    }
  }
}
</style>