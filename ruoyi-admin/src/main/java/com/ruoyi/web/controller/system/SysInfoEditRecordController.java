package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.system.service.ISysInfoEditRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysInfoEditRecord;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * oa编辑记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/system/edit/record")
public class SysInfoEditRecordController extends BaseController
{
    @Autowired
    private ISysInfoEditRecordService sysInfoEditRecordService;

    /**
     * 查询oa编辑记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysInfoEditRecord sysInfoEditRecord)
    {
        startPage();
        List<SysInfoEditRecord> list = sysInfoEditRecordService.selectSysInfoEditRecordList(sysInfoEditRecord);
        return getDataTable(list);
    }

    /**
     * 导出oa编辑记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "oa编辑记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysInfoEditRecord sysInfoEditRecord)
    {
        List<SysInfoEditRecord> list = sysInfoEditRecordService.selectSysInfoEditRecordList(sysInfoEditRecord);
        ExcelUtil<SysInfoEditRecord> util = new ExcelUtil<SysInfoEditRecord>(SysInfoEditRecord.class);
        util.exportExcel(response, list, "oa编辑记录数据");
    }

    /**
     * 获取oa编辑记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sysInfoEditRecordService.selectSysInfoEditRecordById(id));
    }

    @GetMapping(value = "/getEditRecord/{logQueryId}")
    public AjaxResult getEditRecord(@PathVariable("logQueryId") Long logQueryId)
    {
        return AjaxResult.success(sysInfoEditRecordService.getEditRecord(logQueryId));
    }

    /**
     * 新增oa编辑记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "oa编辑记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysInfoEditRecord sysInfoEditRecord)
    {
        return toAjax(sysInfoEditRecordService.insertSysInfoEditRecord(sysInfoEditRecord));
    }

    /**
     * 修改oa编辑记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "oa编辑记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysInfoEditRecord sysInfoEditRecord)
    {
        return toAjax(sysInfoEditRecordService.updateSysInfoEditRecord(sysInfoEditRecord));
    }

    /**
     * 删除oa编辑记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "oa编辑记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysInfoEditRecordService.deleteSysInfoEditRecordByIds(ids));
    }
}
